{"name": "sw-ubo-hub-casino", "version": "5.2.96", "private": true, "scripts": {"api": "json-server --watch mock-data/db.json --routes mock-data/routes.json --port 3004 --delay 2", "ng": "node --max-old-space-size=8192 --max-http-header-size=40960 ./node_modules/@angular/cli/bin/ng", "build:dev": "npm run ng build", "build:prod": "npm run ng build -- --configuration production --source-map", "build:docker": "npm run build:prod && docker build -t angular2-webpack-start:latest .", "config": "scripty", "clean": "npm cache clean && rimraf node_modules doc coverage dist compiled dll", "clean:dirs": "npm run clean:dist", "clean:dist": "<PERSON><PERSON><PERSON> dist", "clean:install": "npm set progress=false && npm install", "lint": "node ./node_modules/@angular/cli/bin/ng lint", "prebuild:dev": "npm run clean:dist && npm run config", "prebuild:prod": "npm run clean:dist && npm run config", "prestart": "npm run config", "prestart:prod": "npm run config", "preclean:install": "npm run clean", "start": "env-cmd npm run ng serve", "start:prod": "env-cmd npm run ng serve -- --configuration production", "test": "npm run ng test", "test:lint": "npm run lint", "ci:test": "npm run config && npm run ng test -- --no-watch --no-progress --code-coverage", "postinstall": "ngcc"}, "dependencies": {"@angular/animations": "12.2.16", "@angular/cdk": "12.2.13", "@angular/common": "12.2.16", "@angular/compiler": "12.2.16", "@angular/core": "12.2.16", "@angular/flex-layout": "12.0.0-beta.35", "@angular/forms": "12.2.16", "@angular/material": "12.2.16", "@angular/platform-browser": "12.2.16", "@angular/platform-browser-dynamic": "12.2.16", "@angular/router": "12.2.16", "@auth0/angular-jwt": "5.0.2", "@babel/traverse": "7.22.0", "@ngx-formly/core": "5.6.2", "@ngx-formly/material": "5.6.2", "@ngx-formly/schematics": "5.6.2", "@ngx-translate/core": "13.0.0", "@ngx-translate/http-loader": "6.0.0", "@skywind-group/lib-swui": "^0.1.649", "angular2-text-mask": "9.0.0", "animate.css": "4.1.1", "clipboard": "2.0.1", "color-rgba": "2.2.3", "core-js": "3.1.4", "css-minify": "1.0.1", "d3": "6.2.0", "dexie": "3.0.3", "dom-autoscroller": "2.3.4", "dragula": "3.7.2", "file-saver": "2.0.5", "find-root": "1.0.0", "highcharts": "8.2.0", "ie-shim": "0.1.0", "ionicons": "5.2.3", "is-cidr": "4.0.2", "jquery": "3.2.1", "js-base64": "3.5.2", "lodash": "4.17.10", "lodash.merge": "4.6.1", "moment": "2.29.1", "moment-timezone": "0.5.32", "ng-click-outside": "9.0.0", "ng-table-virtual-scroll": "1.3.5", "ng2-dragula": "2.1.1", "ngx-bootstrap": "5.6.1", "ngx-clipboard": "14.0.1", "ngx-color-picker": "10.1.0", "ngx-markdown": "12.0.1", "ngx-toastr": "13.1.0", "normalize.css": "8.0.1", "parse5": "6.0.1", "pre-commit": "1.2.2", "reflect-metadata": "0.1.10", "roboto-npm-webfont": "1.0.1", "rxjs": "6.6.3", "rxjs-compat": "6.5.2", "select2": "4.0.3", "slugify": "1.6.0", "source-map": "0.7.3", "tslib": "2.0.2", "ua-parser-js": "0.7.12", "validator": "13.1.17", "xlsx": "0.16.9", "zone.js": "0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "12.2.16", "@angular/cli": "12.2.16", "@angular/compiler-cli": "12.2.16", "@angular/language-service": "12.2.16", "@schematics/angular": "10.1.5", "@types/clipboard": "2.0.0", "@types/fs-extra": "9.0.1", "@types/jasmine": "3.5.14", "@types/jasminewd2": "2.0.6", "@types/jquery": "3.2.1", "@types/lodash": "4.14.119", "@types/node": "14.11.5", "@types/uglify-js": "3.11.0", "codelyzer": "6.0.1", "dotenv": "8.2.0", "env-cmd": "10.1.0", "fs-extra": "9.0.1", "jasmine-core": "3.5.0", "jasmine-spec-reporter": "6.0.0", "json-server": "0.16.2", "karma": "6.3.4", "karma-chrome-launcher": "3.1.0", "karma-coverage-istanbul-reporter": "3.0.3", "karma-jasmine": "4.0.1", "karma-jasmine-html-reporter": "1.5.4", "less": "3.9.0", "rimraf": "3.0.2", "rxjs-tslint": "0.1.6", "scripty": "2.0.0", "stylus": "0.54.5", "ts-node": "9.0.0", "tslint": "6.1.3", "typescript": "4.3.5"}, "pre-commit": ["test:lint"], "engines": {"node": ">= 10.1.0", "npm": ">= 5.0"}}