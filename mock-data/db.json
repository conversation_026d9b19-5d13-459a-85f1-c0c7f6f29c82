{"users": [{"status": "normal", "username": "ENT1-User", "email": "<EMAIL>", "entity": "ENT1", "grantedPermissions": [], "revokedPermissions": []}, {"status": "normal", "username": "MASTER", "email": "<EMAIL>", "entity": "MASTER", "grantedPermissions": [], "revokedPermissions": []}, {"status": "normal", "username": "SUPERADMIN", "email": "SUPERADMIN@SUPERADMIN", "entity": "MASTER", "grantedPermissions": ["entity", "entity:create", "entity:change-state", "entity:view", "entity:delete", "country", "country:add", "country:remove", "currency", "currency:add", "currency:remove", "user", "user:view", "user:create", "user:change-state", "user:change-password", "permissions", "permissions:view", "permissions:grant", "permissions:revoke", "finance", "finance:debit", "finance:credit", "player", "player:create", "player:view", "player:deposit", "player:withdrawal", "player:change-state", "keyentity:user", "keyentity:user:view", "keyentity:user:create", "keyentity:user:change-state", "keyentity:user:change-password", "keyentity:permissions", "keyentity:permissions:view", "keyentity:permissions:grant", "keyentity:permissions:revoke", "keyentity:player", "keyentity:player:create", "keyentity:player:view", "keyentity:player:deposit", "keyentity:player:withdrawal", "keyentity:player:change-state", "keyentity:gamegroup", "keyentity:gamegroup:view", "keyentity:gamegroup:edit", "keyentity:game", "keyentity:game:view", "keyentity:game:url", "keyentity:game:change-state", "keyentity:game<PERSON><PERSON><PERSON>", "keyentity:gameprovider:create", "keyentity:gameprovider:change-state", "keyentity:gameprovider:change-secret"], "revokedPermissions": []}, {"status": "normal", "username": "TLE1-User", "email": "<EMAIL>", "entity": "TLE1", "grantedPermissions": [], "revokedPermissions": []}], "players": [{"id": "PLAYER001", "code": "PLAYER001", "status": "normal", "firstName": "<PERSON>", "lastName": "Dow", "email": "<EMAIL>", "country": "US", "currency": "USD", "language": "US", "gameGroup": "VIP"}, {"id": "PLAYER002", "code": "PLAYER002", "status": "suspended", "firstName": "<PERSON>", "lastName": "Dow", "email": "<EMAIL>", "country": "US", "currency": "USD", "language": "US", "gameGroup": "VIP"}], "messages": [{"id": "1", "title": "Why do we use it?", "icon": "icon-newspaper", "text": "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).", "displayDate": 1478003074494, "creationDate": 1478003074494, "publicationDate": 1478003074494}, {"id": "2", "title": "Where does it come from?", "icon": "icon-newspaper", "text": "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).", "displayDate": 1478003074494, "creationDate": 1478003074494, "publicationDate": 1478003074494}, {"id": "3", "title": "Where can I get some?", "icon": "icon-newspaper", "text": "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).", "displayDate": 1478003074494, "creationDate": 1478003074494, "publicationDate": 1478003074494}, {"id": "4", "title": "What is <PERSON><PERSON>?", "icon": "icon-newspaper", "text": "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).", "displayDate": 1478003074494, "creationDate": 1478003074494, "publicationDate": 1478003074494}], "game_history": [{"id": "1", "gameCode": "string", "currencyCode": "USD", "timestamp": 1478003074494, "brandId": 123, "playerCode": "string", "deviceId": "string", "spinSerialNumber": 123, "gameResult": "string", "requestedPositions": "string", "betAmount": 10, "winAmount": -10, "revenue": 0, "finished": true}, {"roundId": 100000063, "brandId": 1000074, "playerCode": "PLAYER", "gameCode": "sw_mrmnky", "currency": "USD", "bet": 50, "win": 0, "revenue": 50, "timestamp": 2487110400, "roundEnded": true}, {"roundId": 100000064, "brandId": 1000074, "playerCode": "PLAYER", "gameCode": "sw_mrmnky", "currency": "USD", "bet": 50, "win": 0, "revenue": 50, "timestamp": 2487110400, "roundEnded": true}, {"id": "4", "gameCode": "string", "currencyCode": "EU", "timestamp": 1478003074494, "brandId": 123, "playerCode": "string", "deviceId": "string", "spinSerialNumber": 123, "betAmount": 0.01, "gameResult": "string", "requestedPositions": "string"}], "currency_report": {"reports": [{"currency": "EUR", "playedGames": 102, "totalBet": 4900, "totalWin": 40942.4, "totalGGR": -36042.4}, {"currency": "USD", "playedGames": 190, "totalBet": 9450, "totalWin": 22578, "totalGGR": -13128}], "total": {"currency": "USD", "playedGames": 292, "totalBet": 14693, "totalWin": 66386.368, "totalGGR": -51693.368}}, "gameInfo": {"request": "spin", "stake": {"lines": 30, "bet": 5, "coin": 1}, "totalBet": 0, "totalWin": 0, "state": {"currentScene": "freeSpins", "multiplier": 1, "collapsingWin": 215, "freeSpinsCount": 3, "freeSpinsWin": 0, "collapsingCount": 0, "initialFreeSpinWin": 215}, "reels": {"set": "main", "positions": [20, 29, 12, 33, 0], "view": [[8, 10, 1, 8, 3], [1, 8, 5, 5, 8], [6, 3, 3, 10, 1]]}, "rewards": [], "events": [{"id": "freeSpinsStart", "amount": 3, "reels": {"set": "freeSpins", "positions": [9, 30, 31, 28, 6], "view": [[1, 0, 9, 6, 5], [9, 4, 8, 0, 7], [6, 8, 10, 3, 3]]}}]}}