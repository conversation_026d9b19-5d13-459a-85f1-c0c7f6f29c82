export interface TwofaTypeList {
  [type: string]: TwofaTypeItem;
}

export const TWOFA_TYPE_NAMES = {
  google: 'google',
  sms: 'sms',
  email: 'email',
};

export class TwofaTypeItem {
  name: string;
  initialSetupRequired: boolean;
  challengeOnChange: boolean;
  displayName: string;

  constructor( obj?: any ) {
    this.name = obj && obj.name || '';
    this.initialSetupRequired = obj && obj.initialSetupRequired || false;
    this.challengeOnChange = obj && obj.challengeOnChange || false;
    this.displayName = obj && obj.displayName || '';
  }
}

export const twofaTypes: TwofaTypeList = {
  [TWOFA_TYPE_NAMES.google]: new TwofaTypeItem({
    name: TWOFA_TYPE_NAMES.google,
    initialSetupRequired: true,
    challengeOnChange: false,
    displayName: 'TWOFA.twofa_googleAuth',
  }),
  [TWOFA_TYPE_NAMES.sms]: new TwofaTypeItem({
    name: TWOFA_TYPE_NAMES.sms,
    initialSetupRequired: true,
    challengeOnChange: true,
    displayName: 'TWOFA.twofa_phoneBySMS',
  }),
  [TWOFA_TYPE_NAMES.email]: new TwofaTypeItem({
    name: TWOFA_TYPE_NAMES.email,
    initialSetupRequired: false,
    challengeOnChange: true,
    displayName: 'TWOFA.twofa_email',
  })
};

export const getTwofaType = ( name: string ) => {
  return twofaTypes[name];
};

export const isSMS = ( type: TwofaTypeItem ): boolean => {
  return type.name === TWOFA_TYPE_NAMES.sms;
};

export const isEmail = ( type: TwofaTypeItem ): boolean => {
  return type.name === TWOFA_TYPE_NAMES.email;
};

export const isGoogle = ( type: TwofaTypeItem ): boolean => {
  return type.name === TWOFA_TYPE_NAMES.google;
};
