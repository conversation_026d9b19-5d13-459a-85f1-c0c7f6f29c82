import { ControlOptionData } from '@skywind-group/lib-swui';
import {
  DEAFULT_GAME_THUMB_IMAGE,
  DEFAULT_ICON,
  DEFAULT_LIVE_LOGIN_BG,
  DEFAULT_LOGO,
  DEFAULT_PLAYTECH_HOME_BG,
  DEFAULT_PLAYTECH_LOGIN_BG
} from './theme.constants';

const rgba = require('color-rgba');

type ThemeOptionTarget = 'css' | 'token' | 'meta' | 'config' | 'builder';

function minify(value: string): string {
  return value.replace(/\s+/g, ' ').trim();
}

interface ThemeControlOptionData {
  target?: ThemeOptionTarget | ThemeOptionTarget[];
  cssFn?: (value: any) => string;
  metaFn?: (value: any) => string;
}

export interface ThemeOptionsData {
  [key: string]: ControlOptionData & ThemeControlOptionData;
}

export interface ThemeData {
  version: number;
  name: string;
  thumbnailUrl: string;
  options: ThemeOptionsData;
}

export type Theme = ThemeData & { key: string };

export interface Themes {
  [key: string]: ThemeData;
}

export function colorToRgba(color: string, alpha?: number): string {
  const r = rgba(color)[0],
    g = rgba(color)[1],
    b = rgba(color)[2],
    a = alpha !== undefined ? alpha : rgba(color)[3];
  return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + a + ')';
}

export const OPTIONS: ThemeOptionsData = {
  defaultLanguage: {
    type: 'select',
    target: 'meta',
    title: 'LOBBY.THEMES.defaultLanguage',
    defaultValue: 'en',
    data: [
      { value: 'en', title: 'LANGUAGES.en' },
      { value: 'zh', title: 'LANGUAGES.zh' },
      { value: 'zh-tw', title: 'LANGUAGES.zh-tw' },
      { value: 'ja', title: 'LANGUAGES.ja' },
      { value: 'ko', title: 'LANGUAGES.ko' },
      { value: 'ms', title: 'LANGUAGES.ms' },
      { value: 'id', title: 'LANGUAGES.id' },
      { value: 'vi', title: 'LANGUAGES.vi' },
      { value: 'th', title: 'LANGUAGES.th' },
      { value: 'ro', title: 'LANGUAGES.ro' },
      { value: 'it', title: 'LANGUAGES.it' },
      { value: 'el', title: 'LANGUAGES.el' },
      { value: 'km', title: 'LANGUAGES.km' },
      { value: 'es', title: 'LANGUAGES.es' },
      { value: 'pt', title: 'LANGUAGES.pt' },
      { value: 'pt-br', title: 'LANGUAGES.pt-br' },
      { value: 'ru', title: 'LANGUAGES.ru' },
      { value: 'de', title: 'LANGUAGES.de' },
      { value: 'sv', title: 'LANGUAGES.sv' },
      { value: 'da', title: 'LANGUAGES.da' },
      { value: 'nl', title: 'LANGUAGES.nl' },
      { value: 'bg', title: 'LANGUAGES.bg' },
      { value: 'sr', title: 'LANGUAGES.sr' },
      { value: 'tr', title: 'LANGUAGES.tr' }
    ]
  },
  cashierUrl: {
    type: 'url',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.cashierURL',
    defaultValue: ''
  },
  loginUrl: {
    type: 'text',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.loginURL',
    defaultValue: ''
  },
  showUnfinishedGames: {
    type: 'boolean',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.showUnfinishedGames',
    defaultValue: false
  },
  icon: {
    type: 'base64image',
    target: 'builder',
    fileInputLabel: 'LOBBY.THEMES.selectIcon',
    title: 'LOBBY.THEMES.iconImg',
    required: true,
    defaultValue: DEFAULT_ICON
  },
  disableLogout: {
    type: 'boolean',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.disableLogout',
    defaultValue: false
  },
  disableCloseGame: {
    type: 'boolean',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.disableCloseGame',
    defaultValue: false
  },
  numberOfPlayers_show: {
    type: 'boolean',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.numberOfPlayers_show',
    defaultValue: false
  },
  numberOfPlayers_games: {
    type: 'select-table',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.numberOfPlayers_games',
    defaultValue: false,
    showSearch: true,
    rowsNumber: 5,
    columns: [
      {
        field: 'title',
        name: 'Game name'
      },
      {
        field: 'code',
        name: 'Game code'
      }
    ],
    data: [],
    loading: true
  },
  refreshIntervalLive: {
    type: 'select',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.REFRESH_LIVE.title',
    defaultValue: '15',
    data: [
      { value: '15', title: 'LOBBY.THEMES.REFRESH_LIVE.value1' },
      { value: '30', title: 'LOBBY.THEMES.REFRESH_LIVE.value2' },
      { value: '45', title: 'LOBBY.THEMES.REFRESH_LIVE.value3' },
      { value: '60', title: 'LOBBY.THEMES.REFRESH_LIVE.value4' }
    ]
  },
  playerInactivityTime: {
    type: 'select',
    target: ['config', 'token'],
    title: 'LOBBY.THEMES.PLAYER_INACTIVITY.title',
    defaultValue: '600',
    data: [
      { value: '0', title: 'LOBBY.THEMES.PLAYER_INACTIVITY.value0' },
      { value: '600', title: 'LOBBY.THEMES.PLAYER_INACTIVITY.value4' },
      { value: '900', title: 'LOBBY.THEMES.PLAYER_INACTIVITY.value5' },
      { value: '1800', title: 'LOBBY.THEMES.PLAYER_INACTIVITY.value6' },
      { value: '3600', title: 'LOBBY.THEMES.PLAYER_INACTIVITY.value7' },
      { value: '7200', title: 'LOBBY.THEMES.PLAYER_INACTIVITY.value8' }
    ]
  },
  gameLaunchMode: {
    type: 'collection',
    title: 'LOBBY.FORM.gameLaunch',
    target: ['config', 'token'],
    inputs: {
      pwaDesktop: {
        type: 'select',
        title: 'LOBBY.FORM.gameLaunchDesktop',
        defaultValue: 'modal',
        emptyOption: false,
        data: [
          { value: 'modal', title: 'LOBBY.FORM.gameLaunchOption.modal' },
          { value: 'same-window', title: 'LOBBY.FORM.gameLaunchOption.same-window' },
          { value: 'new-window', title: 'LOBBY.FORM.gameLaunchOption.new-window' },
          { value: 'new-three-windows', title: 'LOBBY.FORM.gameLaunchOption.new-three-windows' }
        ]
      },
      pwaDesktopSize: {
        type: 'collection',
        title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.title',
        target: ['config', 'token'],
        inputs: {
          height: {
            type: 'collection',
            title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.height',
            target: ['config', 'token'],
            inputs: {
              type: {
                type: 'select',
                title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.type',
                defaultValue: 'percentage',
                emptyOption: false,
                data: [
                  { value: 'fixed', title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.typeOption.fixed' },
                  { value: 'percentage', title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.typeOption.percentage' },
                  { value: 'ratio', title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.typeOption.ratio' }
                ]
              },
              value: {
                type: 'number',
                title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.value',
                defaultValue: '0.7'
              },
            }
          },
          width: {
            type: 'collection',
            title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.width',
            target: ['config', 'token'],
            inputs: {
              type: {
                type: 'select',
                title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.type',
                defaultValue: 'ratio',
                emptyOption: false,
                data: [
                  { value: 'fixed', title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.typeOption.fixed' },
                  { value: 'percentage', title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.typeOption.percentage' },
                  { value: 'ratio', title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.typeOption.ratio' }
                ]
              },
              value: {
                type: 'number',
                title: 'LOBBY.FORM.GAME_LAUNCH.WINDOW_SIZE.value',
                defaultValue: '1.6'
              },
            }
          }
        }
      },
      pwaMobile: {
        type: 'select',
        title: 'LOBBY.FORM.gameLaunchMobile',
        defaultValue: 'modal',
        emptyOption: false,
        data: [
          { value: 'modal', title: 'LOBBY.FORM.gameLaunchOption.modal' },
          { value: 'same-window', title: 'LOBBY.FORM.gameLaunchOption.same-window' },
          { value: 'new-window', title: 'LOBBY.FORM.gameLaunchOption.new-window' },
          { value: 'new-three-windows', title: 'LOBBY.FORM.gameLaunchOption.new-three-windows' }
        ]
      }
    }
  }
};

const preloader: ControlOptionData & ThemeControlOptionData = {
  type: 'collection',
  target: 'meta',
  metaFn: value => {
    const { showPreloader, lobbyPreloaderLogo, lobbyPreloaderAnimation } = value;
    if (!showPreloader) {
      return '';
    }
    const keyframes = {
      none: '',
      fadeIn: `
      @-webkit-keyframes fadeIn {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      @keyframes fadeIn {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
    `,
      scaleIn: `
      @-webkit-keyframes scaleIn {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
        }
        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      }
      @keyframes scaleIn {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
        }
        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      }
    `,
      slideLeft: `
      @-webkit-keyframes slideLeft {
        0% {
          -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
          opacity: 0;
        }
        30% {
          opacity: 0;
        }
        100% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
          opacity: 1;
        }
      }
      @keyframes slideLeft {
        0% {
          -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
          opacity: 0;
        }
        30% {
          opacity: 0;
        }
        100% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
          opacity: 1;
        }
      }
    `,
      slideRight: `
      @-webkit-keyframes slideRight {
        0% {
          -webkit-transform: translateX(100%);
          transform: translateX(100%);
          opacity: 0;
        }
        30% {
          opacity: 0;
        }
        100% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
          opacity: 1;
        }
      }
      @keyframes slideRight {
        0% {
          -webkit-transform: translateX(100%);
          transform: translateX(100%);
          opacity: 0;
        }
        30% {
          opacity: 0;
        }
        100% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
          opacity: 1;
        }
      }
    `
    };

    return minify(`
      <style>
        .preloader {
          position: fixed;
          top: 0;
          left: 0;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          -webkit-box-pack: center;
          -ms-flex-pack: center;
          justify-content: center;
          width: 100vw;
          height: 100vh;
          z-index: 999;
        }
        .preloader:after {
          content: "";
          display: block;
          width: 100%;
          height: 100%;
          background-position: center;
          background-repeat: no-repeat;
          background-size: 300px;
          background-image: url(${lobbyPreloaderLogo});
          -webkit-animation-duration: 2s;
          animation-duration: 2s;
          -webkit-animation-timing-function: ease;
          animation-timing-function: ease;
          -webkit-animation-name: ${lobbyPreloaderAnimation};
          animation-name: ${lobbyPreloaderAnimation};
        }
        .app-loaded .preloader {
            -webkit-animation: hidePreloader .3s ease-in-out forwards;
            animation: hidePreloader .3s ease-in-out forwards;
        }
        @-webkit-keyframes hidePreloader {
          0% {
            z-index: 999;
            opacity: 1;
          }
          85% {
            opacity: 0;
          }
          100% {
            z-index: -1;
            opacity: 0;
          }
        }

        @keyframes hidePreloader {
          0% {
            z-index: 999;
            opacity: 1;
          }
          85% {
            opacity: 0;
          }
          100% {
            z-index: -1;
            opacity: 0;
          }
        }
        ${keyframes[lobbyPreloaderAnimation]}
      </style>
    `);
  },
  inputs: {
    showPreloader: {
      type: 'boolean',
      title: 'LOBBY.THEMES.PRELOADER.showPreloader',
      defaultValue: true
    },
    lobbyPreloaderLogo: {
      type: 'base64image',
      fileInputLabel: 'LOBBY.THEMES.PRELOADER.selectLogo',
      title: 'LOBBY.THEMES.PRELOADER.preloaderLogo',
      required: true,
      defaultValue: DEFAULT_LOGO,
    },
    lobbyPreloaderAnimation: {
      type: 'select',
      title: 'LOBBY.THEMES.PRELOADER.preloaderAnimation',
      data: [
        { value: 'none', title: 'LOBBY.THEMES.PRELOADER.disabled' },
        { value: 'fadeIn', title: 'LOBBY.THEMES.PRELOADER.fadeIn' },
        { value: 'scaleIn', title: 'LOBBY.THEMES.PRELOADER.scaleIn' },
        { value: 'slideLeft', title: 'LOBBY.THEMES.PRELOADER.slideLeft' },
        { value: 'slideRight', title: 'LOBBY.THEMES.PRELOADER.slideRight' }
      ],
      defaultValue: 'slideLeft',
    },
  }
};

export const THEMES: Themes = {
  bioshock: {
    name: 'Steampunk Theme',
    thumbnailUrl: 'img/dl-template-steampunk.png',
    version: 1,
    options: {
      logo: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.logoImg',
        required: false,
        defaultValue: DEFAULT_LOGO,
        cssFn: value => `.bo-logo-image { background-image: url(${value}) !important; }`
      },
      preloaderBg: {
        type: 'color',
        target: 'meta',
        title: 'LOBBY.THEMES.PRELOADER.backgroundColor',
        defaultValue: '#000000',
        metaFn: value => minify(`
            <style>
            body, .preloader {
              background: ${value};
            }
            </style>
          `)
      },
      customCss: {
        type: 'textarea',
        target: 'css',
        title: 'LOBBY.THEMES.customCss',
        required: false,
        defaultValue: '',
        autosize: { minRows: 2, maxRows: 40 },
        cssFn: value => `${value}`
      }
    }
  },
  live: {
    name: 'Live Theme',
    thumbnailUrl: 'img/dl-template-live.jpg',
    version: 1,
    options: {
      template: {
        type: 'select',
        target: ['config', 'token'],
        title: 'LOBBY.THEMES.template',
        defaultValue: 'default',
        emptyOption: false,
        data: [
          { value: 'default', title: 'LOBBY.THEMES.templateDefault' },
          { value: 'foxy', title: 'LOBBY.THEMES.templateFoxy' }
        ]
      },
      logo: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.logoImg',
        required: false,
        defaultValue: DEFAULT_LOGO,
        cssFn: value => `.bo-logo-image { background-image: url(${value}) !important; }`
      },
      preloaderBg: {
        type: 'color',
        target: 'meta',
        title: 'LOBBY.THEMES.PRELOADER.backgroundColor',
        defaultValue: '#000000',
        metaFn: value => minify(`
            <style>
            body, .preloader {
              background: ${value};
            }
            </style>
          `)
      },
      preloader,
      loginBgImage: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.COLORS.bgLoginImage',
        required: false,
        defaultValue: DEFAULT_LIVE_LOGIN_BG,
        cssFn: value => `.bo-login-bg-image { background-image: url(${value}) !important; }`
      },
      thumbBgImage: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.COLORS.gameThumbBgImage',
        required: false,
        defaultValue: DEAFULT_GAME_THUMB_IMAGE,
        cssFn: value => `.bo-thumb-img { background-image: url(${value}) !important; }`
      },
      primaryBg: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgPrimary',
        required: false,
        defaultValue: '#000000',
        cssFn: value => `
          .bo-page { background: ${value} !important; }
          .bo-page-gradient:after {
            background:
              -webkit-gradient(linear,left bottom,left top,from(${value}),to(${colorToRgba(value, 0)})) !important;
            background: linear-gradient(to top, ${value} 0%, ${colorToRgba(value, 0)} 100%) !important;
          }
          .bo-page-scroll::-webkit-scrollbar-track { background: ${colorToRgba(value, 0.5)} !important; }
          .bo-page-scroll::-webkit-scrollbar { background: ${colorToRgba(value, 0.5)} !important; }
          .bo-page-scroll::-webkit-scrollbar-thumb { background-color: ${value} !important; }
          .bo-limits {
            background: -webkit-gradient(
              linear,left top,left bottom,from(${value}),to(${colorToRgba(value, 0.7)})) !important;
            background: linear-gradient(to bottom, ${value} 0%, ${colorToRgba(value, 0.7)} 100%) !important;
          }
        `
      },
      additionalBg: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgExtra',
        required: false,
        defaultValue: '#383C48',
        cssFn: value => `
          .bo-thumb-caption { background: ${value} !important; }
          .bo-limits-selector { background: ${value} !important; }
        `
      },
      highlight: {
        type: 'color',
        target: ['config', 'css', 'token'],
        title: 'LOBBY.THEMES.COLORS.highlightColor',
        required: false,
        defaultValue: '#FFCF00',
        cssFn: value => `
          .bo-hover-color:hover,
          .bo-highlight-color,
          .bo-categories-item.active,
          .bo-categories-item.active .bo-categories-item-fit,
          .bo-categories-item:hover,
          .bo-categories-item:hover .bo-categories-item-fit,
          .bo-domik-logout:hover,
          .bo-lang-select-highlight-color,
          .bo-lang-select-item.active,
          .bo-lang-select-item.active .bo-lang-select-id,
          .bo-limits,
          .bo-limits-change:hover,
          .mobile .bo-limits-change,
          .bo-limits-selector-item.selected,
          .bo-limits-selector-item:hover { color: ${value} !important; }
          .bo-categories-item.active { border-color: ${value} !important; }
          .bo-btn-highlight { background: ${value} !important; }
          .bo-categories:not(.mobile):hover .bo-categories-next:after {
            background:
            -webkit-gradient(linear, right top, left top, from(${value}), to(${colorToRgba(value, 0)})) !important;
            background: linear-gradient(to left, ${value} 0%, ${colorToRgba(value, 0)}) !important; }
          @media(max-width: 1024px) { .bo-balance-value { color: ${value} !important; } }
        `
      },
      header: {
        type: 'collection',
        target: 'css',
        title: 'LOBBY.THEMES.GROUPS.groupHeader',
        cssFn: value => {
          if (!value) {
            return '';
          }
          const { headerBg, headerBorderColor } = value;
          return `
            .bo-header { background: ${headerBg} !important; border-color: ${headerBorderColor} !important }
            .bo-categories-prev {
              background: -webkit-gradient(
                linear, left top, right top, from(${headerBg}), to(${colorToRgba(headerBg, 0)})) !important;
              background: linear-gradient(to right, ${headerBg} 0%, ${colorToRgba(headerBg, 0)} 100%) !important;
            }
            .bo-categories-next:before {
              background: -webkit-gradient(
                linear, right top, left top, from(${headerBg}), to(${colorToRgba(headerBg, 0)})) !important;
              background: linear-gradient(to left, ${headerBg} 0%, ${colorToRgba(headerBg, 0)} 100%) !important;
            }
          `;
        },
        inputs: {
          headerBg: {
            type: 'color',
            title: 'LOBBY.THEMES.COLORS.bgHeader',
            required: false,
            defaultValue: '#000000'
          },
          headerBorderColor: {
            type: 'color',
            title: 'LOBBY.THEMES.COLORS.bottomBorderColor',
            required: false,
            defaultValue: '#282A35'
          }
        }
      },
      mobile: {
        type: 'collection',
        target: 'css',
        title: 'LOBBY.THEMES.GROUPS.groupMobile',
        cssFn: value => {
          if (!value) {
            return '';
          }
          const { categoriesBg, thumbBg } = value;
          return `
            @media (max-width: 1024px){
              @media(orientation: portrait) {
                .bo-categories { background: ${categoriesBg} !important; }
                .bo-categories-prev {
                  background: -webkit-gradient(
                    linear, left top, right top, from(${categoriesBg}), to(${colorToRgba(categoriesBg, 0)})) !important;
                  background:
                    linear-gradient(to right, ${categoriesBg} 0%, ${colorToRgba(categoriesBg, 0)} 100%) !important;
                }
                .bo-categories-next {
                  background: -webkit-gradient(
                    linear, right top, left top, from(${categoriesBg}), to(${colorToRgba(categoriesBg, 0)})) !important;
                  background:
                    linear-gradient(to left, ${categoriesBg} 0%, ${colorToRgba(categoriesBg, 0)} 100%) !important;
                }
                .bo-thumb-caption {
                  background: -webkit-gradient(
                    linear,left top,left bottom,from(${thumbBg}),to(${colorToRgba(categoriesBg, 0)})) !important;
                  background:
                    linear-gradient(to bottom, ${thumbBg} 0%, ${colorToRgba(categoriesBg, 0)} 100%) !important;
                }
                .bo-thumb-scoreboard { background: ${thumbBg} !important; }
                .thumb:not(.mobile):hover,
                .thumb:not(.mobile):hover .bo-thumb-caption { background: ${thumbBg} !important; }
                .thumb.mobile .bo-thumb-caption.thumb__caption--active { background: ${thumbBg} !important; }
              }
            }
          `;
        },
        inputs: {
          categoriesBg: {
            type: 'color',
            title: 'LOBBY.THEMES.COLORS.categoriesBg',
            required: false,
            defaultValue: '#0B0E19',
          },
          thumbBg: {
            type: 'color',
            title: 'LOBBY.THEMES.COLORS.thumbBg',
            required: false,
            defaultValue: '#212531',
          }
        }
      },
      customCss: {
        type: 'textarea',
        target: 'css',
        title: 'LOBBY.THEMES.customCss',
        required: false,
        defaultValue: '',
        autosize: { minRows: 2, maxRows: 40 },
        cssFn: value => `${value}`
      }
    }
  },
  playtech: {
    name: 'Default Theme',
    thumbnailUrl: 'img/dl-template-default.png',
    version: 1,
    options: {
      logo: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.logoImg',
        required: false,
        defaultValue: DEFAULT_LOGO,
        cssFn: value => `.bo-logo-image { background-image: url(${value}) !important; }`
      },
      isLogoOnLogin: {
        type: 'boolean',
        target: 'css',
        title: 'LOBBY.THEMES.isLogoOnLogin',
        defaultValue: false,
        cssFn: value => `.bo-logo-login { display: ${value ? 'block' : 'none'} !important; }`
      },
      lobbyLayout: {
        type: 'select',
        target: ['config', 'token'],
        title: 'LOBBY.THEMES.LAYOUT.layoutType',
        defaultValue: 'medium',
        data: [
          { value: 'large', title: 'LOBBY.THEMES.LAYOUT.large' },
          { value: 'medium', title: 'LOBBY.THEMES.LAYOUT.medium' },
          { value: 'small', title: 'LOBBY.THEMES.LAYOUT.small' }
        ]
      },
      preloaderBg: {
        type: 'color',
        target: 'meta',
        title: 'LOBBY.THEMES.PRELOADER.backgroundColor',
        defaultValue: '#000000',
        metaFn: value => minify(`
              <style>
              body, .preloader {
                background: ${value};
              }
              </style>
            `)
      },
      preloader,
      bgMainColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgPrimary',
        defaultValue: '#232323',
        cssFn: value => value ? `.bo-bg-main { background-color: ${value} !important; }` : ''
      },
      bgExtraColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgExtra',
        defaultValue: '#1b1b1b',
        cssFn: value => value ? `.bo-bg-sub { background-color: ${value} !important; }` : ''
      },
      bgBorderColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.borderColor',
        defaultValue: 'rgba(255, 255, 255, 0.2)',
        cssFn: value => value ? `.bo-border-color { border-color: ${value} !important; }` : ''
      },
      iconToggleColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.iconViewToggleColor',
        defaultValue: 'rgba(255, 255, 255, 0.25)',
        cssFn: value => value ? `.bo-view-toggle-color { color: ${value} !important; }` : ''
      },
      bgHighlightColor: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgHighlight',
        defaultValue: 'linear-gradient(to bottom, #2a87c1 0%, #014c7e 50%, #013f69 51%, #014a7a 100%)',
        cssFn: value => value ? `.bo-highlight-bg {
         background: ${value} !important;
         box-shadow: none !important; }` : ''
      },
      fontHighlightColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.fontHighlight',
        defaultValue: '#4db7fb',
        cssFn: value => value ? `.bo-highlight-color { color: ${value} !important; }` : ''
      },
      fontHoverColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.fontHoverColor',
        defaultValue: '#4db7fb',
        cssFn: value => value ? `.bo-hover-color:hover { color: ${value} !important; }` : ''
      },
      errorColor: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.errorColor',
        defaultValue: '#8b0000',
        cssFn: value => value ? `.bo-error { color: ${value} !important; }` : ''
      },
      bgLoginDialogColor: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgLoginDialogColor',
        defaultValue: 'linear-gradient(to top, rgba(18, 16, 16, 0.99) 0%, rgba(18, 16, 16, 0.68) 70%, ' +
          'rgba(61, 61, 61, 0.99) 100%)',
        cssFn: value => value ? `.bo-login-dialog-bg { background: ${value} !important; }` : ''
      },
      bgLoginImage: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.COLORS.bgLoginImage',
        required: false,
        defaultValue: DEFAULT_PLAYTECH_LOGIN_BG,
        cssFn: value => value ? `.bo-login-bg { background-image: url(${value}) !important; }` : ''
      },
      bgImageHome: {
        type: 'base64image',
        target: 'css',
        fileInputLabel: 'LOBBY.THEMES.chooseFile',
        title: 'LOBBY.THEMES.COLORS.bgHomeImage',
        required: false,
        defaultValue: DEFAULT_PLAYTECH_HOME_BG,
        cssFn: value => value ? `.bo-home-bg-image { background-image: url(${value}) !important; }` : ''
      },
      bgHeader: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgHeader',
        defaultValue: 'linear-gradient(to bottom, #5a5b5d 0%, #1a1b1d 31%, #1a1b1d 100%)',
        cssFn: value => value ? `.bo-header-bg { background: ${value} !important; }` : ''
      },
      bgPanel: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgPanel',
        defaultValue: 'linear-gradient(0deg, rgba(0, 0, 0, 0.56), rgba(0, 0, 0, 0))',
        cssFn: value => value ? `.bo-panel-bg { background: ${value} !important; }` : ''
      },
      bgAside: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgSidebar',
        defaultValue: 'radial-gradient(circle at 180% 80%, #1a1b1d, #1a1b1d 66%, #222325 66%)',
        cssFn: value => value ? `.bo-aside-bg { background: ${value} !important; }` : ''
      },
      bgModal: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgModal',
        defaultValue: 'linear-gradient(to bottom, #4c5053 0%, #383d40 51%, #1d2124 100%)',
        cssFn: value => value ? `.bo-modal-bg { background: ${value} !important; }` : ''
      },
      bgExtraButton: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgButtonAdditional',
        defaultValue: 'linear-gradient(to bottom, #bfbfbf 0%, #b0b0b0 22%, #474747 100%)',
        cssFn: value => value ? `.bo-sub-btn-bg {
         background: ${value} !important;
         box-shadow: none !important; }` : ''
      },
      bgListItemHover: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgListItemStateHover',
        defaultValue: 'linear-gradient(to right, rgba(84, 84, 84, 0.95) 0%, rgba(125, 125, 125, 0.95) 25%, ' +
          'rgba(125, 125, 125, 0.95) 37%, black 100%)',
        cssFn: value => value ? `.bo-list-item-state:hover { background: ${value} !important; }` : ''
      },
      bgListItemActive: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgListItemStateActive',
        defaultValue: 'linear-gradient(to right, rgba(39, 48, 54, 0.95) 0%, rgba(24, 43, 56, 0.95) 51%, ' +
          'rgba(19, 24, 27, 0.95) 100%)',
        cssFn: value => value ? `.bo-list-item-state.active { background: ${value} !important; }` : ''
      },
      bgTriangleColor: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgTriangleColor',
        defaultValue: '#014a7a',
        cssFn: value => value ? `.bo-toggle-triangle { border-top-color: ${value} !important; }` : ''
      },
      shadowBalance: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.shadowBalance',
        defaultValue: 'inset 1px 1px 1px 0 rgba(255, 255, 255, 0.5)',
        cssFn: value => value ? `.bo-balance-shadow { box-shadow: ${value} !important; }` : ''
      },
      bgScrollBar: {
        type: 'color',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgScrollBar',
        defaultValue: '#26272a',
        cssFn: value => value ? `.bo-scroll-styled::-webkit-scrollbar {
         background-color: ${value} !important;
         border-color: transparent important; }` : ''
      },
      bgScrollThumb: {
        type: 'text',
        target: 'css',
        title: 'LOBBY.THEMES.COLORS.bgScrollThumb',
        defaultValue: 'linear-gradient(to right, rgba(75, 73, 73, 1) 0%, rgba(85, 84, 84, 1) 51%, ' +
          'rgba(103, 101, 101, 1) 100%)',
        cssFn: value => value ? `.bo-scroll-styled::-webkit-scrollbar-thumb {
         background: ${value} !important;
         box-shadow: none important; }` : ''
      },
      customCss: {
        type: 'textarea',
        target: 'css',
        title: 'LOBBY.THEMES.customCss',
        required: false,
        defaultValue: '',
        autosize: { minRows: 2, maxRows: 40 },
        cssFn: value => `${value}`
      }
    }
  }
};
