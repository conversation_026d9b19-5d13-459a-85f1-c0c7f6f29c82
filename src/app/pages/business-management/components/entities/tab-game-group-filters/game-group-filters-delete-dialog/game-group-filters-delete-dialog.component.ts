import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { GameGroupFiltersService } from '../../../../../../common/services/game-group-filters.service';

@Component({
  selector: 'game-group-filters-delete-dialog',
  templateUrl: 'game-group-filters-delete-dialog.component.html',
})
export class GameGroupFiltersDeleteDialogComponent {

  path: string;
  gameGroup: string;
  filterId: string;

  constructor( private dialogRef: MatDialogRef<GameGroupFiltersDeleteDialogComponent>,
               private gameGroupFiltersService: GameGroupFiltersService,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               @Inject(MAT_DIALOG_DATA) public data: { path: string, gameGroup: string, filterId: string }
  ) {
    this.path = data.path;
    this.gameGroup = data.gameGroup;
    this.filterId = data.filterId;
  }

  cancel() {
    this.dialogRef.close();
  }

  submit() {
    this.gameGroupFiltersService.deleteGameGroupFilter(this.path, this.gameGroup, this.filterId).subscribe(
      () => {
        this.dialogRef.close();
        this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.NOTIFICATIONS.deleted'));
      }
    );
  }
}
