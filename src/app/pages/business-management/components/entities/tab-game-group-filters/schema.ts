import { SelectInputOptionData, SelectOptionItem, SwuiGridField } from '@skywind-group/lib-swui';
import { GameGroupFilter } from '../../../../../common/models/game-group.model';
import { FormattedMoneyPipe } from '../../../../../common/pipes/formatted-money/formatted-money.pipe';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'id',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.id',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'click',
      nowrap: true,
      isDisabled: ( row: GameGroupFilter ) => !row.group?.isOwner,
    },
  },
  {
    field: 'createdAt',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.createdAt',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'updatedAt',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.updatedAt',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'groupId',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.gameGroup',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: ( row: GameGroupFilter, field: SwuiGridField & SelectInputOptionData ) => {
        const gameGroup = (field.data as SelectOptionItem[])?.find(item => item.id === row.groupId);
        if (!gameGroup) {
          return '';
        }
        return gameGroup.isDefault ? `${gameGroup.name} (Default)` : gameGroup.name;
      },
      classFn: () => {
      }
    }
  },
  {
    field: 'minTotalBet',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.minTotalBet',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: GameGroupFilter ) => row.minTotalBet ? new FormattedMoneyPipe().transform(row.minTotalBet) : '--',
      classFn: () => {
      },
    },
  },
  {
    field: 'maxTotalBet',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.maxTotalBet',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: GameGroupFilter ) => row.maxTotalBet ? new FormattedMoneyPipe().transform(row.maxTotalBet) : '--',
      classFn: () => {
      },
    },
  },
  {
    field: 'maxExposure',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.maxExposure',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: GameGroupFilter ) => row.maxExposure ? new FormattedMoneyPipe().transform(row.maxExposure) : '--',
      classFn: () => {
      },
    },
  },
  {
    field: 'defTotalBet',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.defaultTotalBet',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: GameGroupFilter ) => row.defTotalBet ? new FormattedMoneyPipe().transform(row.defTotalBet) : '--',
      classFn: () => {
      },
    },
  },
  {
    field: 'winCapping',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.winCapping',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: GameGroupFilter ) => row.winCapping ? new FormattedMoneyPipe().transform(row.winCapping) : '--',
      classFn: () => {
      },
    },
  },
  {
    field: 'currencies',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.currencies',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'list',
      valueFn: ( row: GameGroupFilter ) => {
        return row.currencies.length ? row.currencies : ['All currencies'];
      },
      useTranslate: false,
    },
  },
  {
    field: 'games',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.games',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'list',
      valueFn: ( row: GameGroupFilter ) => {
        return row.games.length ? row.games : ['All games'];
      },
    },
  },
  {
    field: 'isInherited',
    title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.inherited',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'inactivity',
      valueFn: ( row: any ) => !row.group?.isOwner,
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
