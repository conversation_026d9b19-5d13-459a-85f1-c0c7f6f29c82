<div class="game-group-filters margin-bottom16">
  <hints message="ENTITY_SETUP.GAME_GROUP_FILTERS.note"></hints>
</div>

<lib-swui-grid #grid
               class="game-group__grid"
               [schema]="schema"
               [rowActions]="rowActions"
               [disableRefreshAction]="true"
               (widgetActionEmitted)="onClick($event)">
  <div class="grid-head-button">
    <button mat-flat-button color="primary" (click)="showGameGroupFiltersModal()">
      {{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.newFilter' | translate }}
    </button>
  </div>
</lib-swui-grid>
