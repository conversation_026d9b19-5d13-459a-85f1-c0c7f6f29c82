import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { TabGameGroupFiltersComponent } from './tab-game-group-filters.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: TabGameGroupFiltersComponent,
        resolve: {
          currencies: CurrenciesResolver,
        }
      }
    ])
  ],
  exports: [
    RouterModule,
  ]
})
export class TabGameGroupFiltersRoutingModule {
}
