<h3 mat-dialog-title class="no-margin-top mb-20">
  {{ (!!gameGroupFilter ?
  'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.editGameGroupFilterTitle' :
  'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.createGameGroupFilterTitle') | translate }}
</h3>

<div mat-dialog-content>
  <form [formGroup]="form" fxLayout="column" style="padding-top: 20px;">
    <mat-form-field appearance="outline" class="select">
      <lib-swui-select
        [placeholder]="'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.gameGroup' | translate"
        [data]="gameGroupsSelectOptions"
        [showSearch]="true"
        [disableEmptyOption]="true"
        [formControl]="gameGroupControl">
      </lib-swui-select>
      <mat-error>
        <lib-swui-control-messages
          [messages]="errorMessages"
          [control]="gameGroupControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="input">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.minTotalBet' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="minTotalBetControl"/>
      <mat-error>
        <lib-swui-control-messages [control]="minTotalBetControl" [messages]="errorMessages"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="input">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.maxTotalBet' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="maxTotalBetControl"/>
      <mat-error>
        <lib-swui-control-messages [control]="maxTotalBetControl" [messages]="errorMessages"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="input">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.maxExposure' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="maxExposureControl"/>
      <mat-error>
        <lib-swui-control-messages [control]="maxExposureControl" [messages]="errorMessages"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="input">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.defaultTotalBet' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="defTotalBetControl"/>
      <mat-error>
        <lib-swui-control-messages [control]="defTotalBetControl" [messages]="errorMessages"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="input">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.winCapping' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="winCappingControl"/>
      <mat-error>
        <lib-swui-control-messages [control]="winCappingControl" [messages]="errorMessages"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <control-messages class="validation-error-label" [control]="form" [forceShow]="submitted"></control-messages>

    <div style="padding: 15px 0 15px 0;">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.currencies' | translate }}</div>

    <mat-radio-group [value]="gameGroupFilter?.currencies.length ? 'specificCurrencies' : 'allCurrencies'"
                     (change)="changeAppliesToCurrenciesValue($event)">
      <div>
        <mat-radio-button [value]="'allCurrencies'">
          <div style="padding-left: 10px;">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.appliesToAllCurrenciesLabel' | translate }}</div>
        </mat-radio-button>
      </div>
      <div style="padding: 15px 0 15px 0;">
        <mat-radio-button [value]="'specificCurrencies'">
          <div style="padding-left: 10px;">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.appliesToSpecificCurrenciesLabel' | translate }}</div>
        </mat-radio-button>
      </div>
    </mat-radio-group>

    <mat-form-field *ngIf="showCurrenciesSelect" appearance="outline" class="select">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.currencies' | translate }}</mat-label>
      <lib-swui-chips-autocomplete
        [items]="currencySelectOptions"
        [formControl]="currenciesControl">
      </lib-swui-chips-autocomplete>
      <mat-error>
        <lib-swui-control-messages [control]="currenciesControl" [messages]="errorMessages"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <div style="padding: 20px 0 15px 0;">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.games' | translate }}</div>

    <mat-radio-group [value]="gameGroupFilter?.games.length ? 'specificGames' : 'allGames'"
                     (change)="changeAppliesToGamesValue($event)">
      <div>
        <mat-radio-button [value]="'allGames'">
          <div style="padding-left: 10px;">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.appliesToAllGamesLabel' | translate }}</div>
        </mat-radio-button>
        <div *ngIf="!showGamesSelectManager && fullSpecificGamesList?.length" class="note">
          <div class="pb-10">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.appliesToAllGamesLabelNote' | translate }}</div>
          <div *ngFor="let specificGame of fullSpecificGamesList" class="pl-15">
            {{ '• ' + specificGame.title + ' (' + specificGame.code + ')' }}
          </div>
        </div>
      </div>
      <div style="padding-top: 15px;">
        <mat-radio-button [value]="'specificGames'">
          <div style="padding-left: 10px;">{{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.appliesToSpecificGamesLabel' | translate }}</div>
        </mat-radio-button>
        <div *ngIf="showGamesSelectManager" class="note">
          {{ 'ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.appliesToSpecificGamesLabelNote' | translate }}
        </div>
      </div>
    </mat-radio-group>

    <lib-swui-games-select-manager class="games-select-manager"
                                   *ngIf="showGamesSelectManager"
                                   (selectedItemsChanged)="handleSelectedItemsChange($event)"
                                   [availableGames]="games"
                                   [selectedGames]="selectedGames">
    </lib-swui-games-select-manager>
  </form>
</div>

<div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
  <button mat-button color="primary" (click)="cancel()" class="mr-5 mat-button-md">{{ 'DIALOG.cancel' | translate }}</button>
  <button mat-flat-button color="primary" class="mat-button-md" (click)="submit()">{{ 'DIALOG.save' | translate }}</button>
</div>
