import { <PERSON>mpo<PERSON>, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatRadioChange } from '@angular/material/radio';
import { TranslateService } from '@ngx-translate/core';
import { GameSelectItem, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { CurrencyModel } from '../../../../../../common/models/currency.model';
import { Entity } from '../../../../../../common/models/entity.model';

import { GameGroup, GameGroupFilter } from '../../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { GameGroupFiltersService } from '../../../../../../common/services/game-group-filters.service';
import { GameGroupService } from '../../../../../../common/services/game-group.service';
import { comparisonTypes, ValidationService } from '../../../../../../common/services/validation.service';
import { Currency, GameInfo, isGameSupportDecreasingMaxBet, isGameSupportIncreasingMinBet } from '../../../../../../common/typings';
import { ERROR_MESSAGES } from '../game-group-filters-form.constants';

@Component({
  selector: 'game-group-filters-dialog',
  templateUrl: 'game-group-filters-dialog.component.html',
  styleUrls: ['./game-group-filters-dialog.component.scss'],
})
export class GameGroupFiltersDialogComponent implements OnInit, OnDestroy {

  errorMessages = ERROR_MESSAGES;

  minTotalBetDisplayName = this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.minTotalBet');
  maxTotalBetDisplayName = this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.maxTotalBet');
  maxExposureDisplayName = this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.maxExposure');
  defaultTotalBetDisplayName = this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.defaultTotalBet');
  winCappingDisplayName = this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.winCapping');

  displayNamesOfRequiredFields = [
    this.minTotalBetDisplayName,
    this.maxTotalBetDisplayName,
    this.maxExposureDisplayName,
    this.defaultTotalBetDisplayName,
    this.winCappingDisplayName,
  ];

  gameGroupFilter?: GameGroupFilter;
  entity: Entity;
  currencies: Currency[];
  form: FormGroup;

  gameGroupControl: FormControl = new FormControl('', Validators.required);

  public gameGroupsSelectOptions: SelectOptionModel[] = [];
  public currencySelectOptions: SelectOptionModel[] = [];

  public showCurrenciesSelect;
  public showGamesSelectManager;

  public games: GameSelectItem[];
  public filteredGames: GameInfo[];
  public selectedGames: GameSelectItem[] = [];

  public specificGames: GameInfo[] = [];
  public fullSpecificGamesList: GameInfo[] = [];

  public gameGroup?: string;

  public submitted;

  private destroyed$ = new Subject<void>();

  constructor( private dialogRef: MatDialogRef<GameGroupFiltersDialogComponent>,
               private fb: FormBuilder,
               private gameGroupService: GameGroupService,
               private gameGroupFiltersService: GameGroupFiltersService,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               @Inject(MAT_DIALOG_DATA) public data: {
                 gameGroupFilter: GameGroupFilter,
                 entity: Entity,
                 currencies: Currency[],
                 games: GameSelectItem[],
                 filteredGames: GameInfo[],
                 specificGames: GameInfo[],
               }
  ) {
    this.gameGroupFilter = data.gameGroupFilter;
    this.entity = data.entity;
    this.currencies = data.currencies;
    this.games = data.games;
    this.filteredGames = data.filteredGames;
    this.specificGames = data.specificGames;

    this.fullSpecificGamesList = this.specificGames;

    this.initGames();
    this.initForm();
  }

  ngOnInit() {
    this.buildGameGroupsSelectOptions();
    this.buildCurrencySelectOptions();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initForm() {
    this.form = this.fb.group({
      minTotalBet: [
        '', Validators.compose([
          ValidationService.positiveNumbers,
          ValidationService.fractionsNumbersLengthValidator,
        ])
      ],
      maxTotalBet: [
        '', Validators.compose([
          ValidationService.positiveNumbers,
          ValidationService.fractionsNumbersLengthValidator,
        ])
      ],
      maxExposure: [
        '', Validators.compose([
          ValidationService.positiveNumbers,
          ValidationService.fractionsNumbersLengthValidator,
        ])
      ],
      defTotalBet: [
        '', Validators.compose([
          ValidationService.positiveNumbers,
          ValidationService.fractionsNumbersLengthValidator,
        ])
      ],
      winCapping: [
        '', Validators.compose([
          ValidationService.positiveNumbers,
          ValidationService.fractionsNumbersLengthValidator,
        ])
      ],
      games: [[]],
      currencies: [[]],
    }, {
      validator: Validators.compose([
        ValidationService.atLeastOneFieldShouldBeRequired(
          ['minTotalBet', 'maxTotalBet', 'maxExposure', 'defTotalBet', 'winCapping'],
          this.displayNamesOfRequiredFields.join(', '),
        ),
        ValidationService.validateFirstMoreThanSecondAndLessThanThird('defTotalBet', 'minTotalBet', 'maxTotalBet',
          this.defaultTotalBetDisplayName, this.minTotalBetDisplayName, this.maxTotalBetDisplayName),
        ValidationService.compareFirstValueWithSecondValue(
          'minTotalBet',
          'maxTotalBet',
          comparisonTypes.lt,
          {
            minTotalBet: this.minTotalBetDisplayName,
            maxTotalBet: this.maxTotalBetDisplayName,
          },
          true,
        ),
      ]),
    });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(
        formValue => {
          this.fullSpecificGamesList = [];

          if (formValue.minTotalBet || formValue.maxTotalBet || formValue.maxExposure) {
            if (formValue.minTotalBet && !(formValue.maxTotalBet || formValue.maxExposure)) {
              this.fullSpecificGamesList = [
                ...this.filteredGames.filter(( game: GameInfo ) => !isGameSupportIncreasingMinBet(game)),
                ...this.specificGames,
              ];
            }

            if ((formValue.maxTotalBet || formValue.maxExposure) && !formValue.minTotalBet) {
              this.fullSpecificGamesList = [
                ...this.filteredGames.filter(( game: GameInfo ) => !isGameSupportDecreasingMaxBet(game)),
                ...this.specificGames,
              ];
            }

            if (formValue.minTotalBet && (formValue.maxTotalBet || formValue.maxExposure)) {
              this.fullSpecificGamesList = [
                ...this.filteredGames.filter(
                  ( game: GameInfo ) => !isGameSupportIncreasingMinBet(game) || !isGameSupportDecreasingMaxBet(game)
                ),
                ...this.specificGames,
              ];
            }
          } else {
            this.fullSpecificGamesList = this.specificGames;
          }
        }
      );

    if (this.gameGroupFilter) {
      this.showCurrenciesSelect = !!this.gameGroupFilter.currencies.length;
      this.showGamesSelectManager = !!this.gameGroupFilter.games.length;

      this.form.patchValue(this.gameGroupFilter);
    }
  }

  get minTotalBetControl(): FormControl {
    return this.form.get('minTotalBet') as FormControl;
  }

  get maxTotalBetControl(): FormControl {
    return this.form.get('maxTotalBet') as FormControl;
  }

  get maxExposureControl(): FormControl {
    return this.form.get('maxExposure') as FormControl;
  }

  get defTotalBetControl(): FormControl {
    return this.form.get('defTotalBet') as FormControl;
  }

  get winCappingControl(): FormControl {
    return this.form.get('winCapping') as FormControl;
  }

  get gamesControl(): FormControl {
    return this.form.get('games') as FormControl;
  }

  get currenciesControl(): FormControl {
    return this.form.get('currencies') as FormControl;
  }

  buildGameGroupsSelectOptions() {
    this.gameGroupService.getGameGroupsList(this.entity.path)
      .pipe(
        map(( data: GameGroup[] ) => data.filter(item => item.isOwner)),
        takeUntil(this.destroyed$)
      ).subscribe(
      data => {
        this.gameGroupsSelectOptions = data.map(( gameGroup: GameGroup ) => {
          if (this.gameGroupFilter?.groupId === gameGroup.id) {
            this.gameGroup = gameGroup.name;
            this.gameGroupControl.setValue(this.gameGroup);
          }

          let result = new GameGroup(gameGroup).toSelectOption();
          if (gameGroup.isDefault) {
            result.text = `${result.text} (${this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.FORM.defaultGroupLabel')})`;
          }

          return result;
        });
      }
    );
  }

  buildCurrencySelectOptions() {
    this.currencySelectOptions = [];

    this.currencies.map(( currency: any ) => {
      this.currencySelectOptions.push(
        (new CurrencyModel(currency)).toSelectOption());
    });
  }

  changeAppliesToCurrenciesValue( event: MatRadioChange ) {
    if (event.value === 'allCurrencies') {
      this.currenciesControl.setValue([]);
      this.showCurrenciesSelect = false;
    } else {
      this.showCurrenciesSelect = true;
    }
  }

  changeAppliesToGamesValue( event: MatRadioChange ) {
    if (event.value === 'allGames') {
      this.gamesControl.setValue([]);
      this.selectedGames = [];
      this.showGamesSelectManager = false;
    } else {
      this.showGamesSelectManager = true;
    }
  }

  handleSelectedItemsChange( { items } ) {
    let codes = items.map(item => item.id);
    this.gamesControl.setValue(codes);
  }

  initGames() {
    this.games?.forEach(game => {
      if (this.gameGroupFilter?.games.length && this.gameGroupFilter.games.includes(game.id)) {
        this.selectedGames.push(game);
      }
    });
  }

  cancel() {
    this.dialogRef.close();
  }

  submit() {
    this.gameGroupControl.markAllAsTouched();
    this.form.markAllAsTouched();

    this.submitted = true;

    if (this.gameGroupControl.valid && this.form.valid) {
      let body = {};
      Object.keys(this.form.value).forEach(key => {
        return this.form.value[key] !== '' && this.form.value[key] !== null ?
          body[key] = (['games', 'currencies'].indexOf(key) !== -1 ? this.form.value[key] : parseFloat(this.form.value[key])) :
          key;
      });

      if (!this.gameGroupFilter) {
        this.gameGroupFiltersService.createGameGroupFilter(this.entity.path, this.gameGroupControl.value, body).subscribe(
          () => {
            this.dialogRef.close();
            this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.NOTIFICATIONS.created'));
          }
        );
      } else {
        this.gameGroupFiltersService.updateGameGroupFilter(this.entity.path, this.gameGroupControl.value, this.gameGroupFilter.id,
          body).subscribe(
          () => {
            this.dialogRef.close();
            this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_GROUP_FILTERS.NOTIFICATIONS.updated'));
          }
        );
      }
    }
  }
}
