.jp-list {
  position: relative;
  &:hover {
    .jp-list {
      &__editable {
        display: block
      }
    }
  }
  &__editable {
    display: none;
  }
  a {
    position: absolute;
    top: 0;
    left: 5px;
  }
}


.mw-800 {
  max-width: 800px;
}

.label-xs {
  padding: 2px 5px 1px 5px;
}


.actions {
  position: relative;
  display: block;
  text-align: right;
}

/*3 tables grid*/


.table-fixed-header {
  border: 1px solid #ddd;
  tbody,
  thead,
  tr,
  td,
  th {
    display: block;
    width: 100%;
    white-space: nowrap;
  }
  td {
    display: block;
    width: 100%;
    white-space: nowrap;
  }
  tr {
    display: flex;
    flex-direction: row;
    &:first-child {
      td {
        border-top: none;
      }
    }
    &:last-child {
      td {
        border-bottom: 1px solid #ddd;
      }
    }
  }
  thead {
    border-bottom: 1px solid #ddd;
  }
  tbody {
    height: 280px;
    overflow-y: auto;
  }
  th {
    border-bottom: none !important;
  }
  .td-width-75 {
    width: calc(100% - 100px);
  }

  .td-width-25 {
    width: calc((100% - 100px) / 3)
  }

  .td-width-50 {
    width: calc(((100% - 100px) / 3) * 2);
  }

  .td-width-fixed {
    width: 100px;
  }

  .td-width-checkbox {
    width: 53px
  }

  .td-width-hascheckbox {
    width: calc(100% - 43px)
  }
}

.btn-hidden {
  visibility: hidden;
}

td {
  &.editable {
    padding: 3px 15px !important;
  }
  &.fixed-150 {
    width: 150px;
    min-width: 150px;
  }
}



.bg-warning-no-royalties {
  background: #FFB74D !important;
  animation: none !important;
}


td-percent-editable-widget {
  display: inline;
  .input-xs {
    height: 26px;
    padding: 5px;
    min-width: 22px;
  }

  .btn-xs {
    padding: 2px 8px;
    &.btn-success {
      padding: 2px 3px;
    }
  }
}


.twofa-setup {
  .panel.card {
    border none
    .heading-elements {
      right: 0;
      margin-top: -18px
    }
  }
}

.rates-form {
  max-width: 410px;
  width: 100%;
  @media (max-width: 768px) {
    max-width: 100%;
  }
}

.td-content-ellipsis {
  display: block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

#modal_jurisdictions {
  .modal-content {
    width: 100%
  }

  .modal-body {
    width: 100%
  }

  form {
       width: 100%
     }

  table {
    display: block;
  }
}

.modal-responsive {
  padding-left: 20px;
  padding-right: 20px;
}


.modal-responsive {
  .modal-dialog {
    width: 100%;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
  }
}


.role-cell{
  max-width: 150px;
  .widget{
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.btn-xs-action {
  font-size: 12px;
  padding: 2px 10px;
  line-height: 18px;
  margin: 0;
  opacity: 0.3;

  &:hover {
    opacity: 1;
  }
}

.options-inline {
  display: inline-block;
  padding-left: 5px;
}

li.checkbox-switchery {
  switchery {
    display: block;
  }

  .switchery {
    display: inline;

  }

  span.switchery.checked {
    small {
      left: 14px !important;
    }
  }

  &.checkbox-right {
    .switchery {
      left: auto !important;
      right: 15px;
    }
  }
}

.coin-balance {
  display: flex;
  align-items: center;
  label {
    margin-right: 10px;
    margin-bottom: 0
  }
  input {
    max-width: 100px;
  }
}

.entity-setup-nav {
  padding: 0;
  .mat-tab-link {
    padding: 0 10px;
    font-size: 13px;
    letter-spacing: 0;
  }
}
