<div class="entity-breadcrumbs">
  <div class="entity-breadcrumbs__item">
    <sw-entity-breadcrumbs-item [item]="breadcrumbs[0]"></sw-entity-breadcrumbs-item>
  </div>
  <div
    *ngIf="isCollapsed && breadcrumbs.length >= 4 && (activeBreadCrumbIndex$ | async) >= 3"
    matRipple
    class="entity-breadcrumbs__ellipsis"
    (click)="onEllipsisClick($event)">
    &#8230;
  </div>
  <div
    *ngFor="let item of getShiftedBreadCrumbsArray(); let i=index"
    class="entity-breadcrumbs__item"
    [ngClass]="{'entity-breadcrumbs__item--collapsed': isBreadCrumbCollapsed(item, i)}">
    <sw-entity-breadcrumbs-item [item]="item"></sw-entity-breadcrumbs-item>
  </div>
  <div *ngIf="emptyBreadcrumb" class="entity-breadcrumbs__item">
    <sw-entity-breadcrumbs-item [item]="emptyBreadcrumb"></sw-entity-breadcrumbs-item>
  </div>
  <button
    mat-icon-button
    matTooltip="{{'ENTITY_SETUP.BREADCRUMBS.copy' | translate}}"
    class="entity-breadcrumbs__copy"
    (click)="copyPath($event)">
    <mat-icon>settings_ethernet</mat-icon>
  </button>
  <button
    *ngIf="!isCollapsed && breadcrumbs.length >= 4 && (activeBreadCrumbIndex$ | async) >= 3"
    mat-icon-button
    matTooltip="{{'ENTITY_SETUP.BREADCRUMBS.collapse' | translate}}"
    class="entity-breadcrumbs__collapse"
    (click)="onCollapseClick($event)">
    <mat-icon>keyboard_tab</mat-icon>
  </button>
</div>
