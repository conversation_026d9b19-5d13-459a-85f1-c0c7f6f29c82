import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatRippleModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EntityBreadcrumbsItemComponent } from './entity-breadcrumbs-item.component';

export const ENTITY_BREADCRUMBS_ITEM_MODULES = [
  ReactiveFormsModule,
  MatIconModule,
  MatMenuModule,
  MatRippleModule,
];

@NgModule({
  declarations: [EntityBreadcrumbsItemComponent],
  exports: [EntityBreadcrumbsItemComponent],
    imports: [
        CommonModule,
        TranslateModule,
        ...ENTITY_BREADCRUMBS_ITEM_MODULES,
        TrimInputValueModule,
    ]
})
export class EntityBreadcrumbsItemModule { }
