  $color-primary: #1468cf;
  $color-border: rgba(0, 0, 0, .12);
  $color-text: rgba(0, 0, 0, 0.87);
  $color-disabled: #6e6e6f;
  $color-hover-bg: rgba(0,0,0,.06);

.breadcrumbs-item {
  display: flex;
  align-items: center;
  height: 24px;
  border-radius: 12px;
  box-shadow: 5px 0 5px rgb(0 0 0 / 3%);
  overflow: hidden;
  transition: all .15s ease-in-out;
  border-right: 1px solid  $color-border;
  &--disabled {
    opacity: 0.8;
    pointer-events: none;
  }
  &:not(.breadcrumbs-item--active) {
    &:not(.breadcrumbs-item--empty) {
      &:hover {
        .breadcrumbs-item__link {
          box-shadow: inset 0 0 0 1px $color-primary;
        }
      }
    }
  }
  &__icon {
    display: flex;
    justify-content: center;
    font-size: 20px;
    line-height: 24px;
    width: 24px;
    margin-right: -4px;
    border-radius: 12px;
    padding: 0 3px;
    overflow: hidden;
    pointer-events: initial;
    transition: background-color .15s ease-in-out;
    &:hover {
      background-color: $color-hover-bg;
    }
  }
  &__link {
    display: flex;
    align-items: center;
    padding: 0 12px;
    color: $color-primary;
    font-size: 12px;
    height: 100%;
    line-height: 24px;
    cursor: pointer;
    border-radius: 12px;
    transition: box-shadow .15s ease-in-out;
    &--selectable {
      padding-right: 4px;
    }
    &--active,
    &--empty {
      pointer-events: none;
    }
    &--empty {
      color: $color-disabled;
    }
    &--active {
      background-color: rgba($color-primary, .12);
    }
  }
}

.breadcrumbs-menu {
  width: 204px;
  &__list {
    max-height: 300px;
    overflow: auto;
  }
  &__search {
    display: flex;
    align-items: center;
    height: 40px;
    border-bottom: 1px solid  $color-border;
    input {
      display: block;
      width: 100%;
      height: 100%;
      padding: 0 12px;
      margin: 0;
      border: unset;
      font-size: 14px;
      color: $color-text;
    }
  }
  &__item {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
    line-height: 1;
    color: $color-text;
    cursor: pointer;
    &--empty {
      justify-content: center;
      font-size: 12px;
      color: $color-disabled;
      cursor: auto;
      user-select: none;
    }
    &--active {
      background-color: rgba($color-primary, .12);
      color: $color-primary;
      pointer-events: none;
    }
    &:not(.breadcrumbs-menu__item--empty) {
      &:hover {
        background-color: $color-hover-bg;
      }
    }
  }
  &__name {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
