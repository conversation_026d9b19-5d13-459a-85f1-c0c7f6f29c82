<div
  class="breadcrumbs-item"
  [ngClass]="{
    'breadcrumbs-item--active': !!item?.isActive,
    'breadcrumbs-item--empty': !item?.path,
    'breadcrumbs-item--disabled': disabled
  }">
  <div
    class="breadcrumbs-item__link"
    [ngClass]="{
          'breadcrumbs-item__link--active': !!item?.isActive,
          'breadcrumbs-item__link--selectable': !!item?.singleLevelItems.length,
          'breadcrumbs-item__link--empty': !item?.path
        }"
    (click)="onLinkClick($event, item)">
    {{item?.name || ('ENTITY_SETUP.BREADCRUMBS.selectEntity' | translate)}}
    <mat-icon
      *ngIf="!!item?.singleLevelItems.length"
      matRipple
      class="breadcrumbs-item__icon"
      [matMenuTriggerFor]="menu"
      (click)="onSelectButtonClick($event)">
      arrow_drop_down
    </mat-icon>
  </div>
  <mat-menu #menu="matMenu" (closed)="onMenuClosed()" xPosition="before" class="entity-breadcrumbs__menu">
    <div class="breadcrumbs-menu">
      <div class="breadcrumbs-menu__search" (click)="prevent($event)">
        <input trimValue type="text" [formControl]="searchControl" placeholder="Search">
      </div>
      <div #list class="breadcrumbs-menu__list">
        <div
          *ngFor="let menuItem of filteredSingleLevelItems"
          class="breadcrumbs-menu__item"
          [class.breadcrumbs-menu__item--active]="item.path === menuItem.path"
          (click)="onLinkClick($event, menuItem, true)">
          <div class="breadcrumbs-menu__name" [title]="menuItem.name">{{menuItem.name}}</div>
        </div>
        <div
          *ngIf="!filteredSingleLevelItems.length"
          class="breadcrumbs-menu__item breadcrumbs-menu__item--empty">
          {{'ENTITY_SETUP.BREADCRUMBS.noEntities' | translate}}
        </div>
      </div>
    </div>
  </mat-menu>
</div>
