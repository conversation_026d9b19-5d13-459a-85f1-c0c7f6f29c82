import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatRippleModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { EntityBreadcrumbsItemModule } from './entity-breadcrumbs-item/entity-breadcrumbs-item.module';
import { EntityBreadcrumbsComponent } from './entity-breadcrumbs.component';
import { LOCALSTORAGE_ENTITY_PATH } from './entity-breadcrumbs.model';

export const ENTITY_BREADCRUMBS_MODULES = [
  MatButtonModule,
  MatRippleModule,
  MatMenuModule,
  MatIconModule,
  MatTooltipModule,
  EntityBreadcrumbsItemModule,
];

@NgModule({
  declarations: [EntityBreadcrumbsComponent],
  exports: [EntityBreadcrumbsComponent],
  imports: [
    CommonModule,
    TranslateModule,
    ...ENTITY_BREADCRUMBS_MODULES,
  ]
})
export class EntityBreadcrumbsModule {
  constructor() {
    localStorage.removeItem(LOCALSTORAGE_ENTITY_PATH);
  }
}
