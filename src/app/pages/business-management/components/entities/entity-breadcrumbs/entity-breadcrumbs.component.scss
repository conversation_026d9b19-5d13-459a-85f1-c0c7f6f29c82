.entity-breadcrumbs {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  min-height: 40px;
  padding: 6px 40px 6px 32px;
  background: #fff;
  border-top: 1px solid rgba(0,0,0,.12);
  &__ellipsis {
    display: flex;
    align-items: center;
    border-right: 1px solid rgb(0 0 0 / 17%);
    border-radius: 12px;
    box-shadow: 5px 0 5px rgb(0 0 0 / 3%);
    overflow: hidden;
    transition: all .15s ease-in-out;
    color: #1468cf;
    height: 24px;
    cursor: pointer;
    padding: 0 12px;
    font-size: 20px;
    line-height: 18px;
    &:hover {
      background-color: rgba(#1468cf, .12);
    }
  }
  &__item {
    margin: 2px 0;
    &--collapsed {
      display: none;
    }
  }
  &__copy {
    position: absolute;
    right: 8px;
    top: 4px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    mat-icon {
      font-size: 20px;
      line-height: 22px;
    }
  }
  &__collapse {
    margin-left: 8px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    mat-icon {
      font-size: 20px;
      transform: rotate(180deg);
    }
  }
}


