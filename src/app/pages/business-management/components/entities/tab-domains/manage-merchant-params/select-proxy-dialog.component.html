<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.PROXY.labelSelectProxyForEntity' | translate }}
</h2>
<mat-dialog-content class="mat-typography">
  <mat-form-field appearance="outline" style="width: 100%">
    <mat-label>{{ 'ENTITY_SETUP.PROXY.proxyTabName' | translate }}</mat-label>
    <lib-swui-select [data]="selectboxProxies" [formControl]="proxyControl" [showSearch]="true"></lib-swui-select>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>
    {{ 'ENTITY_SETUP.PROXY.btnCancelProxy' | translate }}
  </button>
  <button
    mat-flat-button
    color="primary"
    cdkFocusInitial
    [class.disabled]="!selectedProxy"
    [disabled]="!selectedProxy"
    (click)="submitSelectedProxy()">
    {{ 'ENTITY_SETUP.PROXY.btnSaveChanges' | translate }}
  </button>
</mat-dialog-actions>
