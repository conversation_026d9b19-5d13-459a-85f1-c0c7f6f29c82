import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { Proxy } from '../../../../../../common/models/proxy.model';


export interface SelectProxyDialogData {
  merchantProxy: any;
  proxy: Proxy;
  proxies: Proxy[];
}

@Component({
  selector: 'select-proxy-dialog',
  templateUrl: 'select-proxy-dialog.component.html'
})
export class SelectProxyDialogComponent implements OnInit, OnDestroy {

  selectboxProxies: SwuiSelectOption[] = [];
  selectedProxy: Proxy;
  proxyControl: FormControl = new FormControl();

  private readonly destroyed$ = new Subject<void>();

  constructor(
    public dialogRef: MatDialogRef<SelectProxyDialogComponent, Proxy>,
    @Inject(MAT_DIALOG_DATA) public data: SelectProxyDialogData,
  ) {
    this.proxyControl.setValue(data.merchantProxy.proxy ? data.merchantProxy.proxy.id : '');
    this.processProxies();
  }

  ngOnInit() {
    this.proxyControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe( (id: string) => {
        this.selectedProxy = this.data.proxies.find(proxy => proxy.id === id);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  public selectProxy({ id }) {
    this.selectedProxy = this.data.proxies.find(proxy => proxy.id === id);
  }

  public submitSelectedProxy() {
    if (this.selectedProxy) {
      this.dialogRef.close(this.selectedProxy);
    }
  }

  private processProxies() {
    this.selectboxProxies = this.data.proxies.map(( item: Proxy ) => {
      let title = item.url;
      if ('description' in item) {
        title += ' (' + item.description + ')';
      }

      let obj = { id: item.id, text: title };

      if (this.data.proxy && this.data.proxy.id === item.id) {
        obj['selected'] = true;
      }

      return obj;
    });
  }
}
