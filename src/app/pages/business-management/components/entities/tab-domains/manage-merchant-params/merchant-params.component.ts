import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { filter, switchMap, take, takeUntil } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { Proxy } from '../../../../../../common/models/proxy.model';
import { ProxyService } from '../../../../../../common/services/proxy.service';
import { SelectProxyDialogComponent } from './select-proxy-dialog.component';

@Component({
  selector: 'merchant-params',
  templateUrl: './merchant-params.component.html',
  styleUrls: [
    './merchant-params.component.scss',
  ],
  providers: [
    ProxyService,
  ]
})

export class MerchantParamsComponent implements OnInit, On<PERSON><PERSON><PERSON> {
  @Input() entity: Entity;

  proxy: Proxy;
  proxies: Proxy[];
  merchantProxy: any;
  form: FormGroup;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private service: ProxyService<Proxy>,
    private dialog: MatDialog,
  ) {
    this.subscribeToData();
  }

  ngOnInit() {
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  loadData() {
    this.service.getList();
    this.fetchMerchantData();
  }

  setNewProxy() {
    this.dialog.open(SelectProxyDialogComponent, {
      width: '600px',
      data: {
        proxy: this.proxy,
        proxies: this.proxies,
        merchantProxy: this.merchantProxy,
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(result => !!result),
      switchMap(( proxy: Proxy ) => this.service.setEntityProxy(proxy.id, this.entity.path))
    ).subscribe(() => {
      this.loadData();
    });
  }

  resetButtonDisabled(): boolean {
    return !(this.merchantProxy && this.merchantProxy.hasOwnProperty('proxyId'));
  }

  resetMerchantProxy( event: Event ) {
    event.preventDefault();

    if (this.resetButtonDisabled()) {
      return;
    }

    this.service.setEntityProxy(null, this.entity.path)
      .pipe(
        take(1),
      ).subscribe(( data ) => {
      this.merchantProxy = data;
    });
  }

  private fetchMerchantData() {
    this.service.getEntityProxy(this.entity.path)
      .pipe(take(1))
      .subscribe(( data ) => this.merchantProxy = data);
  }

  private subscribeToData() {
    this.service.items
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( proxies: Proxy[] ) => this.proxies = proxies);
    this.service.item
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( proxy: Proxy ) => this.proxy = proxy);
  }
}
