<ng-template #emptyProxy>
  <strong class="text-warning">
    {{ 'ENTITY_SETUP.PROXY.labelNotSet' | translate }}
  </strong>
</ng-template>

<div class="proxy">
  <div class="proxy--info">
    <span style="text-transform: capitalize">{{ 'ENTITY_SETUP.PROXY.merchantProxy' | translate }}:</span>&nbsp;
    <ng-container *ngIf="merchantProxy?.proxyId; else emptyProxy">
      <strong>{{ merchantProxy.proxy.url }}</strong>
    </ng-container>
  </div>
  <div class="proxy--controls">
    <button mat-icon-button (click)="setNewProxy()" matTooltip="{{'ENTITY_SETUP.PROXY.btnSet' | translate}}">
      <mat-icon>edit</mat-icon>
    </button>
    <button mat-icon-button
            matTooltip="{{ 'ENTITY_SETUP.PROXY.btnRemove' | translate }}"
            [disabled]="!merchantProxy?.proxyId"
            (click)="resetMerchantProxy($event)">
      <mat-icon>delete</mat-icon>
    </button>
  </div>
</div>
