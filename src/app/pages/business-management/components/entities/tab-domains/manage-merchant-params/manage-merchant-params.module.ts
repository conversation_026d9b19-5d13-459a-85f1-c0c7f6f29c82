import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectModule } from '@skywind-group/lib-swui';

import { MerchantParamsComponent } from './merchant-params.component';
import { SelectProxyDialogComponent } from './select-proxy-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatFormFieldModule,
    SwuiSelectModule,
  ],
  exports: [
    MerchantParamsComponent,
  ],
  declarations: [
    MerchantParamsComponent,
    SelectProxyDialogComponent,
  ],
  providers: [],
  entryComponents: [
    SelectProxyDialogComponent
  ]
})
export class ManageMerchantParamsModule {
}
