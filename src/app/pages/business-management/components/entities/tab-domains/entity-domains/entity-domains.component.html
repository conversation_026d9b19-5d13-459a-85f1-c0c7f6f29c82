<mat-card class="mat-elevation-z0 domains-section">
  <div class="domains-section__title">{{'ENTITY_SETUP.DOMAINS.maintenanceUrl' | translate}}</div>
  <entity-maintenance [entity]="entity"></entity-maintenance>
</mat-card>

<mat-card class="mat-elevation-z0 domains-section">
  <div class="domains-section__title">Domain restrictions</div>
  <entity-static-domain-tags [entity]="entity"></entity-static-domain-tags>
  <entity-allowed-child-domains [entity]="entity"></entity-allowed-child-domains>
</mat-card>

<mat-card class="mat-elevation-z0 domains-section">
  <div class="domains-section__title">{{'ENTITY_SETUP.DOMAINS.entityDomains' | translate}}</div>

  <domain-item [domainType]="domainTypes.static" [entity]="entity" [staticDomainType]="staticDomainTypes.static"></domain-item>
  <domain-item [domainType]="domainTypes.static" [entity]="entity" [staticDomainType]="staticDomainTypes.lobby"></domain-item>
  <domain-item [domainType]="domainTypes.static" [entity]="entity" [staticDomainType]="staticDomainTypes.liveStreaming"></domain-item>
  <domain-item [domainType]="domainTypes.static" [entity]="entity" [staticDomainType]="staticDomainTypes.ehub"></domain-item>
  <domain-item [domainType]="domainTypes.dynamic" [entity]="entity"></domain-item>
</mat-card>

<mat-card class="mat-elevation-z0 domains-section">
  <div class="domains-section__title">{{'ENTITY_SETUP.DOMAINS.pools' | translate}}</div>
  <pool-item [entity]="entity" [poolType]="domainTypes.static"></pool-item>
  <pool-item [entity]="entity" [poolType]="domainTypes.dynamic"></pool-item>
</mat-card>

<mat-card *ngIf="entity.isMerchant" class="mat-elevation-z0 domains-section">
  <div class="domains-section__title">{{'ENTITY_SETUP.PROXY.merchantParams' | translate}}</div>
  <merchant-params [entity]="entity"></merchant-params>
</mat-card>
