import { Component } from '@angular/core';
import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';

@Component({
  selector: 'tab-domains',
  templateUrl: './tab-domains.component.html'
})
export class TabDomainsComponent {
  readonly entity?: Entity;

  constructor({entity}: SetupEntityService) {
    this.entity = entity;
  }
}
