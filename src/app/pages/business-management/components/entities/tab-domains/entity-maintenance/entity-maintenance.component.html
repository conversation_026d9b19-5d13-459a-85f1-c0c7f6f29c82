<form [formGroup]="form" class="entity-maintenance">
  <mat-form-field appearance="outline">
    <input
      matInput trimValue type="text"
      formControlName="maintenanceUrl"
      [placeholder]="'ENTITY_SETUP.DOMAINS.maintenancePlaceholder' | translate">
    <mat-error>
      <control-messages [control]="form.get('maintenanceUrl')"></control-messages>
    </mat-error>
  </mat-form-field>

  <button
    mat-icon-button
    color="primary"
    [disabled]="isCurrentUrl() || form.invalid"
    (click)="showConfirmation($event)"
    matTooltip="{{'ENTITY_SETUP.DOMAINS.btnApply' | translate}}">
    <mat-icon>save</mat-icon>
  </button>
</form>
