import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { EntityAllowedChildDomainsComponent } from './entity-allowed-child-domains.component';
import { EntitySettingsService } from 'src/app/common/services/entity-settings.service';
import { DomainsManagementService } from 'src/app/pages/domains-management/domains-management.service';
import { Entity } from 'src/app/common/typings';
import { EntitySettingsModel } from 'src/app/common/models/entity-settings.model';

describe('EntityAllowedChildDomainsComponent', () => {
  let component: EntityAllowedChildDomainsComponent;
  let fixture: ComponentFixture<EntityAllowedChildDomainsComponent>;
  let mockEntitySettingsService: jasmine.SpyObj<EntitySettingsService<EntitySettingsModel>>;
  let mockDomainsManagementService: jasmine.SpyObj<DomainsManagementService>;


  beforeEach(async () => {
    const entitySettingsServiceSpy = jasmine.createSpyObj('EntitySettingsService', [
      'getSettings',
      'patchSettings'
    ]);
    const domainsManagementServiceSpy = jasmine.createSpyObj('DomainsManagementService', ['getList']);
    const notificationsServiceSpy = jasmine.createSpyObj('SwuiNotificationsService', ['success']);

    await TestBed.configureTestingModule({
      declarations: [EntityAllowedChildDomainsComponent],
      imports: [
        ReactiveFormsModule,
        MatSelectModule,
        MatFormFieldModule,
        MatChipsModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: EntitySettingsService, useValue: entitySettingsServiceSpy },
        { provide: DomainsManagementService, useValue: domainsManagementServiceSpy },
        { provide: SwuiNotificationsService, useValue: notificationsServiceSpy }
      ]
    }).compileComponents();

    mockEntitySettingsService = TestBed.inject(EntitySettingsService) as jasmine.SpyObj<EntitySettingsService<EntitySettingsModel>>;
    mockDomainsManagementService = TestBed.inject(DomainsManagementService) as jasmine.SpyObj<DomainsManagementService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EntityAllowedChildDomainsComponent);
    component = fixture.componentInstance;

    // Mock entity
    component.entity = {
      path: 'test:entity'
    } as Entity;

    // Mock settings
    const mockSettings = {
      allowedStaticDomainsForChildId: ['domain1', 'domain2']
    } as EntitySettingsModel;

    mockEntitySettingsService.getSettings.and.returnValue(of(mockSettings));

    // Mock domains list
    mockDomainsManagementService.getList.and.returnValue(of([
      { id: 'domain1', domain: 'example1.com', type: 'static' },
      { id: 'domain2', domain: 'example2.com', type: 'lobby' }
    ] as any));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load available domains on init', () => {
    expect(mockDomainsManagementService.getList).toHaveBeenCalled();
    expect(component.availableDomains.length).toBe(2);
  });

  it('should load current settings and selection on init', () => {
    expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity');
    expect(component.selectedDomainIds).toEqual(['domain1', 'domain2']);
    expect(component.domainsControl.value).toEqual(['domain1', 'domain2']);
  });
});
