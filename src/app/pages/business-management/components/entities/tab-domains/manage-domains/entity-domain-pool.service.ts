import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { DOMAIN_TYPES, DomainPool, DomainType } from '../../../../../../common/models/domain.model';
import { toDomainPool } from '../../../../../domains-management/domains-pool/domains-pool.service';
import { API_ENDPOINT } from '../../../../../../app.constants';

function getUrl(path: string, poolType: DomainType, id?: string): string {
  return `${API_ENDPOINT}/entities/${path}/domain-pools/${id ? `${id}/` : ''}${poolType}`;
}

@Injectable()
export class EntityDomainPoolService {
  private readonly cachedItem: Record<string, DomainPool> = {};

  constructor(private readonly http: HttpClient,
              private readonly notifications: SwuiNotificationsService) {
  }

  get(path: string, poolType: DomainType, force = false) {
    if (!force) {
      if (this.cachedItem[path]) {
        return of(this.cachedItem[path]);
      }
    }
    return this.http.get(getUrl(path, poolType), { params: { inherited: true } }).pipe(
      catchError(() => of(undefined)),
      map(toDomainPool(DOMAIN_TYPES.static)),
      tap((data) => {
        this.cachedItem[path] = data;
      }),
    );
  }

  set(id: string, poolType: DomainType, path: string) {
    return this.http.put(getUrl(path, poolType, id), {}).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
      switchMap(() => this.get(path, poolType, true)),
    );
  }

  remove(poolType: DomainType, path: string) {
    return this.http.delete(getUrl(path, poolType)).pipe(
      tap(() => {
        delete this.cachedItem[path];
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }
}
