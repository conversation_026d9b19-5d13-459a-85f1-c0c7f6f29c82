<div class="domain">
  <div class="domain--info">
    <span>{{ 'ENTITY_SETUP.DOMAINS.allowedChildDomains' | translate }}:</span>
  </div>
  <div class="domain--controls">
    <mat-form-field appearance="outline" style="min-width: 320px; width: 100%; max-width: 560px; display: block;">
      <mat-label>{{ 'ENTITY_SETUP.DOMAINS.selectAllowedDomains' | translate }}</mat-label>
      <mat-select
        [formControl]="domainsControl"
        multiple
        (selectionChange)="onDomainSelectionChange($event.value)">
        <mat-option *ngFor="let domain of availableDomains" [value]="domain.id">
          {{ domain.domain }}
          <span *ngIf="domain.type" class="domain-type"> ({{ domain.type }})</span>
        </mat-option>
      </mat-select>
      <mat-hint align="start">Select which static domains child entities are allowed to use</mat-hint>
    </mat-form-field>

    <button mat-icon-button (click)="saveDomains()" [matTooltip]="'ENTITY_SETUP.DOMAINS.saveAllowedChildDomains' | translate">
      <mat-icon>save</mat-icon>
    </button>
    <button mat-icon-button (click)="resetDomains()" [matTooltip]="'ENTITY_SETUP.DOMAINS.resetAllowedChildDomains' | translate">
      <mat-icon>undo</mat-icon>
    </button>
  </div>
</div>

<div *ngIf="selectedDomainIds.length > 0" class="selected-domains-preview">
  <div class="selected-domains-title">{{ 'ENTITY_SETUP.DOMAINS.selectedDomains' | translate }}:</div>
  <div class="selected-domains-list">
    <mat-chip-list>
      <mat-chip *ngFor="let domainId of selectedDomainIds">
        {{ getDomainDisplayName(domainId) }}
      </mat-chip>
    </mat-chip-list>
  </div>
</div>
