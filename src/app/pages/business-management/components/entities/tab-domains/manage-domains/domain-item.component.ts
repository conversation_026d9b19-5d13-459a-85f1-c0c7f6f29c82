import { Component, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { combineLatest } from 'rxjs';
import { filter, finalize, switchMap, take } from 'rxjs/operators';
import { Domain, DOMAIN_TYPES, DomainType, StaticDomainType } from '../../../../../../common/models/domain.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { DomainsManagementService } from '../../../../../domains-management/domains-management.service';
import { SelectDomainDialogComponent } from './select-domain-dialog.component';
import { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';

@Component({
  selector: 'domain-item',
  templateUrl: './domain-item.component.html',
  styleUrls: [
    './domain-item.component.scss',
  ],
})
export class DomainItemComponent {
  readonly domainTypes = DOMAIN_TYPES;

  @Input() domainType: DomainType;
  @Input() staticDomainType: StaticDomainType;
  @Input() entity: Entity;

  domains: Domain[];
  domain: Domain;
  loading = true;

  private resetComplete: boolean = false;

  constructor(
    private readonly service: DomainsManagementService,
    private readonly entityDomainService: EntityDomainService,
    private readonly dialog: MatDialog,
  ) {
  }

  ngOnInit() {
    combineLatest([
      this.service.getList(this.domainType),
      this.entityDomainService.getEntityDomain(this.domainType, this.entity.path, true, this.staticDomainType)
    ]).pipe(
      finalize(() => this.loading = false),
      take(1)
    ).subscribe(([domains, domain]) => {
      this.domains = domains;
      this.domain = domain;
    });
  }

  resetButtonDisabled(): boolean {
    return !(this.domain && this.domain.hasOwnProperty('id')) || this.resetComplete;
  }

  resetToParent(event: Event) {
    event.preventDefault();
    if (this.resetButtonDisabled()) {
      return;
    }
    this.entityDomainService.removeEntityDomain(this.domainType, this.entity.path, this.staticDomainType).pipe(
      take(1),
    ).subscribe((domain) => {
      this.domain = domain;
      this.resetComplete = true;
    });
  }

  setNewDomain() {
    this.dialog.open(SelectDomainDialogComponent, {
      width: '600px',
      data: {
        domains: this.domains,
        domain: this.domain,
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(result => !!result),
      switchMap((domain) => this.entityDomainService.setEntityDomain(this.domainType, domain.id, this.entity.path, this.staticDomainType)),
      take(1),
    ).subscribe((result) => {
      this.domain = result;
      this.resetComplete = false;
    });
  }
}
