import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { Domain, DomainType } from '../../../../../../common/models/domain.model';

export interface SelectDomainDialogData {
  domain: Domain;
  domainType: DomainType;
  domains: Domain[];
}

@Component({
  selector: 'select-domain-dialog',
  templateUrl: 'select-domain-dialog.component.html'
})
export class SelectDomainDialogComponent implements OnInit, OnDestroy {
  readonly selectOptions: SwuiSelectOption[];
  readonly domainControl = new FormControl();

  selectedDomain: Domain;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly dialogRef: MatDialogRef<SelectDomainDialogComponent, Domain>,
    @Inject(MAT_DIALOG_DATA) public readonly data: SelectDomainDialogData,
  ) {
    this.domainControl.setValue(data.domain?.id ?? '');
    this.selectOptions = this.data.domains?.map((item: Domain) => {
      let title = item.domain;
      if ('environment' in item) {
        title += ' (' + item.environment + ')';
      }
      return {
        id: item.id,
        text: title
      };
    });
  }

  ngOnInit() {
    this.domainControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe((id: string) => {
        this.selectedDomain = this.data.domains?.find(domain => domain.id === id);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  submitSelectedDomain() {
    if (this.selectedDomain) {
      this.dialogRef.close(this.selectedDomain);
    }
  }
}
