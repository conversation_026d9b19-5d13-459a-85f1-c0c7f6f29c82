<lib-swui-schema-top-filter [schema]="filterSchema"></lib-swui-schema-top-filter>
<lib-swui-grid
  #grid
  class="entity-rtp-grid"
  [schema]="schema"
  [pageSize]="pageSize"
  [ignorePlainLink]="true"
  [rowActions]="actions"
  [rowActionsColumnTitle]="''">
  <div style="width: 100%; text-align: right">
    <button mat-flat-button
            color="primary"
            (click)="createNewConfiguration()"
            [disabled]="grid.loading$ | async">
      {{ 'ENTITY_SETUP.RTP_REDUCER.btnCreateRtp' | translate }}
    </button>
  </div>
</lib-swui-grid>
