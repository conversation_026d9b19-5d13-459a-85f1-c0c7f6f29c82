import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';
import { RtpReducer } from '../../../../../common/typings/rtp-reducer';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'entityTitle',
    title: 'ENTITY_SETUP.RTP_REDUCER.entity',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: RtpReducer ) => {
        return row?.entityTitle || '-';
      },
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'inheritedFromEntityTitle',
    title: 'ENTITY_SETUP.RTP_REDUCER.inheritedForm',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: RtpReducer ) => {
        return row?.inheritedFromEntityTitle || '-';
      },
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'left',
      td: 'center',
    },
  },
  {
    field: 'gameTitle',
    title: 'ENTITY_SETUP.RTP_REDUCER.gameName',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: RtpReducer ) => {
        return row?.gameTitle || '-';
      },
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'gameCode__in',
    title: 'ENTITY_SETUP.RTP_REDUCER.filterTitle',
    type: 'select',
    isList: false,
    isFilterable: true,
    showSearch: true,
    isFilterableAlways: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: RtpReducer ) => {
        return row?.gameTitle || '-';
      },
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'gameCode',
    title: 'ENTITY_SETUP.RTP_REDUCER.gameCode',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: RtpReducer ) => {
        return row?.gameCode || '-';
      },
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
    isFilterableAlways: true
  },
  {
    field: 'ts',
    title: 'ENTITY_SETUP.RTP_REDUCER.changeDateTime',
    type: 'datetimerange',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'timestamp',
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'rtp',
    title: 'ENTITY_SETUP.RTP_REDUCER.rtpBefore',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: ( row: RtpReducer ) => rtpResult(row?.rtp),
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'finalRTP',
    title: 'ENTITY_SETUP.RTP_REDUCER.rtpAfter',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: ( row: RtpReducer ) => rtpResult(row?.finalRTP),
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'rtpDeduction',
    title: 'ENTITY_SETUP.RTP_REDUCER.rtpDeduction',
    type: 'number',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    filterMatch: SchemaFilterMatchEnum.GreaterThanEquals,
    td: {
      type: 'calc',
      titleFn: ( row: RtpReducer ) => rtpResult(row?.rtpDeduction),
      classFn: ( row: RtpReducer ) => ({
        'text-green': (row.inheritedFromEntityId && row.entityId) && row.entityId !== row.inheritedFromEntityId,
        'nowrap': true
      }),
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
const rtpResult = (value: any) => {
  if (typeof value === 'string') {
    value = parseFloat(value);
  }
  return value?.toFixed(2) || '-';
};
