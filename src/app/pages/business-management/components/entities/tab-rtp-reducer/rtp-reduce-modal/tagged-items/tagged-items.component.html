<div class="table-sticky bordered" [ngStyle]="{height:height}">
  <div class="table-sticky__header" fxLayout="row" fxLayoutAlign="end center">
    <mat-checkbox
      class="table-sticky__select-all"
      [checked]="isAllChecked"
      (change)="toggleAllChecked()"
      [disabled]="disabled || !availableItems.length">
      {{ 'COMPONENTS.GAMES_SELECT_MANAGER.selectAll' | translate }}
    </mat-checkbox>
    <div class="table-sticky__info">
      <mat-form-field class="table-sticky__search no-field-padding" appearance="outline">
        <mat-icon matPrefix class="search-icon">search</mat-icon>
        <input
          matInput trimValue
          type="text"
          [formControl]="searchInput"
          [placeholder]="'Search by Name or Label'"
          [disabled]="disabled">
        <button mat-button *ngIf="searchInput.value" matSuffix mat-icon-button aria-label="Clear"
                (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
    </div>
  </div>

  <div class="table-sticky__body">
    <cdk-virtual-scroll-viewport
      class="table-sticky__scroll"
      [minBufferPx]="960"
      [maxBufferPx]="1008"
      [itemSize]="48">

      <div class="table-sticky__table">
        <div *ngIf="loading">
          <div class="loading-overlay"><i class="icon-spinner4 spinner"></i></div>
        </div>

        <div *ngIf="(!availableItems || availableItems.length === 0) && !loading">
          {{ 'COMPONENTS.GAMES_SELECT_MANAGER.noGamesToShow' | translate }}
        </div>

        <div class="table-sticky__row"
             *cdkVirtualFor="let game of availableItems; templateCacheSize: 0; trackBy: trackByFn"
             [ngClass]="{'selected': game.checked}">
          <div class="table-sticky__checkbox">
            <mat-checkbox
              class="games-checkbox"
              (change)="emitOnChanged()"
              [(ngModel)]="game.checked"
              [disabled]="disabled">
              <div class="table-sticky__checkbox_label"
                   [title]="game.title">{{ game.title }}</div>
            </mat-checkbox>
          </div>
          <div class="table-sticky__info">
            {{ game.id }}
          </div>
          <div class="table-sticky__chips">
            <mat-chip-list>
              <mat-chip class="chip" *ngFor="let label of game.labels" [ngClass]="getLabelClass(label.group)">
                {{ label.title | titlecase }}
              </mat-chip>
            </mat-chip-list>
          </div>
        </div>
      </div>
    </cdk-virtual-scroll-viewport>
  </div>
</div>
