import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatRadioModule } from '@angular/material/radio';
import { RtpReduceModalComponent } from './rtp-reduce-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatStepperModule } from '@angular/material/stepper';
import { MatButtonModule } from '@angular/material/button';
import { GamesFormModule } from './games-form/games-form.module';
import { FlexModule } from '@angular/flex-layout';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';



@NgModule({
  declarations: [
    RtpReduceModalComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    MatStepperModule,
    MatButtonModule,
    GamesFormModule,
    FlexModule,
    MatDialogModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    SwuiControlMessagesModule,
    ControlMessagesModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatRadioModule,
    FormsModule,
  ]
})
export class RtpReduceModalModule { }
