.modal_body {
  padding-top: unset;
}

.mat-stepper-horizontal {
  margin-top: 8px;
}

.mat-form-field {
  margin-top: 16px;
}

::ng-deep .mat-horizontal-stepper-header-container {
  display: none !important;
}

.mt-20 {
  margin-top: 20px;
}

/* Table */
.example-container {
  position: relative;
  min-height: 200px;
}

.example-table-container {
  position: relative;
  max-height: calc(70vh - 250px);
  overflow: auto;
}

table {
  width: 100%;

  td {
    padding: 10px;
  }
  th {
    padding: 10px;
    white-space: normal;
  }
}

.example-loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 56px;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.example-rate-limit-reached {
  color: #980000;
  max-width: 360px;
  text-align: center;
}

/* Column Widths */
.mat-column-number,
.mat-column-state {
  max-width: 64px;
}

.mat-column-created {
  max-width: 124px;
}

tr:nth-child(even) {background-color: #f2f2f2;}

/*
errors
*/

.custom-error {
  color: rgb(102, 60, 0);
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 4px;

  &--alert {
    background-color: rgb(255, 244, 229);
  }

  &--warn {
    background-color: rgb(253, 236, 234);
  }
}
