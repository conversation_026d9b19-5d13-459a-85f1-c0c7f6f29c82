<div class="mat-card-title">
  <h2>
    {{'ENTITY_SETUP.RTP_REDUCER.modalTitle' | translate: {title: entityName} }}
  </h2>
</div>

<mat-horizontal-stepper linear [@.disabled]="true">
  <mat-step [stepControl]="gamesFormGroup">
    <form [formGroup]="gamesFormGroup">
      <sw-games-form
        formControlName="gamesList"
        [sourceGames]="sourceGames"
        [submitted]="submitted">
      </sw-games-form>
      <div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
        <button mat-button color="primary" class="mr-5 mat-button-md" (click)="onNoClick()">
          {{ 'DIALOG.cancel' | translate }}
        </button>
        <button mat-flat-button color="primary" class="mat-button-md" matStepperNext
                [disabled]="!selectedGames?.length" (click)="goNext()">
          {{ 'DIALOG.next' | translate }}
        </button>
      </div>
    </form>
  </mat-step>

  <mat-step [stepControl]="reduceRtpFormGroup">
    <form [formGroup]="reduceRtpFormGroup">
      <!--<div *ngIf="!rtpGameControl?.dirty" class="custom-error custom-error&#45;&#45;alert">
        {{ 'ENTITY_SETUP.RTP_REDUCER.alertModalRtp' | translate: {title: entityName} }}
      </div>-->
      <div *ngIf="getErrorList()" class="custom-error custom-error--warn">
        {{ 'ENTITY_SETUP.RTP_REDUCER.warningRtp' | translate }}
        <br> {{ getErrorList() }}
      </div>

      <div class="width100">
        <mat-radio-group [formControl]="radioGroupControl" value='reduceBy' class="radio-group">

          <div>
            <mat-radio-button class="radio-button" value='reduceBy'></mat-radio-button>
            <mat-form-field appearance="outline" class="mr-5">
              <mat-label class="mr-10">{{ 'Reduce the RTP by' }}:</mat-label>
              <input matInput [formControl]="rtpGameByControl" type="number" min="0.01" step="0.01">
              <mat-error>
                <control-messages [control]="rtpGameByControl"></control-messages>
              </mat-error>
            </mat-form-field>
            <mat-icon>%</mat-icon>
          </div>

          <div>
            <mat-radio-button class="radio-button" value='reduceTo'></mat-radio-button>
            <mat-form-field appearance="outline" class="mr-5">
              <mat-label class="mr-10">{{ 'Set the RTP to' }}:</mat-label>
              <input matInput [formControl]="rtpGameToControl" type="number" min="0.01" step="0.01">
              <mat-error>
                <control-messages [control]="rtpGameToControl"></control-messages>
              </mat-error>
            </mat-form-field>
            <mat-icon>%</mat-icon>
          </div>

        </mat-radio-group>
      </div>

      <div class="example-container mat-elevation-z0">
        <div class="example-table-container">
          <table mat-table [dataSource]="rtpGamesConfigList" class="example-table"
                 matSort matSortActive="created" matSortDisableClear matSortDirection="desc">

            <ng-container matColumnDef="gameName">
              <th mat-header-cell *matHeaderCellDef>{{'ENTITY_SETUP.RTP_REDUCER.MODAL.gameName' | translate}}</th>
              <td mat-cell *matCellDef="let row">{{row?.gameName}}</td>
            </ng-container>

            <ng-container matColumnDef="gameCode">
              <th mat-header-cell *matHeaderCellDef>{{'ENTITY_SETUP.RTP_REDUCER.MODAL.gameCode' | translate}}</th>
              <td mat-cell *matCellDef="let row">{{row?.gameCode}}</td>
            </ng-container>

            <ng-container matColumnDef="currentRtp">
              <th mat-header-cell *matHeaderCellDef>{{'ENTITY_SETUP.RTP_REDUCER.MODAL.currentRtp' | translate}}</th>
              <td mat-cell *matCellDef="let row">
                <div *ngIf="row?.currentRtp">{{row?.currentRtp}}</div>
                <div *ngIf="row?.currentRtpMax">max: {{row?.currentRtpMax}}</div>
                <div *ngIf="row?.currentRtpMin">min: {{row?.currentRtpMin}}</div>
              </td>
            </ng-container>

            <ng-container matColumnDef="newRtp">
              <th mat-header-cell *matHeaderCellDef>{{'ENTITY_SETUP.RTP_REDUCER.MODAL.newRtp' | translate}}</th>
              <td mat-cell *matCellDef="let row">
                <div *ngIf="row?.currentRtp">{{row?.newRtpDeduction}}</div>
                <div *ngIf="row?.currentRtpMax">max: {{row?.newRtpDeductionMax}}</div>
                <div *ngIf="row?.currentRtpMin">min: {{row?.newRtpDeductionMin}}</div>
              </td>
            </ng-container>

            <ng-container matColumnDef="state">
              <th mat-header-cell *matHeaderCellDef></th>
              <td mat-cell *matCellDef="let row">
                <ng-container *ngIf="!row?.error; else error">
                  <mat-icon color="primary">done</mat-icon>
                </ng-container>
                <ng-template #error>
                  <mat-icon color="warn">not_interested</mat-icon>
                </ng-template>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </div>

      <div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center" class="mt-20">
        <button mat-button color="primary" class="mr-5 mat-button-md" matStepperPrevious (click)="goBack()">
          {{ 'DIALOG.back' | translate }}
        </button>
        <button mat-flat-button color="primary" class="mat-button-md"
                [disabled]="isValidRtpForm()" (click)="onConfirmClick()">
          {{ 'DIALOG.save' | translate }}
        </button>
      </div>
    </form>
  </mat-step>

</mat-horizontal-stepper>

