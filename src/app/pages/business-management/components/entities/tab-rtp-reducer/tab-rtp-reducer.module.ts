import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';

import { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';
import { RtpReducerService } from 'src/app/common/services/rtp-reducer.service';
import { RtpReduceModalModule } from './rtp-reduce-modal/rtp-reduce-modal.module';
import { TabRtpReducerComponent } from './tab-rtp-reducer.component';
import { TabRtpReducerRouting } from './tab-rtp-reducer.routing';


@NgModule({
  declarations: [
    TabRtpReducerComponent,
  ],
  imports: [
    CommonModule,
    TabRtpReducerRouting,
    RtpReduceModalModule,
    SwuiGridModule,
    TranslateModule,
    MatButtonModule,
    BoConfirmationModule,
    SwuiSchemaTopFilterModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  providers: [
    RtpReducerService
  ]
})
export class TabRtpReducerModule {
}
