import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { SwBrowserTitleService, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';
import { BaIfAllowedModule } from '../../../../common/directives/baIfAllowed/baIfAllowed.module';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { DomainsManagementService } from '../../../domains-management/domains-management.service';
import { EntityPagePanelModule } from '../entity-page-panel/entity-page-panel.module';
import { EntitiesRoutingModule } from './entities-routing.module';
import { EntityBreadcrumbsModule } from './entity-breadcrumbs/entity-breadcrumbs.module';

import { SetupEntityComponent } from './setup-entity.component';
import { GameProviderService } from '../../../../common/services/game-provider.service';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    EntitiesRoutingModule,
    SwuiPagePanelModule,
    MatTabsModule,
    ReactiveFormsModule,
    BaIfAllowedModule,
    ControlMessagesModule,
    EntityPagePanelModule,
    EntityBreadcrumbsModule,
  ],
  declarations: [
    SetupEntityComponent,
  ],
  exports: [],
  providers: [
    EntitySettingsService,
    DomainsManagementService,
    SwBrowserTitleService,
    GameProviderService
  ]
})
export class EntitiesModule {
}
