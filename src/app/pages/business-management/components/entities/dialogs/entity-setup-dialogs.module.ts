import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { RemoveConfirmDialogComponent } from './remove-confirm-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,

    MatDialogModule,
    MatButtonModule,
    MatIconModule,
  ],
  declarations: [
    RemoveConfirmDialogComponent,
  ],
  entryComponents: [
    RemoveConfirmDialogComponent,
  ],
  exports: [],
  providers: [
  ],
})
export class EntitySetupDialogsModule {
}
