import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { PERMISSIONS_NAMES, SwHubAuthService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { map } from 'rxjs/operators';
import { DeploymentGroupService } from '../../../../../common/services/deployment-group.service';
import { DeploymentGroup } from '../../../../../common/typings/deployment-group';

@Injectable()
export class SetupEntityDeploymentGroupsResolver implements Resolve<DeploymentGroup[]> {
  constructor(
    public swHubAuthService: SwHubAuthService,
    private service: DeploymentGroupService,
  ) {
  }

  resolve() {
    return (this.swHubAuthService.isSuperAdmin && this.swHubAuthService.allowedTo([PERMISSIONS_NAMES.DEPLOYMENT])) ?
      this.service.getDeploymentGroups()
        .pipe(
          map(( data: DeploymentGroup[] ) => data.filter(item => item.type === 'entity'))
        ) :
      of(null);
  }
}
