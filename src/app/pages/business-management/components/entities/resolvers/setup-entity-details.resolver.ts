import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { EntityService } from '../../../../../common/services/entity.service';
import { Entity } from '../../../../../common/typings';

@Injectable()
export class SetupEntityDetailsResolver implements Resolve<Entity> {

  constructor( private service: EntityService<Entity> ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    let path = route.params['path'];
    return path === ':' ? this.service.getBrief() : this.service.getItem(path);
  }
}
