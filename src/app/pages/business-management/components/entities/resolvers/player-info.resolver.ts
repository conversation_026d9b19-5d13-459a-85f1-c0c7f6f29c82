import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { PlayerService } from '../../../../../common/services/player.service';
import { Player } from '../../../../../common/typings';

@Injectable()
export class PlayerInfoResolver implements Resolve<Player> {
  constructor( private service: PlayerService ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    const { params: { path, id } } = route;
    const params = { id, path };

    return this.service.getItem(params, true);
  }
}
