import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Site } from '../../../../../common/models/site.model';
import { SiteService } from '../../../../../common/services/site.service';

@Injectable()
export class SetupEntityAvailableSitesResolver implements Resolve<Site[]> {
  constructor( private service: SiteService<Site> ) {

  }

  resolve( route: ActivatedRouteSnapshot ) {
    let path = route.params['path'];
    return this.service.getAvailableSites(path);
  }
}
