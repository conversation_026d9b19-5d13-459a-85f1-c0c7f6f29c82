import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Entity } from '../../../../../common/models/entity.model';
import { EntityService } from '../../../../../common/services/entity.service';

@Injectable()
export class SetupEntityParentResolver implements Resolve<Entity> {

  constructor( private service: EntityService<Entity> ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    let path = Entity.getParentPath(route.params['path']);
    let source$;

    if (path === ':') {
      source$ = this.service.getBrief();
    } else {
      source$ = this.service.getItem(path);
    }

    return source$;
  }
}
