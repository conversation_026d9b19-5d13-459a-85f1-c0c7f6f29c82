import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { PERMISSIONS_LIST } from '../../../../../app.constants';
import { JurisdictionService } from '../../../../../common/services/jurisdiction.service';
import { Jurisdiction } from '../../../../../common/typings/jurisdiction';

@Injectable()
export class SetupEntityJurisdictionsResolver implements Resolve<Jurisdiction[]> {

  constructor( private readonly authService: SwHubAuthService, private readonly service: JurisdictionService ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    if (this.authService.allowedTo(PERMISSIONS_LIST.JURISDICTION_VIEW)) {
      return this.service.getEntityJurisdictions(route.params['path']);
    }
    return of([]);
  }
}
