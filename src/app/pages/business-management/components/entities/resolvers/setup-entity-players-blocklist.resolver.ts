import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Entity } from '../../../../../common/models/entity.model';
import { SuspendedPlayer } from '../../../../../common/models/player.model';
import { EntityService } from '../../../../../common/services/entity.service';

@Injectable()
export class SetupEntityPlayersBlocklistResolver implements Resolve<SuspendedPlayer[]> {
  constructor( private service: EntityService<Entity> ) {

  }

  resolve( route: ActivatedRouteSnapshot ) {
    let path = route.params['path'];
    return this.service.getSuspendedPlayers(path)
      .pipe(
        map(blocklist => {
          if (blocklist) {
            return blocklist;
          }
          return null;
        }),
        catchError(() => {
          // for case when we're trying to get blocklist from reseller (which is wrong)
          return of(null);
        })
      );
  }
}
