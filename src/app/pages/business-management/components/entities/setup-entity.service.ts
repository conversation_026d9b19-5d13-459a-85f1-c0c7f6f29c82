import { Injectable } from '@angular/core';
import { ReplaySubject, Subject } from 'rxjs';
import { EntitySettingsModel } from '../../../../common/models/entity-settings.model';
import { Entity } from '../../../../common/models/entity.model';
import { GameProvider } from '../../../games-management/game-provider.model';
import { BusinessStructureService } from '../mat-business-structure/business-structure.service';


@Injectable()
export class SetupEntityService {
  entity?: Entity;
  availableProviders: GameProvider[] = [];
  settings?: EntitySettingsModel;
  settings$?: ReplaySubject<EntitySettingsModel> = new ReplaySubject<EntitySettingsModel>();
  entityNeedUpdate$ = new Subject();

  constructor(
    private bsService: BusinessStructureService,
    ) {
  }

  initSnapshot({ entity, entitySettings, availableProviders }: any): void {
    const entityObj = { ...this.bsService.getEntity(entity.id), ...entity };
    this.entity = new Entity({
      ...entityObj,
      parentId: entityObj.parentId,
      getEntityParent: () => this.bsService.getEntity(entityObj.parentId)
    });
    this.settings = entitySettings ? new EntitySettingsModel(entitySettings) : undefined;
    this.settings$.next(this.settings);
    this.availableProviders = availableProviders;
  }
}
