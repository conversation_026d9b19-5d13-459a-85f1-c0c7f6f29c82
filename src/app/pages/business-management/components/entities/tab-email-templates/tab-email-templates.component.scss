$color-border: rgba(0, 0, 0, 0.12);

.template {
  &__header {
    display: flex;
    align-items: center;
    height: 63px;
    padding: 8px 16px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border: 1px solid $color-border;
    border-bottom: none;
    background: #fff;
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
  }

  &__body {
    flex: 1;
    padding: 0 16px 3px;
    background: #fff;
    border-left: 1px solid $color-border;
    border-right: 1px solid $color-border;
  }
}

.formGroup {
  display: inline-flex;
  width: 100%;
}

.formItem {
  width: 50%;
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.saveBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  margin-bottom: 26px;
}
