import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

import { EntitySettingsModel, EntityEmailTemplates } from '../../../../../common/models/entity-settings.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Entity } from '../../../../../common/typings';
import { SetupEntityService } from '../setup-entity.service';


@Component({
  selector: 'tab-email-templates',
  styleUrls: ['./tab-email-templates.component.scss'],
  templateUrl: './tab-email-templates.component.html',
})
export class TabEmailTemplatesComponent implements OnInit {

  form: FormGroup;

  set entityTemplates(templates: EntityEmailTemplates) {
    if (!templates) return;
    this.form.get('emailTemplates').patchValue(templates);
  }

  private entity: Entity;
  private destroyed$ = new Subject<void>();

  constructor(public fb: FormBuilder,
              { settings, entity }: SetupEntityService,
              private readonly translate: TranslateService,
              private readonly notifications: SwuiNotificationsService,
              private readonly service: EntitySettingsService<EntitySettingsModel>,
  ) {
    this.initForm();
    this.entityTemplates = settings.emailTemplates;
    this.entity = entity;
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  patchTemplates() {
    this.service.patchSettings(this.form.getRawValue(), this.entity.path)
      .pipe(
        tap(() => this.notifications.success(
          this.translate.instant('ENTITY_SETUP.EMAIL_TEMPLATE.templateUpdated'))
        ),
        takeUntil(this.destroyed$)
      ).subscribe();
  }

  private initForm() {
    this.form = this.fb.group({
      emailTemplates: this.fb.group({
        changeEmail: this.initTemplate(),
        passwordRecovery: this.initTemplate(),
      })
    });
  }

  private initTemplate(): FormGroup {
    return this.fb.group({
      from: [null],
      html: [null],
      subject: [null],
      options: this.fb.group({
        resetBaseUrl: [null]
      })
    });
  }
}
