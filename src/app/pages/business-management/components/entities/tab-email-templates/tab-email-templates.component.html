<form class="formGroup" [formGroup]="form">
  <ng-container formGroupName="emailTemplates">
    <div class="formItem" formGroupName="passwordRecovery">
      <div class="template__header">
        <div class="template__title">
          Password Recovery
        </div>
      </div>
      <div class="template__body">
        <mat-form-field class="width100" appearance="outline">
          <mat-label>From</mat-label>
          <input matInput trimValue formControlName="from">
        </mat-form-field>
        <mat-form-field class="width100" appearance="outline">
          <mat-label>HTML</mat-label>
          <textarea matInput trimValue rows="5" formControlName="html"></textarea>
        </mat-form-field>
        <mat-form-field class="width100" appearance="outline">
          <mat-label>Subject</mat-label>
          <input matInput trimValue formControlName="subject">
        </mat-form-field>
        <mat-form-field class="width100" appearance="outline" formGroupName="options">
          <mat-label>Reset Base Url</mat-label>
          <input matInput trimValue formControlName="resetBaseUrl">
        </mat-form-field>
      </div>
    </div>
    <div class="formItem" formGroupName="changeEmail">
      <div class="template__header">
        <div class="template__title">
          Change Email
        </div>
      </div>
      <div class="template__body">
        <mat-form-field class="width100" appearance="outline">
          <mat-label>From</mat-label>
          <input matInput trimValue formControlName="from">
        </mat-form-field>
        <mat-form-field class="width100" appearance="outline">
          <mat-label>HTML</mat-label>
          <textarea matInput trimValue rows="5" formControlName="html"></textarea>
        </mat-form-field>
        <mat-form-field class="width100" appearance="outline">
          <mat-label>Subject</mat-label>
          <input matInput trimValue formControlName="subject">
        </mat-form-field>
      </div>
    </div>
  </ng-container>
</form>
<div class="saveBtn">
  <button mat-flat-button
          color="primary" (click)="patchTemplates()">
    {{'DIALOG.save' | translate}}
  </button>
</div>

