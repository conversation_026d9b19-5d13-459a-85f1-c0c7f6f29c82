import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiNotificationsModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { TabEmailTemplatesComponent } from './tab-email-templates.component';
import { TabEmailTemplatesRouting } from './tab-email-templates.routing';


@NgModule({
  declarations: [
    TabEmailTemplatesComponent
  ],
    imports: [
        CommonModule,
        TabEmailTemplatesRouting,
        MatFormFieldModule,
        TranslateModule,
        MatInputModule,
        ReactiveFormsModule,
        SwuiNotificationsModule,
        TrimInputValueModule
    ]
})
export class TabEmailTemplatesModule { }
