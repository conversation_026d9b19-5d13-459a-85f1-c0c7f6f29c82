<entity-page-panel [entity]="entity"></entity-page-panel>
<sw-entity-breadcrumbs></sw-entity-breadcrumbs>

<div class="p-32">
  <nav mat-tab-nav-bar class="entity-setup-nav">
    <ng-container *ngFor="let tab of tabs$ | async">
      <a mat-tab-link
         *ngIf="tab.available"
         (click)="gotoTab(tab, $event)"
         [active]="activeTab == tab.name">{{ tab.displayName | translate }}</a>
    </ng-container>
  </nav>
  <ng-container *ngIf="!(loading$ | async)">
    <router-outlet></router-outlet>
  </ng-container>
</div>
