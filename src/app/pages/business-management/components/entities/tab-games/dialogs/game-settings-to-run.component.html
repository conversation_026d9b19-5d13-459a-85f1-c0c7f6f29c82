<h2 mat-dialog-title>Run game settings</h2>

<mat-dialog-content style="min-height: 230px">
  <form [formGroup]="form" fxLayout="column">
    <mat-form-field appearance="outline">
      <mat-label>{{'BUSINESS_STRUCTURE.language' | translate}}</mat-label>
      <lib-swui-select
        formControlName="language"
        [data]="languages"
        [disableEmptyOption]="false"
        [showSearch]="true">
      </lib-swui-select>
      <mat-error>
        <lib-swui-control-messages [control]="form.get('language')"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label>{{'BUSINESS_STRUCTURE.currency' | translate}}</mat-label>
      <lib-swui-select
        formControlName="currency"
        [data]="currencies"
        [disableEmptyOption]="false"
        [showSearch]="true">
      </lib-swui-select>
      <mat-error>
        <lib-swui-control-messages [control]="form.get('currency')"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label>{{'BUSINESS_STRUCTURE.gameGroup' | translate}}</mat-label>
      <lib-swui-select
        formControlName="gameGroup"
        [data]="gameGroups"
        [disableEmptyOption]="false"
        [showSearch]="true">
      </lib-swui-select>
      <mat-error>
        <lib-swui-control-messages [control]="form.get('gameGroup')"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
    <mat-checkbox formControlName="doNotShow">
      {{ 'ALL.dontShowAgain' | translate }}
    </mat-checkbox>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button color="primary" (click)="cancel()">
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button mat-flat-button color="primary" cdkFocusInitial (click)="confirm()">
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
