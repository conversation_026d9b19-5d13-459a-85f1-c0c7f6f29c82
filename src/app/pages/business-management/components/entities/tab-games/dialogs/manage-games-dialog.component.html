<h2 mat-dialog-title>{{ 'ENTITY_SETUP.GAMES.MODALS.editGames' | translate }}</h2>

<mat-dialog-content class="content">
  <games-setup-stepper *ngIf="allowFullManagement" [small]="true">
    <mat-step [state]="stepperStates.SELECT" [label]="'ENTITY_SETUP.GAMES.MODALS.selectGames' | translate">
      <manage-games-grid [entity]="entity" [entityGames]="entityGames"
                         (selectedGamesApply)="setSetupStep($event)"
                         (applyButtonDisable)="handleApplyButtonState($event)">
      </manage-games-grid>
    </mat-step>

    <mat-step [state]="stepperStates.SETUP" [label]="'ENTITY_SETUP.GAMES.MODALS.newGamesSetup' | translate">
      <manage-games-setup [entity]="entity" [entityGames]="entityGames"
                          [addedGames]="addedGames" (applyButtonDisable)="handleApplyButtonState($event)"
                          (gamesSetupComplete)="setPreviewStep($event)">
      </manage-games-setup>
    </mat-step>

    <mat-step [state]="stepperStates.PREVIEW" [label]="'ENTITY_SETUP.GAMES.MODALS.preview' | translate">
      <manage-games-preview [gamesToAdd]="addedGames" [gamesToRemove]="removedGames"
                            (changesConfirm)="previewStepSaveChanges()"></manage-games-preview>
    </mat-step>
  </games-setup-stepper>

  <ng-container *ngIf="!allowFullManagement">
    <manage-games-grid [entity]="entity" [entityGames]="entityGames"
                       (selectedGamesApply)="saveSelectedGames($event)"
                       (applyButtonDisable)="handleApplyButtonState($event)">
    </manage-games-grid>
  </ng-container>

</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button color="primary" mat-dialog-close class="mat-button-md">
    {{ 'ENTITY_SETUP.GAMES.MODALS.btnCancel' | translate }}
  </button>

  <button mat-stroked-button color="primary" class="mat-button-md" *ngIf="allowFullManagement" (click)="goBack()"
          [disabled]="isPreviousStepDisabled()"
          [ngClass]="{disabled: isPreviousStepDisabled()}">
    {{ 'ENTITY_SETUP.GAMES.MODALS.btnPrevious' | translate }}
  </button>

  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial [disabled]="disabled" (click)="onApplyFn($event)" [ngClass]="applyClass">
    {{ getApplyLabel() | translate }}
  </button>

</mat-dialog-actions>
