.content {
  max-height: none !important;
}

.games-setup {
  table-layout: fixed;

  th {
    background-color: #fafafa;
    border-bottom: 1px solid #c1c1c1 !important;
  }

  td,
  th {
    &:nth-child(2) {
      width: calc(100% - 51px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &:first-child {
      width: 51px;
      text-align: center;
    }
  }

  .unviewed {
    font-weight: 500;
  }

  .active {
    background-color: #fafafa;
  }

  tbody {
    height: 330px;
  }

  tr {
    &:hover {
      cursor: pointer;
    }
  }

}

.table-game-info {
  td {
    &:first-child {
      width: 100px;
    }
  }
}

.flex-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  list-style: none;

  li {
    margin-right: 3px;
  }

  &--nowrap {
    flex-wrap: nowrap;
  }
}

.list-games {
  max-height: 350px;
  margin-bottom: 0;
  overflow: auto;
  white-space: nowrap;

  & > li {
    margin: 0;
    padding: 5px 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-bottom: 1px solid #ddd;
  }

  li {
    &:last-child {
      border-bottom: none;
    }
  }

}
