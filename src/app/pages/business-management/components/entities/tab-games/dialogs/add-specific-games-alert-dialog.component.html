<mat-dialog-content>
  <div>
    <div class="pb-10">{{ 'ENTITY_SETUP.GAMES.MODALS.specificGamesAlert' | translate }}</div>
    <div *ngFor="let specificGame of specificGames" class="pl-15">
      {{ '• ' + specificGame.title + ' (' + specificGame.code + ')' }}
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button color="primary" (click)="cancel()">
    {{ 'ALL.decline' | translate }}
  </button>
  <button mat-button color="primary" (click)="confirm()">
    {{ 'ALL.confirm' | translate }}
  </button>
</mat-dialog-actions>
