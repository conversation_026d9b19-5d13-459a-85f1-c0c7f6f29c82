import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Game } from '../../../../../../common/typings';

@Component({
  selector: '',
  templateUrl: './add-specific-games-alert-dialog.component.html',
})
export class AddSpecificGamesAlertDialogComponent {
  specificGames: Game[];

  constructor(
    @Inject(MAT_DIALOG_DATA) data: Game[],
    public dialogRef: MatDialogRef<AddSpecificGamesAlertDialogComponent>,
  ) {
    this.specificGames = data;
  }

  cancel() {
    this.dialogRef.close();
  }

  confirm() {
    this.dialogRef.close(true);
  }
}
