<h2 mat-dialog-title>{{'ENTITY_SETUP.GAMES.MODALS.titleConfirmation' | translate}}</h2>

<mat-dialog-content>
  <div *ngIf="data.games.length === 1"
       [innerHTML]="'ENTITY_SETUP.GAMES.MODALS.messageFailedSingle' | translate: data.games[0]">
  </div>
  <div *ngIf="data.games.length > 1">
    {{'ENTITY_SETUP.GAMES.MODALS.messageFailedMultiple' | translate}}
    <ul>
      <li *ngFor="let game of data.games"><strong>{{ game.title }}</strong>
        ({{'ALL.code' | translate}}: {{ game.code }})</li>
    </ul>
  </div>
  <div>{{'ENTITY_SETUP.GAMES.MODALS.messageConfirmRemove' | translate}}.</div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="cancelRemove()">
    {{'ENTITY_SETUP.GAMES.MODALS.cancelRemove' | translate}}
  </button>
  <button mat-button cdkFocusInitial (click)="confirmRemove()">
    {{'ENTITY_SETUP.GAMES.MODALS.confirmForceRemove' | translate}}
  </button>
</mat-dialog-actions>
