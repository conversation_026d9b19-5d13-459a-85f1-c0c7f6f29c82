import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiCheckboxModule, SwuiControlMessagesModule, SwuiGridModule, SwuiSchemaTopFilterModule, SwuiSelectModule
} from '@skywind-group/lib-swui';
import { BoLabelsGroupModule } from '../../../../../common/components/bo-labels-group/bo-labels-group.module';
import { DownloadCsvModule } from '../../../../../common/components/download-csv/download-csv.module';
import { HintsModule } from '../../../../../common/components/hints/hints.module';
import { WizardModule } from '../../../../../common/components/swWizard/wizard.module';
import { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';

import { ProxyService } from '../../../../../common/services/proxy.service';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { LanguagesResolver } from '../../../../../common/services/resolvers/languages.resolver';
import { AddSpecificGamesAlertDialogComponent } from './dialogs/add-specific-games-alert-dialog.component';
import { GameForceRemoveDialogComponent } from './dialogs/game-force-remove-dialog.component';
import { GameSettingsToRunComponent } from './dialogs/game-settings-to-run.component';
import { ManageGamesDialogComponent } from './dialogs/manage-games-dialog.component';
import { ManageJackpotsDialogComponent } from './dialogs/manage-jackpots-dialog.component';
import { EntityGamesComponent } from './entity-games.component';
import { GamesSetupStepperModule } from './games-setup-stepper/games-setup-stepper.module';
import { GeneralGamesInfoModule } from './general-games-info/general-games-info.module';
import { JpGamesInfoModule } from './jp-games-info/jp-games-info.module';
import { ManageGamesService } from './manage-games.service';
import { ManageGamesModule } from './manage-games/manage-games.module';
import { TabGamesComponent } from './tab-games.component';
import { TabGamesRoutingModule } from './tab-games.routing';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    TabGamesRoutingModule,
    BoLabelsGroupModule,
    ManageGamesModule,
    WizardModule,
    SwuiGridModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatDialogModule,
    GamesSetupStepperModule,
    FlexModule,
    HintsModule,
    MatTooltipModule,
    MatIconModule,
    SwuiGridModule,
    MatProgressSpinnerModule,
    DownloadCsvModule,
    SwuiSchemaTopFilterModule,
    ReactiveFormsModule,
    MatTabsModule,
    GeneralGamesInfoModule,
    JpGamesInfoModule,
    SwuiSelectModule,
    SwuiControlMessagesModule,
    SwuiCheckboxModule,
    MatCheckboxModule
  ],
  declarations: [
    TabGamesComponent,
    EntityGamesComponent,
    ManageGamesDialogComponent,
    GameForceRemoveDialogComponent,
    ManageJackpotsDialogComponent,
    AddSpecificGamesAlertDialogComponent,
    GameSettingsToRunComponent
  ],
  exports: [],
  providers: [
    ManageGamesService,
    ProxyService,
    GameGroupFiltersService,
    LanguagesResolver,
    CurrenciesResolver
  ],
  entryComponents: [
    ManageGamesDialogComponent,
    GameForceRemoveDialogComponent,
    ManageJackpotsDialogComponent,
    AddSpecificGamesAlertDialogComponent,
    GameSettingsToRunComponent
  ]
})
export class TabGamesModule {
}
