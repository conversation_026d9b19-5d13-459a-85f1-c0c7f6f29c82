import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { Game } from '../../../../../common/typings';

export interface RejectedGame {
  reason: string;
  game: Game;
}

@Injectable()
export class ManageGamesService {

  added$: Subject<Game> = new Subject();
  rejected$: Subject<RejectedGame> = new Subject();

  constructor() {
  }

  setGameAsAdded(game) {
    this.added$.next(game);
  }

  setGameAsRejected(game, reason) {
    this.rejected$.next({ game, reason });
  }
}
