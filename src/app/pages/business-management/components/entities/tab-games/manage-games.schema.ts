import { SchemaFilter<PERSON><PERSON><PERSON><PERSON>, SwuiGridField } from '@skywind-group/lib-swui';
import { gameStatusClassMap, providerCodeClassMap, tagClassMap } from '../../../../../app.constants';
import { DISPLAY_GAME_STATUS_LIST, GAME_STATUS_LIST, LABEL_DISPLAY_TEXT } from './general-games-info/games.schema';
import { componentGameTypes } from '../../../../games-management/games-create/games-create.component';
import { Game } from '../../../../../common/typings';


const SCHEMA: SwuiGridField[] = [
  {
    field: 'providerTitle',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.providerTitle',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: any) => row && row.providerTitle,
      classFn: ( row: any) => {
        let cssClass = providerCodeClassMap.DEFAULT;

        if (providerCodeClassMap.hasOwnProperty(row.providerCode)) {
          cssClass = providerCodeClassMap[row.providerCode];
        }

        return cssClass;
      }
    },
  },
  {
    field: 'title',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.title',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
  },
  {
    field: 'code',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.code',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
  },
  {
    field: 'type',
    title: 'ENTITY_SETUP.GAMES.type',
    type: 'multiselect',
    data: componentGameTypes,
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => row.type,
      classFn: () => 'sw-chip sw-chip-blue'
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
    filterMatch: SchemaFilterMatchEnum.In,
  },
  {
    field: 'status',
    title: 'ENTITY_SETUP.GAMES.status',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    data: GAME_STATUS_LIST,
    td: {
      type: 'status',
      statusList: GAME_STATUS_LIST,
      displayStatusList: DISPLAY_GAME_STATUS_LIST,
      classMap: gameStatusClassMap,
      readonly: true
    },
  },
  {
    field: 'labels',
    title: 'ENTITY_SETUP.GAMES.labels',
    type: 'multiselect',
    data: LABEL_DISPLAY_TEXT,
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'gameslabels',
      classMap: tagClassMap,
    },
    filterMatch: SchemaFilterMatchEnum.In,
  },
];


export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
