<lib-swui-schema-top-filter [schema]="filterSchema"></lib-swui-schema-top-filter>
<hints *ngIf="entity.status === 'test'" [showCloseBtn]="false" [fontSize]="16" message="Entity is in test mode"></hints>

<lib-swui-grid [schema]="schemaItems"
               [rowActions]="actions"
               [ignorePlainLink]="true"
               [gridId]="schemaTypeName"
               [rowActionsColumnTitle]="''"
               [savedFilteredPageName]="'general-games-info'"
               [disableRefreshAction]="true"
               [columnsManagement]="true"
               (widgetActionEmitted)="onWidgetActionFn($event)">
  <button mat-icon-button
          matTooltip="Run game settings"
          *ngIf="runAvailable"
          (click)="setRunSettings()">
    <mat-icon>settings</mat-icon>
  </button>
  <download-csv [loading]="loading" (downloadCsv)="downloadCsv()"></download-csv>
  <ng-container *ngTemplateOutlet="projectedButtons"></ng-container>
</lib-swui-grid>

<ng-template #projectedButtons>
  <button
    class="ml-10 mr-5"
    mat-flat-button
    color="primary"
    (click)="showManageGamesModal()">
    {{ 'ENTITY_SETUP.GAMES.btnManageGames' | translate }}
  </button>
</ng-template>
