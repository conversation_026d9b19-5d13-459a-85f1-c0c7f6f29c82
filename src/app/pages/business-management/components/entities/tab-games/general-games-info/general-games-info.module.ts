import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';
import { HintsModule } from '../../../../../../common/components/hints/hints.module';
import { GeneralGamesInfoComponent } from './general-games-info.component';



@NgModule({
  declarations: [
    GeneralGamesInfoComponent
  ],
  exports: [
    GeneralGamesInfoComponent
  ],
  imports: [
    CommonModule,
    SwuiSchemaTopFilterModule,
    HintsModule,
    SwuiGridModule,
    DownloadCsvModule,
    MatButtonModule,
    TranslateModule,
    MatTooltipModule,
    MatIconModule
  ]
})
export class GeneralGamesInfoModule { }
