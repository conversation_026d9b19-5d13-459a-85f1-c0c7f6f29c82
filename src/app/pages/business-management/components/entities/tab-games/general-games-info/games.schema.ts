import { Schema<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SwuiGridField } from '@skywind-group/lib-swui';
import { gameStatusClassMap, providerCodeClassMap, tagClassMap } from '../../../../../../app.constants';
import { GAME_STATUSES } from '../../../../../../common/services/game.service';
import { Game, isLiveGame } from '../../../../../../common/typings';
import { componentGameTypes } from '../../../../../games-management/games-create/games-create.component';

export const GAME_STATUS_LIST = [
  { id: 'normal', code: GAME_STATUSES.NORMAL, displayName: 'ALL.active', hidden: false },
  { id: 'suspended', code: GAME_STATUSES.SUSPENDED, displayName: 'ALL.inactive_keep', hidden: false },
  { id: 'suspended', code: GAME_STATUSES.KILL_SESSION, displayName: 'ALL.inactive_kill', hidden: false },
  { id: 'test', code: GAME_STATUSES.TEST, displayName: 'ALL.test', hidden: false },
  { id: 'hidden', code: GAME_STATUSES.HIDDEN, displayName: 'ALL.hidden', hidden: false }
];

export const DISPLAY_GAME_STATUS_LIST = [
  { id: 'normal', code: GAME_STATUSES.NORMAL, displayName: 'ALL.active', hidden: false },
  { id: 'suspended', code: GAME_STATUSES.SUSPENDED, displayName: 'ALL.inactive', hidden: false },
  { id: 'test', code: GAME_STATUSES.TEST, displayName: 'ALL.test', hidden: false },
  { id: 'hidden', code: GAME_STATUSES.HIDDEN, displayName: 'ALL.hidden', hidden: false }
];

export const SUPPORTED_FEATURES = [
  { id: 'true', code: 'true', displayName: 'Yes' },
  { id: 'false', code: 'false', displayName: 'No' },
];

export const LABEL_DISPLAY_TEXT = [
  {
    'id': 'slot',
    'text': 'Slot'
  },
  {
    'id': 'table game',
    'text': 'Table game'
  },
  {
    'id': 'roulette',
    'text': 'Roulette'
  },
  {
    'id': 'html5',
    'text': 'Html5'
  },
  {
    'id': 'flash',
    'text': 'Flash'
  },
  {
    'id': 'downloadable',
    'text': 'Downloadable'
  },
  {
    'id': 'progressive',
    'text': 'Progressive'
  },
  {
    'id': 'jackpot',
    'text': 'Jackpot'
  },
  {
    'id': 'branded',
    'text': 'Branded'
  }
];

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'title',
    title: 'ENTITY_SETUP.GAMES.title',
    type: 'string',
    td: {
      type: 'string',
      nowrap: true,
    },
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'code',
    title: 'ENTITY_SETUP.GAMES.code',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'type',
    title: 'ENTITY_SETUP.GAMES.type',
    type: 'multiselect',
    data: componentGameTypes,
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => row.type,
      classFn: () => 'sw-chip sw-chip-blue'
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
    filterMatch: SchemaFilterMatchEnum.In,
  },
  {
    field: 'providerTitle',
    title: 'ENTITY_SETUP.GAMES.providerTitle',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: any ) => row && row.providerTitle,
      classFn: ( row: any ) => {
        let cssClass = providerCodeClassMap.DEFAULT;

        if (providerCodeClassMap.hasOwnProperty(row.providerCode)) {
          cssClass = providerCodeClassMap[row.providerCode];
        }

        return cssClass;
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'providerId',
    title: 'ENTITY_SETUP.GAMES.providerTitle',
    type: 'select',
    data: [],
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true
  },
  {
    field: 'aamsCode',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.aamsCode',
    type: 'string',
    td: {
      type: 'calc',
      titleFn: (row: any) => row?.settings?.aamsCode || '-'
    },
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'mustWinJackpotBundled',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.mustWinJackpotBundled',
    type: 'select',
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => {
        return SUPPORTED_FEATURES.find(type => type.code === (row?.settings?.mustWinJackpotBundled || false).toString()).displayName;
      },
      classFn: ( row: Game ) => {
        const result = SUPPORTED_FEATURES.find(type => type.code === (row?.settings?.mustWinJackpotBundled || false).toString()).code;
        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';
      }
    },
    data: SUPPORTED_FEATURES,
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'labels',
    title: 'ENTITY_SETUP.GAMES.labels',
    type: 'multiselect',
    data: LABEL_DISPLAY_TEXT,
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'gameslabels',
      classMap: tagClassMap,
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'labelsId',
    title: 'ENTITY_SETUP.GAMES.labels',
    type: 'multiselect',
    data: [],
    isList: false,
    isViewable: false,
    isSortable: true,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.In,
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'limitFiltersWillBeApplied',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.limitFiltersSupported',
    type: 'select',
    data: SUPPORTED_FEATURES,
    emptyOptionPlaceholder: 'ALL.all',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => {
        return SUPPORTED_FEATURES.find(type => type.code === row?.limitFiltersWillBeApplied.toString()).displayName;
      },
      classFn: ( row: Game ) => {
        const result = SUPPORTED_FEATURES.find(type => type.code === row?.limitFiltersWillBeApplied.toString()).code;
        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'status',
    title: 'ENTITY_SETUP.GAMES.status',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    data: DISPLAY_GAME_STATUS_LIST,
    td: {
      type: 'status',
      statusList: GAME_STATUS_LIST,
      displayStatusList: DISPLAY_GAME_STATUS_LIST,
      classMap: gameStatusClassMap,
      readonlyFn: ( row: Game ) => {
        const { changeStateDisabled, changeStateEnabled, changeStateLiveDisabled, changeStateLiveEnabled, changeState, changeStateLive }
          = row._meta;

        let changeGameState: boolean;
        if (isLiveGame(row)) {
          if (row.status === GAME_STATUSES.NORMAL) {
            changeGameState = changeStateLiveDisabled;
          } else if (row.status === GAME_STATUSES.SUSPENDED) {
            changeGameState = changeStateLiveEnabled;
          } else {
            changeGameState = changeStateLive;
          }
        } else {
          if (row.status === GAME_STATUSES.NORMAL) {
            changeGameState = changeStateDisabled;
          } else if (row.status === GAME_STATUSES.SUSPENDED) {
            changeGameState = changeStateEnabled;
          } else {
            changeGameState = changeState;
          }
        }

        // will be improved
        /*if (isSuperAdmin) {
          return false;
        }

        if ([GAME_STATUSES.TEST, GAME_STATUSES.HIDDEN].includes(row.status)) {
          return true;
        }*/

        return !changeGameState;
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'isFreebetSupported',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isFreebetSupported',
    type: 'select',
    data: SUPPORTED_FEATURES,
    emptyOptionPlaceholder: 'ALL.all',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => {
        const { isFreebetSupported } = row?.features;
        if (isFreebetSupported !== undefined) {
          return SUPPORTED_FEATURES.find(features => {
            return features.code === row?.features?.isFreebetSupported.toString();
          }).displayName;
        }
        return '-';
      },
      classFn: ( row: Game ) => {
        let result;
        const { isFreebetSupported } = row?.features;
        if (isFreebetSupported !== undefined) {
          result = SUPPORTED_FEATURES.find(features => {
            return features.code === row?.features?.isFreebetSupported.toString();
          }).code;
        } else {
          result = 'false';
        }
        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'isMarketplaceSupported',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isMarketplaceSupported',
    type: 'select',
    data: SUPPORTED_FEATURES,
    emptyOptionPlaceholder: 'ALL.all',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => {
        const { isMarketplaceSupported } = row?.features;
        if (isMarketplaceSupported !== undefined) {
          return SUPPORTED_FEATURES.find(features => {
            return features.code === row?.features?.isMarketplaceSupported.toString();
          }).displayName;
        }
        return '-';
      },
      classFn: ( row: Game ) => {
        let result;
        const { isMarketplaceSupported } = row?.features;
        if (isMarketplaceSupported !== undefined) {
          result = SUPPORTED_FEATURES.find(features => {
            return features.code === row?.features?.isMarketplaceSupported.toString();
          }).code;
        } else {
          result = 'false';
        }
        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'isCustomLimitsSupported',
    title: 'ENTITY_SETUP.GAMES.MANAGE_GRID.isCustomLimitsSupported',
    type: 'select',
    data: SUPPORTED_FEATURES,
    emptyOptionPlaceholder: 'ALL.all',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: Game ) => {
        const { isCustomLimitsSupported } = row?.features;
        if (isCustomLimitsSupported !== undefined) {
          return SUPPORTED_FEATURES.find(features => {
            return features.code === row?.features?.isCustomLimitsSupported.toString();
          }).displayName;
        }
        return '-';
      },
      classFn: ( row: Game ) => {
        let result;
        const { isCustomLimitsSupported } = row?.features;
        if (isCustomLimitsSupported !== undefined) {
          result = SUPPORTED_FEATURES.find(features => {
            return features.code === row?.features?.isCustomLimitsSupported.toString();
          }).code;
        } else {
          result = 'false';
        }
        return result === 'true' ? 'sw-chip sw-chip-blue' : 'sw-chip';
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
];


export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
