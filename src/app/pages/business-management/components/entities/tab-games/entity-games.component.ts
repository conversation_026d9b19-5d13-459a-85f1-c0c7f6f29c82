import { Component, Input } from '@angular/core';
import { SwHubAuthService, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import { Entity } from '../../../../../common/models/entity.model';
import { GameService } from '../../../../../common/services/game.service';
import { GamesRefreshService } from './games-refresh.service';
import { PERMISSIONS_LIST } from '../../../../../app.constants';

@Component({
  selector: 'entity-games',
  templateUrl: './entity-games.component.html',
  providers: [
    GamesRefreshService,
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: GameService },
  ]
})
export class EntityGamesComponent {

  /**
   * Component option which allows user to manage setings such as royalties, etc...
   * @type {boolean}
   */
  @Input() allowFullManagement: boolean;

  @Input()
  set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
  }

  get entity(): Entity {
    return this._entity;
  }

  public readonly allowedEntityGameView: boolean;

  private _entity: Entity;

  constructor( authService: SwHubAuthService ) {
    this.allowedEntityGameView = authService.allowedTo([
      ...PERMISSIONS_LIST.GAME_GROUP_VIEW,
      ...PERMISSIONS_LIST.ENTITY_GAME_VIEW
    ]);
  }
}
