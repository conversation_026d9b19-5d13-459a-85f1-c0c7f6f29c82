import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatStepperModule } from '@angular/material/stepper';
import { GamesSetupStepComponent } from './games-setup-step.component';

import { GamesSetupStepperComponent } from './games-setup-stepper.component';

@NgModule({
  imports: [
    CommonModule,
    MatStepperModule
  ],
  exports: [
    GamesSetupStepperComponent,
    GamesSetupStepComponent,
    MatStepperModule,
  ],
  declarations: [
    GamesSetupStepperComponent,
    GamesSetupStepComponent,
  ],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false }
    },
  ],
})
export class GamesSetupStepperModule {
}
