import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatStep } from '@angular/material/stepper';

@Component({
  selector: 'games-setup-step',
  templateUrl: 'games-setup-step.component.html',
  providers: [{provide: ErrorStateMatcher, useExisting: GamesSetupStepComponent}],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class GamesSetupStepComponent extends MatStep {
}
