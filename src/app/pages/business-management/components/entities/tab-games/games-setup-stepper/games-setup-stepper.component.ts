import { CdkStepper } from '@angular/cdk/stepper';
import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { MatStepper } from '@angular/material/stepper';

@Component({
  selector: 'games-setup-stepper',
  templateUrl: 'games-setup-stepper.component.html',
  styleUrls: [
    './games-setup-stepper.component.scss',
  ],
  providers: [
    {provide: MatStepper, useExisting: GamesSetupStepperComponent},
    {provide: CdkStepper, useExisting: GamesSetupStepperComponent}
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class GamesSetupStepperComponent extends MatStepper {
  @Input() small = false;

}
