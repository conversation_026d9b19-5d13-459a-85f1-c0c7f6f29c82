$mat-horizontal-stepper-header-height: 72px !default;
$mat-horizontal-stepper-header-height-small: 54px !default;

.mat-stepper-vertical,
.mat-stepper-horizontal {
  display: block;
}

.mat-horizontal-stepper-header-container {
  white-space: nowrap;
  display: flex;
  align-items: center;

  .mat-stepper-label-position-bottom & {
    align-items: flex-start;
  }
}

.mat-stepper-horizontal-line {
  border-top-width: 1px;
  border-top-style: solid;
  flex: auto;
  height: 0;
  margin: 0 -16px;
  min-width: 32px;

  .mat-stepper-label-position-bottom & {
    margin: 0;
    min-width: 0;
    position: relative;
    top: 36px;
  }
}

%mat-header-horizontal-line-label-position-bottom {
  border-top-width: 1px;
  border-top-style: solid;
  content: '';
  display: inline-block;
  height: 0;
  position: absolute;
  top: 36px;
  width: calc(50% - 20px);
}

.mat-horizontal-stepper-header {
  display: flex;
  height: $mat-horizontal-stepper-header-height;
  overflow: hidden;
  align-items: center;
  padding: 0 24px;

  .mat-step-icon {
    margin-right: 8px;
    flex: none;

    [dir='rtl'] & {
      margin-right: 0;
      margin-left: 8px;
    }
  }

  &_small {
    height: $mat-horizontal-stepper-header-height-small;
  }

  .mat-stepper-label-position-bottom & {
    box-sizing: border-box;
    flex-direction: column;
    // We use auto instead of fixed 104px (by spec) because when there is an optional step
    //  the height is greater than that
    height: auto;
    padding: 24px;

    &:not(:last-child)::after,
    [dir='rtl'] &:not(:first-child)::after {
      @extend %mat-header-horizontal-line-label-position-bottom;
      right: 0;
    }

    &:not(:first-child)::before,
    [dir='rtl'] &:not(:last-child)::before {
      @extend %mat-header-horizontal-line-label-position-bottom;
      left: 0;
    }

    [dir='rtl'] &:last-child::before,
    [dir='rtl'] &:first-child::after {
      display: none;
    }

    & .mat-step-icon {
      // Cleans margin both for ltr and rtl direction
      margin-right: 0;
      margin-left: 0;
    }

    & .mat-step-label {
      padding: 16px 0 0 0;
      text-align: center;
      width: 100%;
    }
  }
}

.mat-vertical-stepper-header {
  display: flex;
  align-items: center;
  padding: 24px;

  // We can't use `max-height` here, because it breaks the flexbox centering in IE.
  height: 24px;

  .mat-step-icon {
    margin-right: 12px;

    [dir='rtl'] & {
      margin-right: 0;
      margin-left: 12px;
    }
  }
}

.mat-horizontal-stepper-content {
  outline: 0;

  &[aria-expanded='false'] {
    height: 0;
    overflow: hidden;
  }
}

.mat-horizontal-content-container {
  overflow: hidden;
  // padding: 0 $mat-stepper-side-gap $mat-stepper-side-gap $mat-stepper-side-gap;
  padding: 0;
}

.mat-vertical-content-container {
  margin-left: 36px;
  border: 0;
  position: relative;

  [dir='rtl'] & {
    margin-left: 0;
    margin-right: 36px;
  }
}

.mat-stepper-vertical-line::before {
  content: '';
  position: absolute;
  top: -16px;
  bottom: -16px;
  left: 0;
  border-left-width: 1px;
  border-left-style: solid;

  [dir='rtl'] & {
    left: auto;
    right: 0;
  }
}

.mat-vertical-stepper-content {
  overflow: hidden;
  outline: 0;
}

.mat-vertical-content {
  padding: 0 24px 24px 24px;
}

.mat-step:last-child {
  .mat-vertical-content-container {
    border: none;
  }
}


mat-step-header:hover {
  background: none !important;
  cursor: default !important;
}
