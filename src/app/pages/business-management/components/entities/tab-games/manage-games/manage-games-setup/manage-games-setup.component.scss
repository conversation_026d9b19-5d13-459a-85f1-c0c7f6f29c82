.games-setup {
  table-layout: fixed;
  th {
    background-color: #fafafa;
    border-bottom: 1px solid #c1c1c1 !important;
  }
  td,
  th {
    &:nth-child(2) {
      width: calc(100% - 51px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    &:first-child {
      width: 51px;
      text-align: center;
    }
  }
  .unviewed {
    font-weight: 500;
  }
  .active {
    background-color: #fafafa;
  }
  tbody {
    height: 330px;
  }
  tr {
    &:hover {
      cursor: pointer
    }
  }


}
