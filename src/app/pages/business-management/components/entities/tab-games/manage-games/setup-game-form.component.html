<div class="panel" [formGroup]="form">
  <div class="panel-body no-padding-bottom">
    <div class="form-group">
      <label class="col-lg-3 control-label">{{'ENTITY_SETUP.GAMES.MODALS.selectStatus' | translate}}:</label>
      <div class="col-lg-9">
        <select class="select form-control" formControlName="status">
          <option value="" disabled selected hidden>{{'ALL.pleaseSelect' | translate}}</option>
          <option value="test">{{'ALL.test' | translate}}</option>
          <option value="normal">{{'ALL.active' | translate}}</option>
          <option value="suspended">{{'ALL.inactive' | translate}}</option>
        </select>
        <control-messages class="validation-error-label" [control]="form.get('status')"
                          [forceShow]="submitted"></control-messages>
      </div>
    </div>
    <div class="form-group">
      <label class="col-lg-3 control-label">{{'ALL.settings' | translate}}:</label>
      <div class="col-lg-9">
        <textarea style="max-width: 100%; min-width: 100%" formControlName="settings" class="form-control"></textarea>
        <control-messages class="validation-error-label" [control]="form.get('settings')"
                          [forceShow]="submitted"></control-messages>
      </div>
    </div>
    <div class="form-group">
      <label class="col-lg-3 control-label">
        {{ 'ENTITY_SETUP.GAMES.MODALS.externalGameId' | translate }}:
      </label>
      <div class="col-lg-9">
        <input class="form-control"
               trimValue
               type="text"
               formControlName="externalGameId"/>
        <control-messages class="validation-error-label"
                          [control]="externalGameIdControl"
                          [forceShow]="submitted">
        </control-messages>
      </div>
    </div>
    <div *ngIf="isItalianRegulation" class="form-group">
      <label class="col-lg-3 control-label">{{'ENTITY_SETUP.GAMES.MODALS.aamsCode' | translate}}:</label>
      <div class="col-lg-9">
        <input class="form-control" type="number" min="0" [formControl]="aamsCode"/>
        <control-messages class="validation-error-label" [control]="aamsCode"
                          [forceShow]="submitted"></control-messages>
      </div>
    </div>
    <div *ngIf="isItalianRegulation" class="form-group">
      <label class="col-lg-3 control-label">{{'ENTITY_SETUP.GAMES.MODALS.mustWinJackpotBundled' | translate}}:</label>
      <div class="col-lg-1" style="display: flex; align-items: center; height: 30px">
        <input type="checkbox" [formControl]="mustWinJackpotBundled"/>
      </div>
    </div>
  </div>
  <div class="panel-footer text-right">
    <button class="btn bg-slate-400 mr-20" (click)="submitChanges($event)">{{'DIALOG.save' | translate}}</button>
  </div>
</div>
