import { Game } from '../../../../../../common/typings';

export class SetupGameChanges {
  status: string;
  settings?: Object;
  externalGameId?: string;

  constructor( data: SetupGameChanges ) {
    this.status = data.status;
    this.externalGameId = data.externalGameId;

    if ('settings' in data) {
      this.settings = data.settings;
    }
  }
}

export class SetupGameItem {
  public game: Game;
  public unviewed: boolean = true;
  public complete: boolean = false;
  public required: boolean = false;

  private changes: SetupGameChanges;

  constructor( game: Game ) {
    this.game = game;
  }

  get title() {
    return this.game.title;
  }

  setChanges( data ) {
    this.complete = true;
    if (this.required) {
      this.required = false;
    }
    this.changes = new SetupGameChanges(data);
  }

  getChanges(): SetupGameChanges {
    let result: SetupGameChanges = {
      status: this.getChangedField('status'),
      externalGameId: this.getChangedField('externalGameId'),
    };

    let settings = this.getChangedField('settings');
    if (!!settings && typeof settings === 'object' && Object.keys(settings).length > 0) {
      Object.assign(result, { settings });
    }

    return result;
  }

  getGameWithChanges(): Game {
    return Object.assign({}, this.game, this.getChanges()) as Game;
  }

  private getChangedField( field: string ) {
    return this.changes && field in this.changes ? this.changes[field] : this.game[field];
  }
}
