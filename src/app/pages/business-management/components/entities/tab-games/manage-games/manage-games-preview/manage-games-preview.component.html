<div class="row">
  <div class="col-sm-6">
    <div class="panel panel-default">
      <div class="panel-heading pt-10 pb-10">
        <h6 class="text-semibold no-margin">Games to add</h6>
      </div>
      <div class="panel-body no-padding">
        <ul class="list list-unstyled list-games" *ngIf="gamesToAdd">
          <li *ngFor="let game of gamesToAdd" [ngClass]="getGameItemClass(game)">
            <ul class="list-inline pull-right">
              <li>
                <i class="icon-gear position-left mr-5" *ngIf="game?.settings"
                   title="{{ getReadableSetitngs(game.settings) }}" style="cursor: help"></i>
              </li>
              <li>
                <span class="label" [ngClass]="getStatusClass(game.status)">{{ getStatusName(game.status) }}</span>
              </li>
            </ul>
            <span>{{ game.title }} ({{ game.code }})</span>
          </li>
        </ul>
        <ul class="list list-unstyled list-games" *ngIf="!gamesToAdd || gamesToAdd.length === 0">
          <li>
            <span class="label label-info ml-10">No games</span>
          </li>
        </ul>
      </div>
      <div class="panel-footer">
        <span class="ml-20">
          <span class="text-muted">Total:</span>
          <span class="text-semibold ml-5">{{ gamesToAdd ? gamesToAdd.length : 0 }}</span>
        </span>
      </div>
    </div>
  </div>
  <div class="col-sm-6">
    <div class="panel panel-default">
      <div class="panel-heading pt-10 pb-10">
        <h6 class="text-semibold no-margin">Games to remove</h6>
      </div>
      <div class="panel-body no-padding">
        <ul class="list list-unstyled list-games" *ngIf="gamesToRemove">
          <li class="no-margin" *ngFor="let game of gamesToRemove">
            <span>{{ game.title }} ({{ game.code }})</span>
          </li>
        </ul>
        <ul class="list list-unstyled list-games" *ngIf="!gamesToRemove || gamesToRemove.length === 0">
          <li class="no-margin">
            <span class="label label-info ml-10">No games</span>
          </li>
        </ul>
      </div>
      <div class="panel-footer">
        <span class="ml-20">
          <span class="text-muted">Total:</span>
          <span class="text-semibold ml-5">{{ gamesToRemove ? gamesToRemove.length : 0 }}</span>
        </span>
      </div>
    </div>
  </div>
</div>
