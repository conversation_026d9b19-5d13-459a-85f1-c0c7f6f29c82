<div class="row">
  <div class="col-sm-6 col-md-4">
    <div class="content-group">
      <table class="table table-xxs table-fixed-header games-setup">
        <thead>
        <tr>
          <th></th>
          <th>{{'ENTITY_SETUP.GAMES.MODALS.gameName' | translate}}</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of setupGameItems" [ngClass]="{'unviewed':item.unviewed,'active':isItemSelected(item)}"
            (click)="selectItem(item)">
          <td>
            <i class="icon-warning text-warning" *ngIf="item.required"></i>
            <i class="icon-checkmark3 text-success"
               *ngIf="item.complete" title="{{'ENTITY_SETUP.GAMES.MODALS.gameReady' | translate}}"></i>
          </td>
          <td>{{ item.title }}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="col-sm-6 col-md-8">
    <div class="form-horizontal">
      <setup-game-info [game]="selectedItem?.game"></setup-game-info>
      <setup-game-form [game]="selectedItem?.getGameWithChanges()"
                       [entity]="entity"
                       (gameSubmit)="onGameSubmit($event)">
      </setup-game-form>
    </div>
  </div>
</div>
<setup-game-progress [setupGameItems]="setupGameItems" (progressFinished)="onProgressFinished()"></setup-game-progress>
