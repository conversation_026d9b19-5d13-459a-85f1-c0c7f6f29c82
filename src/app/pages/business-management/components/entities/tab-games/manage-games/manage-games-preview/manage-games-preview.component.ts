import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';
import { Game } from '../../../../../../../common/typings';
import { ManageGamesService, RejectedGame } from '../../manage-games.service';

@Component({
  selector: 'manage-games-preview',
  templateUrl: './manage-games-preview.component.html',
  styleUrls: ['./manage-games-preview.component.scss'],
})
export class ManageGamesPreviewComponent implements OnInit, OnDestroy {

  @Input() gamesToAdd: Game[];
  @Input() gamesToRemove: Game[];

  @Output() changesConfirm: EventEmitter<boolean> = new EventEmitter();

  subscriptions: Subscription[] = [];
  addedGames: string[] = [];
  rejectedGames: { [gamecode: string]: string } = {};

  private statusMap = {
    test: { displayName: 'Test', cssClass: 'bg-default' },
    normal: { displayName: 'Active', cssClass: 'bg-success' },
    suspended: { displayName: 'Inactive', cssClass: 'bg-warning' }
  };

  constructor(
    private manageGamesService: ManageGamesService,
  ) {

  }

  ngOnInit() {
    this.subscribeToManageGamesEvents();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  getStatusClass( statusCode: string ): string {
    return this.statusMap.hasOwnProperty(statusCode) ? this.statusMap[statusCode].cssClass : 'bg-grey-300';
  }

  getStatusName( statusCode: string ) {
    let statusName = 'Unknown';
    if (statusCode !== '' && this.statusMap.hasOwnProperty(statusCode)) {
      statusName = this.statusMap[statusCode].displayName;
    }
    return statusName;
  }

  getReadableSetitngs( settings: Object ): string {
    return JSON.stringify(settings, null, 2);
  }

  confirmAllChanges( event ) {
    event.preventDefault();
    this.changesConfirm.emit(true);
  }

  getGameItemClass( game ) {
    const added: boolean = this.addedGames.indexOf(game.code) > -1;
    const rejected: boolean = game.code in this.rejectedGames;
    const classes = {
      'bg-success-300': added,
      'text-success-800': added,
      'bg-danger-300': rejected,
      'text-white': rejected,
    };

    return classes;
  }

  private subscribeToManageGamesEvents() {
    const addedSub = this.manageGamesService.added$.subscribe(( game ) => {
      this.addedGames.push(game.code);
    });

    const rejectedSub = this.manageGamesService.rejected$.subscribe(( rejected: RejectedGame ) => {
      this.rejectedGames[rejected.game.code] = rejected.reason;
    });

    this.subscriptions.push(addedSub, rejectedSub);
  }
}
