<div class="panel" *ngIf="game">
  <div class="panel-heading no-padding-left no-padding-right">
    <table class="table table-xxs table-borderless table-game-info">
      <tbody>
      <tr>
        <td class="text-semibold">{{'ENTITY_SETUP.GAMES.title' | translate}}:</td>
        <td>{{ game.title }}</td>
      </tr>
      <tr>
        <td class="text-semibold">{{'ENTITY_SETUP.GAMES.code' | translate}}:</td>
        <td>{{ game.code }}</td>
      </tr>
      <tr>
        <td class="text-semibold">{{'ENTITY_SETUP.GAMES.providerTitle' | translate}}:</td>
        <td>
          <span [ngClass]="getProviderClass(game.providerCode)">{{ game.providerTitle }}</span>
        </td>
      </tr>
      <tr>
        <td class="text-semibold">{{'ENTITY_SETUP.GAMES.labels' | translate}}</td>
        <td>
          <span class="label label-striped label-xs mr-5" *ngFor="let label of game.labels"
                [ngClass]="getLabelClass(label)">{{ label.title }}</span>
          <span *ngIf="!game.labels.length" class="label label-info">
            {{'ENTITY_SETUP.GAMES.MODALS.noLabels' | translate}}</span>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>
