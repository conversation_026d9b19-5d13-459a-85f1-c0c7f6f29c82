import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import {
  BulkAction, PERMISSIONS_NAMES, SelectionTransformer, SwHubAuthService, SWUI_GRID_SELECTION_TRANSFORMER_TOKEN, SwuiGridComponent,
  SwuiGridField, SwuiSelectOption
} from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, ReplaySubject, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, take, takeUntil } from 'rxjs/operators';
import { Entity } from '../../../../../../../common/models/entity.model';
import { GameService } from '../../../../../../../common/services/game.service';
import { Game, isLiveGame, Label } from '../../../../../../../common/typings';

import { SCHEMA_LIST } from '../../manage-games.schema';

const selectionTransformer: SelectionTransformer = {
  transform: ( game: Game ) => game.code
};

@Component({
  selector: 'manage-games-grid',
  templateUrl: './manage-games-grid.component.html',
  styleUrls: ['./manage-games-grid.component.scss'],
  providers: [
    GameService,
    {
      provide: SWUI_GRID_SELECTION_TRANSFORMER_TOKEN,
      useValue: selectionTransformer
    }
  ],
})
export class ManageGamesGridComponent implements OnInit, OnDestroy {

  @Input() entity: Entity;

  labelsFilterControl = new FormControl();
  items: Game[] = [];
  schema = SCHEMA_LIST;
  schemaItems: SwuiGridField[];

  loading: boolean = true;
  games$ = new ReplaySubject<Game[]>(1);
  limit: number = 10;
  availableGames: Game[] = [];
  availableLabels: SwuiSelectOption[] = [];
  terms: string = '';
  selected: string[] = [];

  bulkActions: BulkAction[] = [
    new BulkAction({ title: 'Select Games' })
  ];

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Game[], string>;

  @Output() applyButtonDisable: EventEmitter<boolean> = new EventEmitter();
  @Output() selectedGamesApply: EventEmitter<any> = new EventEmitter();

  private _entityGames: Game[] = []; // @TODO: to remove
  private _entityGameCodes: string[] = [];
  private allSelected: string[] = [];

  private _applyButtonDisabled: boolean = true;

  private searchStream = new BehaviorSubject<string>('');
  private selectedLabelsStream = new BehaviorSubject<string[]>([]);
  private readonly destroyed$ = new Subject<void>();

  @Input()
  set entityGames( values: Game[] ) {
    if (!values) return;

    let res = values?.filter(game => isLiveGame(game)
      || !isLiveGame(game) && this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]));

    if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])) {
      res = res.filter(game => !isLiveGame(game));
    }

    this._entityGames = res;

    this.selected = res.map(game => game.code);
    this.allSelected = values.map(game => game.code);
    this._entityGameCodes = [...this.selected]; // need to compare codes after manipulation
  }

  constructor( public service: GameService,
               private readonly swHubAuthService: SwHubAuthService,
  ) {
    this.service.getGameLabels()
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(data => {
        this.availableLabels = data.map(( label: Label ) => {
          const { id, title } = label;
          return {
            id,
            text: title
          };
        });
      });
  }

  ngOnInit() {
    this.setupGrid();
    this.initStreams();

    this.labelsFilterControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( val: string[] ) => {
        this.selectedLabelsStream.next(val);
      });
  }

  ngOnDestroy() {
    this.availableGames = [];
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  setupGrid() {
    this.schema = SCHEMA_LIST;
    this.schemaItems = this.schema.filter(item => item.isList);
  }

  confirmGamesSelection( event: Event ) {
    event.preventDefault();
    const hash: { [code: string]: Game } = this.availableGames.reduce(( result, item ) => {
      result[item.code] = item;
      return result;
    }, {});

    let addedGames: Game[] = this.grid.selection.selected
      .filter(code => this._entityGameCodes.indexOf(code) === -1)
      .map(( code ) => hash[code]);
    let removedGames: Game[] = this._entityGames
      .filter(game => !this.grid.selection.isSelected(game.code));

    this.selectedGamesApply.emit({ addedGames, removedGames });
  }

  onFilterChanged( value: string ) {
    this.searchStream.next(value);
  }

  selectAllItems() {
    this.games$
      .pipe(take(1))
      .subscribe(games => {
        const codes: string[] = games.map(game => game.code);
        this.grid.selection.select(...codes);
        this.enableApplyButton();
      });
  }

  private enableApplyButton() {
    if (this._applyButtonDisabled) {
      this._applyButtonDisabled = false;
      this.applyButtonDisable.emit(this._applyButtonDisabled);
    }
  }

  private initStreams() {
    const path = this.entity.entityParent.path || '';
    this.service.getAllGames(path, false, true)
      .pipe(
        map(items => {
          let res = items?.filter(game => isLiveGame(game)
            || !isLiveGame(game) && this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE]));

          if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_ADD])) {
            res = res.filter(game => !isLiveGame(game) || this.allSelected.includes(game.code));
          }

          if (!this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE])) {
            res = res.filter(game => !isLiveGame(game) || !this.allSelected.includes(game.code));
          }

          return res;
        })
      )
      .subscribe(games => {
        this.games$.next(games);
      });

    return combineLatest([
      this.searchStream.pipe(debounceTime(500), distinctUntilChanged()),
      this.selectedLabelsStream,
      this.games$
    ]).pipe(map(( [search, selectedLabels, items] ) => {
      this.availableGames = items;
        return items.filter(( item ) => {
          const { title, code, labels: itemLabels } = item;
          const foundByCodeOrTitle = title.toLowerCase().indexOf(search.toLowerCase()) !== -1
            || code.toLowerCase().indexOf(search.toLowerCase()) !== -1;
          const foundByLabel = !selectedLabels.length
            ? true : itemLabels.some(label => selectedLabels.indexOf(label.id) !== -1);

          return foundByLabel && foundByCodeOrTitle;
        });
      }),
    ).subscribe(res => {
      this.items = res;
      this.loading = false;
    });
  }
}
