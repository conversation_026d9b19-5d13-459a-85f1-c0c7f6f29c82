<ng-template #gridTopMenu>
  <button
    mat-flat-button
    color="primary"
    (click)="selectAllItems()"
    [disabled]="loading"
    class="mr-10"
    [ngClass]="{disabled:loading}">
    {{'ENTITY_SETUP.GAMES.MODALS.selectAll' | translate}}<span *ngIf="!loading" class="ml-5">({{(games$ | async)?.length}})</span>
  </button>

  <input
    matInput trimValue
    type="search"
    class="mr-10 search"
    placeholder="{{ 'ENTITY_SETUP.GAMES.searchPlaceholder' | translate }}"
    [disabled]="loading"
    (keyup)="onFilterChanged($event.target['value'])"
    [value]="terms">

  <lib-swui-multiselect
    style="display: flex;"
    [title]="'Labels'"
    [placeholder]="'Labels filter'"
    [showSearch]="true"
    [data]="availableLabels"
    [formControl]="labelsFilterControl">
  </lib-swui-multiselect>

</ng-template>

<div class="sw-grid-short-view">
  <span>{{this.grid?.loading$| async}}</span>
  <lib-swui-grid
    #grid
    gridId="manage-games-dialog"
    [loading]="loading"
    [schema]="schemaItems"
    [data]="items"
    [selected]="selected"
    [ignoreQueryParams]="true"
    [pageSize]="10"
    [columnsManagement]="false"
    [bulkActions]="bulkActions"
    [bulkSelectionOnly]="true"
    [disableRefreshAction]="true">
    <ng-container *ngTemplateOutlet="gridTopMenu"></ng-container>
  </lib-swui-grid>
</div>

<div *ngIf="!loading" style="padding: 15px 0;">
  <span class="count">{{ 'ALL.available' | translate: {numberOfItems: (games$ | async)?.length} }}</span> |
  <span class="count">{{ 'ALL.found' | translate: {numberOfItems: (grid.dataSource.total$ | async) || 0} }}</span> |
  <span class="count">{{ 'ALL.selected' | translate: {numberOfItems: grid.selection.selected.length} }}</span>
</div>
