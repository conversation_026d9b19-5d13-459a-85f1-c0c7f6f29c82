import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiMultiselectModule } from '@skywind-group/lib-swui';

import { BoLabelsGroupModule } from '../../../../../../common/components/bo-labels-group/bo-labels-group.module';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';
import { TouchspinModule } from '../../../../../../common/components/touchspin/touchspin.module';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { ManageGamesGridComponent } from './manage-games-grid/manage-games-grid.component';
import { ManageGamesPreviewComponent } from './manage-games-preview/manage-games-preview.component';
import { ManageGamesSetupComponent } from './manage-games-setup/manage-games-setup.component';
import { SetupGameFormComponent } from './setup-game-form.component';
import { SetupGameInfoComponent } from './setup-game-info/setup-game-info.component';
import { SetupGameProgressComponent } from './setup-game-progress.component';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule,
        BoLabelsGroupModule,
        TouchspinModule,
        ControlMessagesModule,
        SwuiGridModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        SwuiMultiselectModule,
        TrimInputValueModule,
    ],
  exports: [
    ManageGamesGridComponent,
    ManageGamesPreviewComponent,
    ManageGamesSetupComponent,
  ],
  declarations: [
    ManageGamesGridComponent,
    ManageGamesPreviewComponent,
    ManageGamesSetupComponent,
    SetupGameInfoComponent,
    SetupGameProgressComponent,
    SetupGameFormComponent,
  ],
  providers: [],
})
export class ManageGamesModule {
}
