import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { filter } from 'rxjs/operators';

type TabName = 'general' | 'jp-info';

@Injectable()
export class GamesRefreshService {
  private _refresh = new Subject<TabName>();

  refresh( tab: TabName ): void {
    this._refresh.next(tab);
  }

  listen( tab: TabName ): Observable<TabName> {
    return this._refresh.pipe(filter(tabName => tab === tabName));
  }
}
