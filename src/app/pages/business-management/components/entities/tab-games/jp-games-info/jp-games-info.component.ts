import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  PERMISSIONS_LIST,
  PERMISSIONS_NAMES,
  SwHubAuthService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Subject, throwError } from 'rxjs';
import { catchError, switchMap, take, takeUntil } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { GameService } from '../../../../../../common/services/game.service';
import { JackpotService } from '../../../../../../common/services/jackpot.service';
import { Game } from '../../../../../../common/typings';
import { GamesRefreshService } from '../games-refresh.service';
import { SCHEMA_FILTER, SCHEMA_LIST } from './jp-games-info.schema';
import { ViewDetailsComponent } from './modals/view-details.component';

@Component({
  selector: 'jp-games-info',
  templateUrl: './jp-games-info.component.html',
  styleUrls: ['./jp-games-info.component.scss'],
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: GameService },
  ]
})
export class JpGamesInfoComponent implements OnInit {
  schema: SwuiGridField[] = SCHEMA_LIST;
  filterSchema: SwuiGridField[] = SCHEMA_FILTER;

  isSuperAdmin: boolean = true;
  loading: boolean = false;
  loadingDetailed: boolean = false;
  readonly isDetailedAvailable: boolean;

  @Input() entity: Entity;

  @ViewChild(SwuiGridComponent, { static: true }) gridRef?: SwuiGridComponent<Game>;
  private readonly destroyed$ = new Subject<void>();

  constructor( private service: GameService,
               private swHubAuthService: SwHubAuthService,
               private gamesRefreshService: GamesRefreshService,
               private jackpotService: JackpotService,
               private dialog: MatDialog
  ) {
    this.isSuperAdmin = this.swHubAuthService.isSuperAdmin;
    this.isDetailedAvailable = this.swHubAuthService.allowedTo([PERMISSIONS_NAMES.JP_CONFIG_REPORT]);
  }

  ngOnInit(): void {
    this.gamesRefreshService.listen('general')
      .subscribe(() => {
        this.gridRef.dataSource.loadData();
      });

    this.gridRef.dataSource.requestData = {
      path: this.entity.path,
      jackpots: true,
      jpnDetailsAllowed: this.swHubAuthService.areGranted(PERMISSIONS_LIST.JACKPOT_VIEW)
    };
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onClick( { row, index } ) {
    if (row?.settings?.jackpotId) {
      const key = Object.keys(row?.settings?.jackpotId)[index];
      const id = row?.settings?.jackpotId[key];

      this.jackpotService.getJackpotInfo(id)
        .pipe(
          switchMap(data => this.dialog.open(ViewDetailsComponent, {
            data: { data },
            width: '800px',
            disableClose: true
          }).afterClosed()),
          take(1)
        )
        .subscribe();
    }
  }

  downloadCsv() {
    this.loading = true;
    const fileName = `${this.entity.name} Export games list ${moment().format('YYYY-MM-DD HH:MM')}`;
    this.service.downloadCsvJpGames(this.entity.path, fileName).pipe(
      catchError(( err ) => {
        this.loading = false;
        return throwError(err);
      }),
      take(1)
    ).subscribe(() => {
      this.loading = false;
    });
  }

  downloadDetailedCsv() {
    this.loadingDetailed = true;
    const fileName = `${this.entity.name} JP Definitions Report ${moment().format('YYYY-MM-DD HH:MM')}`;
    this.service.downloadCsvJpDefinitions(this.entity.path, fileName).pipe(
      catchError(( err ) => {
        this.loadingDetailed = false;
        return throwError(err);
      }),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.loadingDetailed = false;
    });
  }
}
