import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';
import { Game } from '../../../../../../common/typings';
import { DISPLAY_GAME_STATUS_LIST } from '../general-games-info/games.schema';
import { gameStatusClassMap } from '../../../../../../app.constants';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'title',
    title: 'ENTITY_SETUP.GAMES.title',
    type: 'string',
    td: {
      type: 'string',
      nowrap: true,
    },
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: false,
    filterMatch: SchemaFilterMatchEnum.Contains,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'code',
    title: 'ENTITY_SETUP.GAMES.code',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: false,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'status',
    title: 'ENTITY_SETUP.GAMES.status',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    data: DISPLAY_GAME_STATUS_LIST,
    td: {
      type: 'status',
      displayStatusList: DISPLAY_GAME_STATUS_LIST,
      classMap: gameStatusClassMap,
      readonly: true
    },
  },
  {
    field: 'connectedPool',
    title: 'ENTITY_SETUP.GAMES.connectedPool',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    isFilterableAlways: true,
    td: {
      type: 'actionList',
      valueFn: ( row: Game ) => {
        if (row?.settings?.jackpotId) {
          return Object.keys(row?.settings?.jackpotId).map(key => {
            return `${key} {${row?.settings?.jackpotId[key]}}`;
          });
        } else if (row?.features?.jackpotTypes) {
          return row.features?.jackpotTypes.map(type => `${type.toLowerCase()} {-}`);
        }
        return [];
      },
      isDisabled: ( row: Game ) => {
        return !row._meta.jpnDetailsAllowed;
      }
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'jackpotTypes',
    title: 'ENTITY_SETUP.GAMES.connectedPool',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true
  }
];


export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
