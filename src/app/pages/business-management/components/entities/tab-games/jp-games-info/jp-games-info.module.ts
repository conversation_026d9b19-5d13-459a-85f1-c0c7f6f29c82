import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../../../common/components/download-csv/download-csv.module';
import { JackpotService } from '../../../../../../common/services/jackpot.service';
import { JpGamesInfoComponent } from './jp-games-info.component';
import { ViewDetailsModule } from './modals/view-details.module';


@NgModule({
  declarations: [
    JpGamesInfoComponent
  ],
  exports: [
    JpGamesInfoComponent
  ],
  imports: [
    CommonModule,
    SwuiGridModule,
    SwuiSchemaTopFilterModule,
    DownloadCsvModule,
    ViewDetailsModule
  ],
  providers: [
    JackpotService
  ]
})
export class JpGamesInfoModule {
}
