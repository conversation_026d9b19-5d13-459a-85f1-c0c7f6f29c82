import { ActivatedRouteSnapshot, CanActivate, CanActivateChild, Router } from '@angular/router';
import { EntityStateService } from '../../entity-state.service';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class SetupEntityGuard implements CanActivate, CanActivateChild {
  constructor(private stateService: EntityStateService,
              private router: Router) {
  }

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> | boolean {
    const {params: {path}} = route;

    this.stateService.initEntity(path);

    return true;
  }

  canActivateChild(childRoute: ActivatedRouteSnapshot): Observable<boolean> | boolean {
    const {fragment} = childRoute;
    return this.stateService.tabs$
      .pipe(
        map(tabs => {
          const tab = tabs.find(({name}) => name === fragment);

          if (!tab?.available) {
            const availableTab = tabs.find(t => t.available);
            const {path} = childRoute.parent.params;

            if (!availableTab) {
              this.router.navigate(['pages']);

              return false;
            }

            this.router.navigate(['pages', 'business-management', 'entities', 'setup', path, 'p', availableTab.name],
              {fragment: availableTab.name});
          }

          return true;
        })
      );
  }
}
