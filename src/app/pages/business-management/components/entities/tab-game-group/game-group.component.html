<div class="game-groups-header">
  <div class="game-groups-header__left">
    <form class="game-groups-header__form">
      <div class="game-groups-header__label">{{ 'ENTITY_SETUP.GAME_GROUP.FORM.gameGroup' | translate }}:</div>
      <mat-form-field appearance="outline" class="no-field-padding" style="margin-top: -2px">
        <lib-swui-select [formControl]="defaultGroupControl" [data]="gameGroupsSelectOptions" [showSearch]="true">
        </lib-swui-select>
      </mat-form-field>
      <button
        *ifAllowed="entitySettingsPermissionsList"
        mat-icon-button
        (click)="submit($event)">
        <mat-icon>save</mat-icon>
      </button>
    </form>
  </div>
</div>

<lib-swui-grid [schema]="schema"
               [rowActions]="rowActions"
               [disableRefreshAction]="true"
               (widgetActionEmitted)="onClick($event)">
  <div class="grid-head-button">
    <button
      *ifAllowed="createPermissionsList"
      mat-flat-button
      color="primary"
      (click)="showGameGroupModal()">
      {{ 'ENTITY_SETUP.GAME_GROUP.addGameGroup' | translate }}
    </button>
  </div>
</lib-swui-grid>
