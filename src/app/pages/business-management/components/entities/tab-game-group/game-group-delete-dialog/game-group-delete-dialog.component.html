<div mat-dialog-content>
  <p>{{ 'ENTITY_SETUP.GAME_GROUP.MODAL.confirmMessage' | translate }}</p>
  <div class="note-force" *ngIf="!!gameGroup.isDefault">
    {{ 'ENTITY_SETUP.GAME_GROUP.MODAL.messageDefaultGameGroup' | translate }}
    <br>
    {{ 'ENTITY_SETUP.GAME_GROUP.MODAL.messageFlag' | translate }}
    <br>
    <mat-checkbox [formControl]="isForceControl">
      {{ 'ENTITY_SETUP.GAME_GROUP.MODAL.labelAddFlag' | translate }}
    </mat-checkbox>
  </div>
</div>

<div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
  <button mat-button color="primary" (click)="cancel()" class="mr-5 mat-button-md">
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" (click)="submit()" [disabled]="!!gameGroup.isDefault && !isForceControl?.value">
    {{ 'DIALOG.save' | translate }}
  </button>
</div>

