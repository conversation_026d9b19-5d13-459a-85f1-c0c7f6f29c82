.table-game-groups {
  &__scroll {
    height: calc(100vh - 400px);
    overflow: auto;
    width: 100%;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #e4e5e9;
  }

  &__table {
    position: relative;
    width: 100%;

    th {
      position: sticky;
      top: 0;
      height: 42px;
      padding: 0 8px;
      border-bottom: none;
      font-size: 16px;
      font-weight: 400;
      color: #2a2c44;
      background: #fff;
      z-index: 1;
      &:first-child {
        padding-left: 16px;
      }
      &:last-child {
        padding-right: 16px;
      }
      &.center {
        text-align: center;
      }
    }

    td {
      font-size: 14px;
      line-height: 1;
      color: #2a2c44;
      border-bottom: none;
      padding: 0 8px;
      &:first-child {
        padding-left: 16px;
      }
      &:last-child {
        padding-right: 16px;
      }
      &.center {
        text-align: center;
      }
    }

    thead {
      tr {
        height: 42px;
        background-color: #fff;
      }
    }

    tbody {
      tr {
        height: 45px;
        &:nth-child(odd) {
          background-color: #eef1f5;
        }
        &:nth-child(even) {
          background-color: #f9f9fa;
        }
      }
    }
  }
  &__actions {
    display: flex;
    justify-content: center;
    button {
      height: 30px;
      width: 30px;
      line-height: 30px;
      margin-right: 5px;
      color: #939db1;
      &:hover {
        color: #2a2c44;
        transition: .15s ease-in-out;
      }
    }
  }
}

.game-groups-header {
  display: flex;
  align-items: flex-end;
  padding: 0 4px 5px;
  height: 48px;
  &__actions {
    display: flex;
    align-items: center;
    margin-left: auto;
    button {
      height: 30px;
      font-size: 12px;
      line-height: 30px;
      padding: 0 8px;
      letter-spacing: 0.0892857em;
      text-transform: uppercase;
    }
  }
  &__form {
    display: flex;
    align-items: center;
    button {
      width: 30px;
      height: 30px;
      line-height: 30px;
      color: #939db1;
      &:hover {
        color: #2a2c44;
        transition: .15s ease-in-out;
      }
    }
  }
  lib-swui-select {
    position: relative;
    bottom: -10px;
  }
  &__label {
    margin-right: 5px;
    font-size: 14px;
    font-weight: 500;
  }
}
