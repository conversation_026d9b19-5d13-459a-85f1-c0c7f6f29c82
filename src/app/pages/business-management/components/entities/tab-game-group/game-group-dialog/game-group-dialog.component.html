<h3 mat-dialog-title class="no-margin-top mb-20">
  {{ (!!gameGroup ? 'ENTITY_SETUP.GAME_GROUP.editGameGroupTitle' : 'ENTITY_SETUP.GAME_GROUP.createGameGroupTitle') | translate }}
</h3>

<div mat-dialog-content>
  <form [formGroup]="form" fxLayout="column">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP.name' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="nameControl"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="nameControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.GAME_GROUP.description' | translate }}</mat-label>
      <textarea matInput trimValue type="text" [formControl]="descriptionControl" rows="5"></textarea>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="descriptionControl"
          [force]="submitted">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </form>
</div>
<div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
  <button mat-button color="primary" (click)="cancel()" class="mr-5 mat-button-md">{{ 'DIALOG.cancel' | translate }}</button>
  <button mat-flat-button color="primary" (click)="submit()" class="mat-button-md">{{ 'DIALOG.save' | translate }}</button>
</div>
