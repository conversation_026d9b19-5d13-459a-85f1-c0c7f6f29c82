import { Component, Inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';

import { GameGroup } from '../../../../../../common/models/game-group.model';
import { ValidationService } from '../../../../../../common/services/validation.service';


@Component({
  selector: 'game-group-dialog',
  templateUrl: 'game-group-dialog.component.html',
})
export class GameGroupDialogComponent {
  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
    invalidFormat: 'VALIDATION.invalidFormat',
    spacesAreNotAllowed: 'VALIDATION.spacesAreNotAllowed',
  };

  gameGroup?: GameGroup;
  form: FormGroup;
  submitted: boolean = false;

  constructor( private dialogRef: MatDialogRef<GameGroupDialogComponent>,
               private fb: FormBuilder,
               @Inject(MAT_DIALOG_DATA) public data: { gameGroup: GameGroup }
  ) {
    this.gameGroup = data.gameGroup;
    this.form = this.initForm();
  }

  cancel() {
    this.dialogRef.close();
  }

  submit() {
    this.submitted = true;
    if (this.form.valid) {
      this.dialogRef.close(this.form.value);
    }
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  get descriptionControl(): FormControl {
    return this.form.get('description') as FormControl;
  }

  private initForm(): FormGroup {
    const form = this.fb.group({
      name: [
        '', Validators.compose([
          Validators.required,
          ValidationService.gameGroupNameValidator,
          ValidationService.noWhitespaceValidation,
        ])
      ],
      description: ['', Validators.required],
    });

    if (!!this.gameGroup) {
      form.patchValue(this.gameGroup);
      form.get('name').disable();
    }

    return form;
  }
}
