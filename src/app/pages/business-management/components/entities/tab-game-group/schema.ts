import { SwuiGridField } from '@skywind-group/lib-swui';
import { GameGroup } from '../../../../../common/models/game-group.model';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'id',
    title: 'ENTITY_SETUP.GAME_GROUP.id',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'click',
      nowrap: true,
      isDisabled: ( row: GameGroup ) => !row.isOwner,
    },
  },
  {
    field: 'name',
    title: 'ENTITY_SETUP.GAME_GROUP.name',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'string',
      truncate: {
        maxLength: 75,
        isEllipsis: true,
      }
    },
  },
  {
    field: 'description',
    title: 'ENTITY_SETUP.GAME_GROUP.description',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'string',
      truncate: {
        maxLength: 75,
        isEllipsis: true,
      }
    },
  },
  {
    field: 'isDefault',
    title: 'ENTITY_SETUP.GAME_GROUP.isDefault',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'inactivity',
      valueFn: ( row: any ) => row.isDefault,
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
  },
  {
    field: 'isInherited',
    title: 'ENTITY_SETUP.GAME_GROUP.inherited',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'inactivity',
      valueFn: ( row: any ) => !row.isOwner,
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
