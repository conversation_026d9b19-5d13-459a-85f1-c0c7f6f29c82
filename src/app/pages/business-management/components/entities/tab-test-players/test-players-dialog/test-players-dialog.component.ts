import { Component, Inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiDatePickerConfig } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { TestPlayer } from '../../../../../../common/typings/test-player';

@Component({
  selector: 'test-players-dialog',
  templateUrl: './test-players-dialog.component.html',
})
export class TestPlayersDialogComponent {
  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
    valueShouldBeUnique: 'ENTITY_SETUP.TEST_PLAYERS.codeShouldBeUnique',
  };

  startDate = moment().toISOString();

  config: SwuiDatePickerConfig = {
    timePicker: true,
    timeDisableLevel: {
      hour: true,
      minute: true,
      second: false
    }
  };

  testPlayer?: TestPlayer;
  codes?: string[];
  form: FormGroup;

  constructor( private dialogRef: MatDialogRef<TestPlayersDialogComponent>,
               private fb: FormBuilder,
               @Inject(MAT_DIALOG_DATA) public data: { testPlayer: TestPlayer, codes: string[] }
  ) {
    this.testPlayer = data.testPlayer;
    this.codes = data.codes;
    this.form = this.initForm();
  }

  get codeControl(): FormControl | undefined {
    return this.form?.get('code') as FormControl;
  }

  initForm(): FormGroup {
    const form = this.fb.group({
      code: [
        '', Validators.compose([
          Validators.required,
          ValidationService.isArrayContainsValue(this.codes),
        ])
      ],
    });

    if (!!this.testPlayer) {
      form.patchValue(this.testPlayer);

      form.get('code').disable();
    }

    return form;
  }

  cancel() {
    this.dialogRef.close();
  }

  submit() {
    if (this.form.valid) {
      this.dialogRef.close({ ...this.form.value, ...{ startDate: this.startDate } });
    }
  }
}
