<h3 mat-dialog-title class="no-margin-top mb-20">
  {{ (!!testPlayer ?
  ('ENTITY_SETUP.TEST_PLAYERS.editTestPlayer' | translate : {code: testPlayer.code}) :
  'ENTITY_SETUP.TEST_PLAYERS.createTestPlayer' | translate) }}
</h3>

<div mat-dialog-content>
  <form [formGroup]="form" fxLayout="column">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.TEST_PLAYERS.code' | translate }}</mat-label>
      <input matInput trimValue type="text" [formControl]="codeControl"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="codeControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </form>
</div>

<div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
  <button mat-button
          color="primary"
          class="mr-5 mat-button-md"
          (click)="cancel()">
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button mat-flat-button
          class="mat-button-md"
          color="primary"
          [disabled]="!form?.valid"
          (click)="submit()">
    {{ 'DIALOG.save' | translate }}
  </button>
</div>
