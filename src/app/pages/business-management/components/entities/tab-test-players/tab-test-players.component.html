<test-players *ngIf="!entity.isReseller()"
              [entity]="entity"
              [entitySettings]="entitySettings"
              [total]="total">
</test-players>

<ng-container *ngIf="entity.isMerchant" style="padding-top: 15px;">
  <test-players-merchant
    [entity]="entity"
    [entitySettings]="entitySettings"
    (totalChanged)="handleTotalChanged($event)">
  </test-players-merchant>
</ng-container>
