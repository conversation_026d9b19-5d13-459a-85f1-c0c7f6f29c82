<form [formGroup]="form">
  <div class="row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.TEST_PLAYERS.maxTestPlayers' | translate }}:</mat-label>
      <input
        matInput
        type="number"
        [formControl]="maxTestPlayersControl"
        [placeholder]="'ENTITY_SETUP.TEST_PLAYERS.maxTestPlayers' | translate">
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="maxTestPlayersControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <div class="save-button">
      <button
        mat-icon-button
        color="primary"
        [disabled]="isApplyButtonDisabled()"
        (click)="showConfirmation($event)"
        matTooltip="{{'SETTINGS.apply' | translate}}">
        <mat-icon>save</mat-icon>
      </button>
    </div>
  </div>

  <span *ngIf="isMaxTestPlayersSmallerThanTotal()" class="error-label">
    {{ 'ENTITY_SETUP.TEST_PLAYERS.maxTestPlayersSmallerThanTotal' | translate }}
  </span>

  <div class="row">
    <mat-slide-toggle [formControl]="autoCreateTestJackpotControl">
      {{'ENTITY_SETUP.TEST_PLAYERS.autoCreateTestJackpot' | translate}}
    </mat-slide-toggle>
  </div>
</form>
