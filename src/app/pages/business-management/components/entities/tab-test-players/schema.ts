import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';

const TEST_PLAYER_SOURCE_CLASS_MAP = {
  support: 'sw-chip sw-chip-green',
  integration: 'sw-chip sw-chip-blue'
};

const TEST_PLAYER_SOURCE_LIST = [
  { id: 'support', code: 'support', displayName: 'ENTITY_SETUP.TEST_PLAYERS.GRID.support' },
  { id: 'integration', code: 'integration', displayName: 'ENTITY_SETUP.TEST_PLAYERS.GRID.integration' }
];

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'code',
    title: 'ENTITY_SETUP.TEST_PLAYERS.GRID.code',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isFilterableAlways: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'source',
    title: 'ENTITY_SETUP.TEST_PLAYERS.GRID.source',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'status',
      statusList: TEST_PLAYER_SOURCE_LIST,
      classMap: TEST_PLAYER_SOURCE_CLASS_MAP,
      readonly: true,
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'startDate',
    title: 'ENTITY_SETUP.TEST_PLAYERS.GRID.startDate',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'timestamp',
      nowrap: true,
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
