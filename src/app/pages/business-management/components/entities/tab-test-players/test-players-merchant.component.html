<lib-swui-schema-top-filter [schema]="filterSchema"></lib-swui-schema-top-filter>
<lib-swui-grid #grid
               class="entity-test-players-grid"
               [schema]="schema"
               [rowActions]="rowActions"
               [ignorePlainLink]="true"
               [rowActionsColumnTitle]="''">
  <div class="grid-head-button">
    <button mat-flat-button color="primary"
            [disabled]="entitySettings?.maxTestPlayers === total"
            (click)="showTestPlayersModal()">
      {{ 'ENTITY_SETUP.TEST_PLAYERS.GRID.newTestPlayer' | translate }}
    </button>
  </div>
</lib-swui-grid>
