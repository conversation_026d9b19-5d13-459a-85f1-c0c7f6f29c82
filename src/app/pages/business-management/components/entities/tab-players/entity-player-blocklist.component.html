<div class="players-block-list">
  <lib-swui-grid
    #grid
    [schema]="schema"
    [bulkActions]="bulkActions"
    [rowActions]="rowActions"
    [disableRefreshAction]="true"
    [columnsManagement]="false"
    [bulkSelectionOnly]="true"
    [ignoreQueryParams]="true"
    [rowActionsColumnTitle]="''">

    <input trimValue type="text" [formControl]="codeControl" [placeholder]="'ENTITY_SETUP.PLAYER_BLOCKLIST.placeholderEnterCode' | translate">

    <button
      mat-flat-button
      color="primary"
      class="suspend-btn ml-10"
      (click)="suspendPlayer()">
      {{ 'ENTITY_SETUP.PLAYER_BLOCKLIST.btnSuspendPlayer' | translate }}
    </button>

    <button
      mat-stroked-button
      color="primary"
      class="ml-10"
      [ngClass]="{'hidden':grid.selection.isEmpty() && !grid.selection.hasValue()}"
      (click)="restoreSelectedPlayers()">
      {{ 'ENTITY_SETUP.PLAYER_BLOCKLIST.btnRestorePlayers' | translate }}
    </button>
  </lib-swui-grid>

</div>
