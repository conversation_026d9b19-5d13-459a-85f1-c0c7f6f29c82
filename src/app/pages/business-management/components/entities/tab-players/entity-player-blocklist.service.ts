import { Injectable } from '@angular/core';
import { GridRequestData, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { BaseService } from '../../../../../common/base';
import { SuspendedPlayer } from '../../../../../common/models/player.model';

@Injectable()
export class EntityPlayerBlocklistService implements SwuiGridDataService<SuspendedPlayer> {

  constructor(private notifications: SwuiNotificationsService,
              private http: HttpClient) {
  }

  public getUrl(path?): string {
    return `${BaseService.apiEndpoint}${path && path !== ':' ? '/entities/' + path : ''}/suspendedplayers`;
  }

  getGridData(params: HttpParams, requestData?: GridRequestData): Observable<HttpResponse<SuspendedPlayer[]>> {
    return this.http.get<SuspendedPlayer[]>(this.getUrl(requestData.path), {
      params,
      observe: 'response'
    }).pipe(
      catchError((err) => this.handleErrors(err))
    );
  }

  private handleErrors(err): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
