import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EntityPlayerBlocklistComponent } from './entity-player-blocklist.component';
import { TabPlayersComponent } from './tab-players.component';

import { TabPlayersRoutingModule } from './tab-players.routing';


@NgModule({
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        TabPlayersRoutingModule,
        ReactiveFormsModule,
        ControlMessagesModule,
        SwuiGridModule,
        MatFormFieldModule,
        MatButtonModule,
        MatInputModule,
        TrimInputValueModule,
    ],
  exports: [],
  declarations: [
    TabPlayersComponent,
    EntityPlayerBlocklistComponent,
  ],
  providers: [],
})
export class TabPlayersModule {
}
