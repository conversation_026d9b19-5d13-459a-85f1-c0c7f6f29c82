import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { BulkAction, RowAction, SwuiGridComponent, SwuiGridDataService } from '@skywind-group/lib-swui';
import { Subject, zip } from 'rxjs';
import { finalize, take } from 'rxjs/operators';

import { Entity } from '../../../../../common/models/entity.model';
import { EntityService } from '../../../../../common/services/entity.service';
import { EntityPlayerBlocklistService } from './entity-player-blocklist.service';


@Component({
  selector: 'entity-player-blocklist, [entity-player-blocklist]',
  templateUrl: './entity-player-blocklist.component.html',
  styleUrls: [
    './entity-player-blocklist.component.scss',
  ],
  providers: [
    EntityPlayerBlocklistService,
    {provide: SwuiGridDataService, useExisting: EntityPlayerBlocklistService}
  ]
})
export class EntityPlayerBlocklistComponent implements OnInit {

  @ViewChild('grid', {static: true}) grid: SwuiGridComponent<{ code: string }>;

  submitted: boolean = false;
  codeControl: FormControl = new FormControl('');
  schema = [
    {
      field: 'code',
      title: 'ENTITY_SETUP.PLAYER_BLOCKLIST.playerCode',
      type: 'string',
      isList: true,
      isListVisible: true,
      isViewable: true,
      alignment: {
        th: 'left',
        td: 'grid-cell-playercode',
      },
    }
  ];

  bulkActions: BulkAction[] = [
    new BulkAction({title: 'Select Player Code'})
  ];
  rowActions: RowAction[] = [
    new RowAction({
      title: 'ENTITY_SETUP.PLAYER_BLOCKLIST.actionRestore',
      icon: 'replay',
      fn: (item) => this.restorePlayer(item.code)
    })
  ];

  private readonly destroyed$ = new Subject<void>();
  private _entity: Entity;
  @Input()
  set entity(value: Entity) {
    if (!value) return;
    this._entity = value;
  }

  get entity(): Entity {
    return this._entity;
  }

  constructor(private entityService: EntityService<Entity>) {
    this.codeControl.setValidators(Validators.required);
    this.codeControl.updateValueAndValidity();
  }

  ngOnInit() {
    this.grid.dataSource.requestData = {
      path: this._entity.path
    };
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  restoreSelectedPlayers() {
    const restoreSources = this.grid.selection.selected
      .map(({code}) => this.entityService.restorePlayer(code, this.entity.path));

    zip(...restoreSources)
      .pipe(take(1))
      .subscribe(() => {
        this.grid.selection.clear();
        this.grid.dataSource.loadData();
      });
  }

  restorePlayer(playerCode: string) {
    this.entityService.restorePlayer(playerCode, this.entity.path)
      .pipe(take(1))
      .subscribe(() => this.grid.dataSource.loadData());
  }

  suspendPlayer() {
    this.submitted = true;

    if (this.codeControl.valid) {
      let code: string = this.codeControl.value;
      this.entityService.suspendPlayer(code, this.entity.path)
        .pipe(
          take(1),
          finalize(() => this.submitted = false)
        )
        .subscribe(() => {
            this.grid.dataSource.loadData();
            this.codeControl.reset();
          }
        );
    }
  }
}
