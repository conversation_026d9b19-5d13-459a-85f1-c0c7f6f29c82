<div class="tables-row__header">
  <div class="tables-row__title">{{ 'ENTITY_SETUP.REGIONAL.titleLanguages' | translate }}</div>
  <button mat-icon-button (click)="showLanguageModal()" matTooltip="{{ 'ENTITY_SETUP.REGIONAL.manage' | translate }}" *ngIf="allowedEdit">
    <mat-icon>tune</mat-icon>
  </button>
</div>

<mat-table [dataSource]="dataSource" class="mat-table--simple">
  <ng-container matColumnDef="displayName">
    <mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.REGIONAL.languageName' | translate }}</mat-header-cell>
    <mat-cell *matCellDef="let language">
      <span [ngClass]="{'text-bold': language.isDefault }">
      {{ language.displayName + ' (' + (language.code | uppercase) + ')' }}
      </span>
    </mat-cell>
  </ng-container>

  <ng-container matColumnDef="code">
    <mat-header-cell *matHeaderCellDef class="mat-table align-right">
      <input matInput trimValue
             class="search-field"
             autocomplete="off"
             [formControl]="searchControl"
             placeholder="{{'ENTITY_SETUP.GAMES.searchPlaceholder' | translate}}">
    </mat-header-cell>
    <mat-cell *matCellDef="let language" class="align-right">
      <ng-container *ngIf="language['isDefault']; else actionsMenu">
        <span class="sw-chip"> {{ 'ENTITY_SETUP.REGIONAL.default' | translate }}</span>
      </ng-container>
      <ng-template #actionsMenu>
        <lib-swui-grid-row-actions [actions]="rowActions" [row]="language" menuIcon="menu" [ignorePlainLink]="true">
        </lib-swui-grid-row-actions>
      </ng-template>
    </mat-cell>
  </ng-container>

  <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
  <mat-row *matRowDef="let language; columns: displayedColumns;"></mat-row>
</mat-table>
