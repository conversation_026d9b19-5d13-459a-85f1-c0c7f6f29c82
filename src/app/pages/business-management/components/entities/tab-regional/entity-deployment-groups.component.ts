import { Component, Input, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { RowAction } from '@skywind-group/lib-swui';
import { take } from 'rxjs/operators';
import { Entity } from '../../../../../common/models/entity.model';
import { DeploymentGroupService } from '../../../../../common/services/deployment-group.service';
import { DeploymentGroup } from '../../../../../common/typings/deployment-group';

@Component({
  selector: '[entity-deployment-groups]',
  templateUrl: './entity-deployment-groups.component.html',
})
export class EntityDeploymentGroupsComponent implements OnInit {

  @Input() entity: Entity;
  @Input() deploymentGroups?: DeploymentGroup[];

  dataSource: MatTableDataSource<DeploymentGroup>;
  displayedColumns: string[] = ['description', 'route', 'actions'];
  rowActions: RowAction[];

  constructor( private deploymentGroupService: DeploymentGroupService ) {
    this.setRowActions();
  }

  ngOnInit() {
    this.dataSource = new MatTableDataSource(this.deploymentGroups);
  }

  setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.setDefault',
        icon: 'home',
        fn: ( deploymentGroup: DeploymentGroup ) => this.setDeploymentGroup(deploymentGroup),
      }),
    ];
  }

  isDefaultDeploymentGroup( deploymentGroup: DeploymentGroup ) {
    return this.entity.deploymentGroup?.id === deploymentGroup.id;
  }

  setDeploymentGroup( deploymentGroup: DeploymentGroup ) {
    this.deploymentGroupService.setDeploymentGroup(this.entity.path, deploymentGroup.route)
      .pipe(
        take(1),
      )
      .subscribe(
        () => this.entity.deploymentGroup = deploymentGroup
      );
  }

  detachDeploymentGroup() {
    this.deploymentGroupService.detachDeploymentGroup(this.entity.path)
      .pipe(
        take(1),
      )
      .subscribe(
        () => delete this.entity.deploymentGroup
      );
  }
}
