import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CountriesResolver } from '../../../../../common/services/resolvers/countries.resolver';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { LanguagesResolver } from '../../../../../common/services/resolvers/languages.resolver';
import { SetupEntityDeploymentGroupsResolver } from '../resolvers/setup-entity-deployment-groups.resolver';
import { SetupEntityJurisdictionsResolver } from '../resolvers/setup-entity-jurisdictions.resolver';
import { TabRegionalComponent } from './tab-regional.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: TabRegionalComponent,
        resolve: {
          countries: CountriesResolver,
          currencies: CurrenciesResolver,
          languages: LanguagesResolver,
          entityJurisdictions: SetupEntityJurisdictionsResolver,
          deploymentGroups: SetupEntityDeploymentGroupsResolver,
        }
      }
    ])
  ],
  exports: [
    RouterModule,
  ]
})
export class TabRegionalRoutingModule {
}
