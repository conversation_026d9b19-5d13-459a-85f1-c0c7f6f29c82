.tables-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;

  &__header {
    display: flex;
    align-items: center;
    padding: 4px 16px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-bottom: none;
    background: #fff;

    button {
      margin-left: auto
    }
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
  }

  &__item {
    width: 50%;
    padding: 0 8px 16px;
    @media(max-width: 767px) {
      overflow: auto
    }
  }

  @media (max-width: 900px) {
    &__item {
      width: 100%;
    }
  }
}

.mat-table {
  &--simple {
    overflow-y: scroll;
    height: 350px;
    border: 1px solid #E0E0E0;

    mat-row {
      min-height: 44px;

      &:nth-child(odd) {
        background-color: #eef1f5;
      }

      &:nth-child(even) {
        background-color: #f9f9fa;
      }
    }

    mat-header-row {
      min-height: 42px;
    }

    mat-header-cell {
      font-size: 16px;
      color: #2a2c44;
      height: 42px !important;
    }

    mat-cell {
      font-size: 14px;
      color: #2a2c44;
      height: 44px !important;

      &:last-child {
        padding-right: 10px;
      }

      lib-swui-grid-row-actions {
        display: flex;
        width: 20px;
        justify-content: center;
        margin-right: 15px;
      }
    }
  }

  .align-right {
    display: flex;
    justify-content: flex-end;
  }
}

.search-field {
  width: calc(10vw - 10px);
}

.opacitedList {
  opacity: 0.5;
}

.hiddenText {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  top: 0;
  padding-top: 0.7em;
}
