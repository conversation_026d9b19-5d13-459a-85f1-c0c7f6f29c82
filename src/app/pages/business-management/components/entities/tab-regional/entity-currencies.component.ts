import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, map, mergeMap, switchMap, take, takeUntil, tap } from 'rxjs/operators';

import { Entity } from '../../../../../common/models/entity.model';
import { CurrencyService } from '../../../../../common/services/currency.service';
import { codeArrayToObjectReducer, EntityService, pushDefaultToStart } from '../../../../../common/services/entity.service';
import { Currency } from '../../../../../common/typings';
import { ManageBalanceComponent } from '../../mat-business-structure/dialogs/manage-balance/manage-balance.component';
import { MatCurrencyDialogComponent } from '../../mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.component';
import { RemoveConfirmDialogComponent } from '../dialogs/remove-confirm-dialog.component';
import { PERMISSIONS_LIST } from '../../../../../app.constants';


interface EntityCurrency extends Currency {
  balance: number;
  isDefault: boolean;
}

@Component({
  selector: '[entity-currencies]',
  templateUrl: './entity-currencies.component.html',
})
export class EntityCurrenciesComponent implements OnInit, OnDestroy {
  selectedCurrencyCode: string;

  @Input() entity: Entity;
  searchControl: FormControl = new FormControl();

  readonly allowedToChangeBalance: boolean;
  readonly allowedEdit: boolean;

  dataSource: MatTableDataSource<EntityCurrency>;
  rowActions: RowAction[];
  readonly displayedColumns: string[];

  private dialogRef: MatDialogRef<any>;
  private currenciesHash: { [code: string]: Currency };
  private _currencies: Currency[];
  private destroyed$ = new Subject<any>();

  @Input()
  set currencies( currencies: Currency[] ) {
    this._currencies = currencies;
    this.currenciesHash = this._currencies.reduce(codeArrayToObjectReducer, {});
  }

  get currencies(): Currency[] {
    return this._currencies;
  }

  constructor( private currencyService: CurrencyService<Currency>,
               private entityService: EntityService<Entity>,
               private authService: SwHubAuthService,
               private dialog: MatDialog,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
  ) {
    this.allowedEdit = this.authService.allowedTo(PERMISSIONS_LIST.ENTITY_SETTINGS);
    this.allowedToChangeBalance = this.authService.allowedTo(['finance:credit', 'finance:debit']);
    this.displayedColumns = ['displayName', ...(this.authService.allowedTo(PERMISSIONS_LIST.ENTITY_BALANCE) ? ['balance'] : []), 'code'];
    this.setRowActions();
  }

  ngOnInit() {
    this.refreshData();

    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( filterValue: string ) => {
      this.dataSource.filter = filterValue?.trim().toLowerCase();
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showBalanceModal( $event, code: string ) {
    $event.preventDefault();
    this.selectedCurrencyCode = code;

    this.dialogRef = this.dialog.open(ManageBalanceComponent, {
      disableClose: true,
      width: '350px',
      data: { entity: this.entity, currencies: this.currencies, selectedCurrencyCode: code }
    });
    this.dialogRef.afterClosed().pipe(
      take(1),
      takeUntil(this.destroyed$)
    ).subscribe(() => this.onBalanceAdded());
  }

  showCurrencyModal() {
    this.searchControl.reset();
    this.dialogRef = this.dialog.open(MatCurrencyDialogComponent, {
      disableClose: true,
      width: '500px',
      data: { entity: this.entity, currencies: this.currencies }
    });
    this.dialogRef.afterClosed()
      .pipe(
        filter(( data: { entity: Entity, selected: string[] } ) => !!data && typeof data.entity !== 'undefined'),
        take(1),
        map(( { entity, selected } ) => {
          const updated = new Entity(entity);
          updated.currencies = [...selected];
          return updated;
        }),
        switchMap(( entity: Entity ) => this.entityService.updateEntityItem(entity)),
        tap(( entity: Entity ) => this.entity.update(entity)),
        switchMap(() => this.translate.get('ENTITY_SETUP.REGIONAL.MODALS.notificationCurrenciesAdded')),
        takeUntil(this.destroyed$)
      ).subscribe(( message ) => {
      this.notifications.success(message, '');
      this.onCurrencyAdded();
    });
  }
  setDefaultCurrency( defaultCurrency: string ) {
    this.entity.defaultCurrency = defaultCurrency;
    this.entityService.updateEntityItem(this.entity).pipe(takeUntil(this.destroyed$)).subscribe(( data ) => {
      this.entity.update(data);
      this.refreshData();
    });
  }

  onBalanceAdded() {
    this.refreshParentBalances();
    this.refreshData();
  }

  onCurrencyAdded() {
    this.refreshParentBalances();
    this.refreshData();
  }

  removeCurrencyModal( currencyCode: string ) {
    this.dialog.open(RemoveConfirmDialogComponent, {
      width: '500px',
      data: { removeCode: currencyCode },
      disableClose: true
    }).afterClosed().pipe(
      filter(( code: string ) => !!code && code in this.currenciesHash),
      mergeMap(( code: string ) => {
        return this.currencyService.deleteCurrency(this.entity.path, code);
      }),
      tap(() => this.notifications.success(
        this.translate.instant('ENTITY_SETUP.REGIONAL.currencyDeleted'))
      ),
      take(1),
      takeUntil(this.destroyed$)
    ).subscribe(
      () => {
        this.entity.currencies = this.entity.currencies.filter(item => item !== currencyCode);
        this.refreshData();
        this.refreshParentBalances();
      }
    );
  }

  private refreshParentBalances() {
    this.entityService.getBalances(this.entity.entityParent.path, true)
      .pipe(
        take(1),
        takeUntil(this.destroyed$)
      ).subscribe(( balances ) => {
      this.entity.entityParent.update({ balances });
    });
  }

  private setRowActions() {
    this.rowActions = this.allowedEdit ? [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.setDefault',
        icon: 'home',
        fn: ( { code } ) => this.setDefaultCurrency(code),
        canActivateFn: ( { isDefault } ) => !isDefault,
      }),
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.actionDelete',
        icon: 'delete',
        fn: ( { code } ) => this.removeCurrencyModal(code),
        canActivateFn: ( { isDefault } ) => !isDefault,
      })
    ] : [];
  }

  private refreshData() {
    const currencies = pushDefaultToStart(this.entity.currencies, this.entity.defaultCurrency);
    this.dataSource = new MatTableDataSource(currencies.map<EntityCurrency>(code => ({
      ...this.currenciesHash[code],
      code,
      balance: this.entity.balances?.[code]?.main || 0,
      isDefault: this.entity.defaultCurrency === code,
    })));
  }
}
