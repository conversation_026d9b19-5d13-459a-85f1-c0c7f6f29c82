<div class="tables-row__header">
  <div class="tables-row__title">{{ 'ENTITY_SETUP.REGIONAL.titleDeploymentGroups' | translate }}</div>
  <button mat-button
          [disabled]="!entity.deploymentGroup"
          (click)="detachDeploymentGroup()">
    {{ 'ENTITY_SETUP.REGIONAL.detachDefaultGroup' | translate }}
  </button>
</div>

<mat-table [dataSource]="dataSource" class="mat-table--simple">
  <ng-container matColumnDef="description">
    <mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.REGIONAL.deploymentGroupDescription' | translate }}</mat-header-cell>
    <mat-cell *matCellDef="let deploymentGroup" matTooltip="{{ deploymentGroup.description }}" class="hiddenText">{{ deploymentGroup.description }}</mat-cell>
  </ng-container>

  <ng-container matColumnDef="route">
    <mat-header-cell *matHeaderCellDef class="align-right">{{ 'ENTITY_SETUP.REGIONAL.deploymentGroupRoute' | translate }}</mat-header-cell>
    <mat-cell *matCellDef="let deploymentGroup" class="align-right">
      {{ deploymentGroup.route }}
    </mat-cell>
  </ng-container>

  <ng-container matColumnDef="actions">
    <mat-header-cell *matHeaderCellDef></mat-header-cell>
    <mat-cell *matCellDef="let deploymentGroup" class="align-right">
      <ng-container *ngIf="isDefaultDeploymentGroup(deploymentGroup); else actionsMenu">
        <span class="sw-chip"> {{ 'ENTITY_SETUP.REGIONAL.default' | translate }}</span>
      </ng-container>
      <ng-template #actionsMenu>
        <lib-swui-grid-row-actions [actions]="rowActions" [row]="deploymentGroup" menuIcon="menu" [ignorePlainLink]="true">
        </lib-swui-grid-row-actions>
      </ng-template>
    </mat-cell>
  </ng-container>

  <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true">
  </mat-header-row>
  <mat-row *matRowDef="let deploymentGroup; columns: displayedColumns;"></mat-row>
</mat-table>
