<div class="tables-row__header">
  <div class="tables-row__title">{{ 'ENTITY_SETUP.REGIONAL.titleJurisdictions' | translate }}</div>
  <button mat-icon-button [disabled]="!parentJurisdictions.length" (click)="showJurisdictionsModal()" matTooltip="{{ 'ENTITY_SETUP.REGIONAL.manage' | translate }}">
    <mat-icon>tune</mat-icon>
  </button>
</div>


<mat-table [dataSource]="dataSource" class="mat-table--simple">
  <ng-container matColumnDef="title">
    <mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.REGIONAL.languageName' | translate }}</mat-header-cell>
    <mat-cell matTooltip="{{ jurisdiction.title + ' (' + jurisdiction.code + ')' }}"
              class="hiddenText"
              *matCellDef="let jurisdiction">{{ jurisdiction.title + ' (' + jurisdiction.code + ')' }}</mat-cell>
  </ng-container>

  <ng-container matColumnDef="code">
    <mat-header-cell *matHeaderCellDef class="mat-table align-right">
      <input matInput trimValue
             class="search-field"
             autocomplete="off"
             [formControl]="searchControl"
             placeholder="{{'ENTITY_SETUP.GAMES.searchPlaceholder' | translate}}">
    </mat-header-cell>
    <mat-cell *matCellDef="let code"></mat-cell>
  </ng-container>

  <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true">
  </mat-header-row>
  <mat-row *matRowDef="let jurisdiction; columns: displayedColumns;"></mat-row>
</mat-table>
