import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, map, mergeMap, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { CountryService } from '../../../../../common/services/country.service';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { codeArrayToObjectReducer, EntityService, pushDefaultToStart } from '../../../../../common/services/entity.service';

import { Country } from '../../../../../common/typings';
import { MatCountryDialogComponent } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';
import { RemoveConfirmDialogComponent } from '../dialogs/remove-confirm-dialog.component';


interface EntityCountry {
  code: string;
  displayName: string;
  isDefault: boolean;
  isInherited: boolean;
}

@Component({
  selector: '[entity-countries]',
  templateUrl: './entity-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityCountriesComponent implements OnInit {
  @Output() restrictedOwnList: EventEmitter<string[]> = new EventEmitter();
  searchControl: FormControl = new FormControl();
  rowActions: RowAction[];
  get displayedColumns(): string[] {
    return ['displayName', 'status', ...( this.disabledList ? [] : ['code'])];
  }
  dataSource: MatTableDataSource<EntityCountry>;

  @Input() set disabledList(val: boolean) {
    this._disabledList = val;
    this.refreshData();
  }

  get disabledList(): boolean {
    return this._disabledList;
  }

  @Input() entity: Entity;
  @Input() title: string = 'ENTITY_SETUP.REGIONAL.titleAllowedCountries';
  @Input() disableDelete?: boolean;
  private allAvailableCountries: string[] = [];
  private _disabledList: boolean;

  @Input()
  set countriesType(val: 'allowed' | 'restricted') {
    this._countriesType = val || 'allowed';
  }

  get countriesType(): 'allowed' | 'restricted' {
    return this._countriesType;
  }
  @Input()
  set restrictedCountries(val: string[]) {
    this._restrictedCountries = val || [];
    this.restrictedOwnList.emit(val);
    this.refreshData();
  }

  get restrictedCountries(): string[] {
    return this._restrictedCountries;
  }

  @Input()
  set restrictedCountriesInherited(val: string[]) {
    this._restrictedCountriesInherited = val || [];
    this.refreshData();
  }

  get restrictedCountriesInherited(): string[] {
    return this._restrictedCountriesInherited;
  }

  @ViewChild('manageCountryBtn') private manageCountryBtn: ElementRef;

  private countriesHash: Object;
  private _countries: Country[];
  private _restrictedCountries: string[] = [];
  private _restrictedCountriesInherited: string[] = [];
  private _countriesType;
  private _entitySettings: EntitySettingsModel;

  @Input()
  set countries(countries: Country[]) {
    this._countries = countries;
    this.countriesHash = this._countries.reduce(codeArrayToObjectReducer, {});
  }

  get countries(): Country[] {
    return this._countries;
  }

  private readonly destroyed$ = new Subject<void>();
  constructor(private countryService: CountryService<Country>,
              private entityService: EntityService<Entity>,
              private dialog: MatDialog,
              private notifications: SwuiNotificationsService,
              private translate: TranslateService,
              private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
              private cdr: ChangeDetectorRef,
  ) {
  }

  ngOnInit() {
    this.setRowActions();
    this.refreshData();
    this.countryService.getList().pipe(
      map((countries: Country[]) => countries.map(country => country.code)),
      take(1)
    ).subscribe((countries: string[]) => this.allAvailableCountries = countries);

    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((filterValue: string) => {
      this.dataSource.filter = filterValue?.trim().toLowerCase();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showCountryModal($event: Event) {
    this.searchControl.reset();
    $event.preventDefault();
    const source = this.dialog.open(MatCountryDialogComponent, {
      disableClose: true,
      width: '500px',
      data: {
        countriesType: this.countriesType,
        entity: this.entity,
        countries: this.countries,
        restrictedCountries: this.restrictedCountries.length ? this.restrictedCountries : this.restrictedCountriesInherited,
        allAvailableCountries: this.allAvailableCountries,
        buildHash: true
      }
    }).afterClosed();

    if (this.countriesType === 'allowed') {
      source.pipe(
        filter((data: { entity: Entity, selected: string[] }) => !!data && typeof data.entity !== 'undefined'),
        map(({ entity, selected }) => {
          const updated = new Entity(entity);
          updated.countries = [...selected];
          return updated;
        }),
        switchMap((entity: Entity) => this.entityService.updateEntityItem(entity)),
        tap((entity: Entity) => this.entity.update(entity)),
        tap(() => this.notifications.success(
          this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountriesAdded'))),
        finalize(() => this.refreshData()),
        take(1)
      ).subscribe();
    } else if (this.countriesType === 'restricted') {
      source.pipe(
        filter((data: { entity: Entity, selected: string[] }) => !!data && typeof data.entity !== 'undefined'),
        switchMap(({ entity, selected }) => {
          selected = selected.length ? selected : null;
          return this.entitySettingsService.patchSettings({ restrictedCountries: selected }, entity.path);
        }),
        tap((settings: EntitySettingsModel) => this._entitySettings = settings),
        switchMap(() => this.entitySettingsService.getSettings(this.entity.path, true)),
        tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountriesAdded'))),
        finalize(() => this.refreshData()),
        take(1)
      ).subscribe((ownEntitySettings: EntitySettingsModel) => {
        this.restrictedCountries = ownEntitySettings?.restrictedCountries || [];
        this.restrictedCountriesInherited = this._entitySettings?.restrictedCountries || [];
      });
    }
  }

  setDefaultCountry(defaultCountry: string) {
    this.entity.defaultCountry = defaultCountry;
    this.entityService.updateEntityItem(this.entity)
      .pipe(
        take(1),
        finalize(() => this.refreshData())
      ).subscribe((data) => {
      this.entity.update(data);
    });
  }

  removeCountryModal(countryCode: string) {
    const removeDialog = this.dialog.open(RemoveConfirmDialogComponent, {
      width: '500px',
      data: { removeCode: countryCode },
      disableClose: true
    }).afterClosed();
    if (this.countriesType === 'allowed') {
      removeDialog.pipe(
        filter(code => !!code && code in this.countriesHash),
        mergeMap((code) => {
          return this.countryService.deleteCountry(this.entity.path, code);
        }),
        tap(() => this.notifications.success(
          this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountryRemoved'))
        ),
        take(1),
        finalize(() => this.refreshData())
      ).subscribe(() => this.entity.countries = this.entity.countries.filter(item => item !== countryCode));
    } else if (this.countriesType === 'restricted') {
      removeDialog.pipe(
        filter(code => !!code && code in this.countriesHash),
        switchMap((code: string) => {
          let removed = this.restrictedCountries.filter(country => country !== code);
          removed = removed.length ? removed : null;
          return this.entitySettingsService.patchSettings({ restrictedCountries: removed }, this.entity.path);
        }),
        tap((settings: EntitySettingsModel) => this._entitySettings = settings),
        switchMap(() => this.entitySettingsService.getSettings(this.entity.path, true)),
        tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountryRemoved'))),
        take(1),
        finalize(() => this.refreshData())
      ).subscribe((ownEntitySettings: EntitySettingsModel) => {
        this.restrictedCountries = ownEntitySettings?.restrictedCountries || [];
        this.restrictedCountriesInherited = this._entitySettings?.restrictedCountries || [];
      });
    }
  }

  getCountry(code: string): Country {
    return this.countriesHash[code];
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  showRowActions(country: EntityCountry): boolean {
    return !this.disabledList && !country.isInherited;
  }

  private refreshData() {
    const countriesList = this.countriesType === 'allowed' ?
      this.entity.countries : this.restrictedCountries.length ? this.restrictedCountries : this.restrictedCountriesInherited;

    const countries = pushDefaultToStart(countriesList, this.entity?.defaultCountry);
    const data: EntityCountry[] = countries.map((code) => (<EntityCountry>{
      code,
      displayName: this.getCountry(code) ? this.getCountry(code).displayName : '',
      isDefault: this.countriesType === 'allowed' ? this.entity.defaultCountry === code : false,
      isInherited: this.countriesType === 'restricted' && !this.restrictedCountries.length && this.restrictedCountriesInherited.length > 0
    }));
    this.unFocusButton();

    this.dataSource = new MatTableDataSource(data);
    this.cdr.detectChanges();
  }

  private setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.actionDelete',
        icon: 'delete',
        inMenu: true,
        fn: ({ code }) => this.removeCountryModal(code),
        canActivateFn: () => !this.disabledList && !this.disableDelete
      })
    ];

    if (this.countriesType === 'allowed') {
      this.rowActions.push(new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.setDefault',
        icon: 'home',
        fn: ({ code }) => this.setDefaultCountry(code),
        canActivateFn: ({ isDefault }) => !isDefault && !this.disabledList,
      }));
    }
  }

  private unFocusButton() {
    if (this.manageCountryBtn) {
      this.manageCountryBtn['_elementRef'].nativeElement
        .classList.remove('cdk-program-focused');
    }
  }
}
