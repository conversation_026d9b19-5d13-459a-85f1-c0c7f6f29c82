import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SwBrowserTitleService } from '@skywind-group/lib-swui';
import { PERMISSIONS_LIST } from '../../../../app.constants';
import { AvailableProvidersResolver } from '../../../../common/services/resolvers/availableProviders.resolver';
import { BriefResolver } from '../../../../common/services/resolvers/brief.resolver';
import { EntitySettingsResolver } from '../../../../common/services/resolvers/entity-settings.resolver';
import { ActiveShortStructureResolver, ShortStructureResolver } from '../../../../common/services/resolvers/structure.resolver';
import { SetupEntityDetailsResolver } from './resolvers/setup-entity-details.resolver';
import { SetupEntityParentResolver } from './resolvers/setup-entity-parent.resolver';

import { SetupEntityComponent } from './setup-entity.component';
import { SetupEntityGuard } from './setup-entity.guard';


const children = [
  {
    path: 'regional',
    loadChildren: () => import('./tab-regional/tab-regional.module').then(m => m.TabRegionalModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Regional'
    }
  },
  {
    path: 'users',
    loadChildren: () => import('./tab-users/tab-users.module').then(m => m.TabUsersModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Users'
    }
  },
  {
    path: 'notifications',
    loadChildren: () => import('./tab-notifications/tab-notifications.module').then(m => m.TabNotificationsModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Notifications'
    }
  },
  {
    path: 'games',
    loadChildren: () => import('./tab-games/tab-games.module').then(m => m.TabGamesModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Games'
    },
  },
  {
    path: '2fa',
    loadChildren: () => import('./tab-2fa/tab-2fa.module').then(m => m.Tab2FaModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: '2FA'
    }
  },
  {
    path: 'domains',
    loadChildren: () => import('./tab-domains/tab-domains.module').then(m => m.TabDomainsModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Domains'
    }
  },
  {
    path: 'players',
    loadChildren: () => import('./tab-players/tab-players.module').then(m => m.TabPlayersModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Players'
    }
  },
  {
    path: 'engagement',
    loadChildren: () => import('./tab-engagement/tab-engagement.module').then(m => m.TabEngagementModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Engagement'
    }
  },
  {
    path: 'additional',
    loadChildren: () => import('./tab-additional/tab-additional.module').then(m => m.TabAdditionalModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Additional'
    }
  },
  {
    path: 'game-limits',
    loadChildren: () => import('./tab-game-limits/tab-game-limits.module').then(m => m.TabGameLimitsModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Game Limits'
    }
  },
  {
    path: 'game-group',
    loadChildren: () => import('./tab-game-group/tab-game-group.module').then(m => m.TabGameGroupModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Game Group'
    }
  },
  {
    path: 'game-group-filters',
    loadChildren: () => import('./tab-game-group-filters/tab-game-group-filters.module').then(m => m.TabGameGroupFiltersModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Game Group Filters'
    }
  },
  {
    path: 'rtp-reducer',
    loadChildren: () => import('./tab-rtp-reducer/tab-rtp-reducer.module').then(m => m.TabRtpReducerModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'RTP Reducer'
    }
  },
  {
    path: 'email-template',
    loadChildren: () => import('./tab-email-templates/tab-email-templates.module').then(m => m.TabEmailTemplatesModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Email Template'
    }
  },
  {
    path: 'test-players',
    loadChildren: () => import('./tab-test-players/tab-test-players.module').then(m => m.TabTestPlayersModule),
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
      title: 'Test Players'
    }
  }
];

const routes: Routes = [
  {
    path: 'setup/p',
    component: SetupEntityComponent,
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
    },
    canActivate: [SetupEntityGuard],
    canActivateChild: [SetupEntityGuard],
    children,
    resolve: {
      entity: BriefResolver,
      parent: SetupEntityParentResolver,
      brief: BriefResolver,
      entitySettings: EntitySettingsResolver,
      shortStructure: ShortStructureResolver,
      activeShortStructure: ActiveShortStructureResolver,
    }
  },
  {
    path: 'setup/:path/p',
    canActivate: [SetupEntityGuard],
    canActivateChild: [SetupEntityGuard],
    component: SetupEntityComponent,
    data: {
      permissions: PERMISSIONS_LIST.ENTITY,
    },
    children,
    resolve: {
      entity: SetupEntityDetailsResolver,
      parent: SetupEntityParentResolver,
      brief: BriefResolver,
      entitySettings: EntitySettingsResolver,
      shortStructure: ShortStructureResolver,
      availableProviders: AvailableProvidersResolver,
      activeShortStructure: ActiveShortStructureResolver,
    }
  },
  {
    path: 'move',
    loadChildren: () => import('../move-entity/move-entity.module').then(m => m.MoveEntityModule)
  },
  {
    path: 'cascade-games',
    loadChildren: () => import('../cascade-games/cascade-games.module').then(m => m.CascadeGamesModule)
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [
    SwBrowserTitleService,
    SetupEntityDetailsResolver,
    SetupEntityParentResolver,
    EntitySettingsResolver,
    BriefResolver,
    ShortStructureResolver,
    AvailableProvidersResolver,
    ActiveShortStructureResolver
  ]
})
export class EntitiesRoutingModule {
}
