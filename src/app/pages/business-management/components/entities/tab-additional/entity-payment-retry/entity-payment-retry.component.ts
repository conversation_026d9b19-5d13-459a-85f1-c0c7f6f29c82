import { Component, Input } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { switchMap } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';

import { Entity } from '../../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';
import { PERMISSIONS_LIST } from '../../../../../../app.constants';

export interface EntityPaymentRetry {
  maxPaymentRetryAttempts: number;
  minPaymentRetryTimeout: number;
}

@Component({
  selector: 'entity-payment-retry',
  templateUrl: './entity-payment-retry.component.html',
  styleUrls: ['./entity-payment-retry.component.scss']
})

export class EntityPaymentRetryComponent {

  @Input()
  public set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
  }

  public get entity(): Entity {
    return this._entity;
  }

  @Input()
  public set settings( value: EntitySettingsModel ) {
    if (!value) return;
    this._settings = value;
    this.patchForm(value);
  }

  public get settings(): EntitySettingsModel {
    return this._settings;
  }

  public form: FormGroup;
  public submitted: boolean;

  public readonly allowedEdit: boolean;

  private _entity: Entity;
  private _settings: EntitySettingsModel;

  constructor( private fb: FormBuilder,
               private service: EntitySettingsService<EntitySettingsModel>,
               private translate: TranslateService,
               private notifications: SwuiNotificationsService,
               authService: SwHubAuthService
  ) {
    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.ENTITY_SETTINGS);
    this.form = this.fb.group({
      maxPaymentRetryAttempts: [{ value: '', disabled: !this.allowedEdit }],
      minPaymentRetryTimeout: [{ value: '', disabled: !this.allowedEdit }]
    });
  }

  onSubmit() {
    this.submitted = true;
    if (this.form.valid) {
      this.service.patchSettings(this.processForm(this.form.value), this.entity.path).pipe(
        switchMap(() => this.translate.get('ENTITY_SETUP.ADDITIONAL.notificationPaymentRetrySettings'))
      )
      .subscribe(message => {
        this.notifications.success(message, '');
      });
    }
  }
  private patchForm(value: EntitySettingsModel): void {
    const paymentRetry = {
      maxPaymentRetryAttempts: value && value.maxPaymentRetryAttempts ? value.maxPaymentRetryAttempts : 0,
      minPaymentRetryTimeout: value && value.minPaymentRetryTimeout ? value.minPaymentRetryTimeout : 0,
    };
    this.form.patchValue(paymentRetry);
  }

  private processForm(formValue: EntityPaymentRetry): EntityPaymentRetry {
    Object.keys(formValue).forEach(key => {
      if (formValue[key] === '' || formValue[key] === 0 || formValue[key] === undefined) {
        formValue[key] = null;
      }
    });
    return formValue;
  }
}
