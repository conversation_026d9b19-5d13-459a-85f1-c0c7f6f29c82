<form [formGroup]="form" class="entity-game-retry">
  <div class="entity-game-retry__row">
    <mat-form-field appearance="outline">
      <mat-label>{{'ENTITY_SETUP.ADDITIONAL.maxPaymentRetryAttempts' | translate}}</mat-label>
      <input matInput type="number" step="1" min="0" formControlName="maxPaymentRetryAttempts">
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label>{{'ENTITY_SETUP.ADDITIONAL.minPaymentRetryTimeout' | translate}}</mat-label>
      <input matInput type="number" step="1" min="0" formControlName="minPaymentRetryTimeout">
    </mat-form-field>
  </div>
  <div class="entity-game-retry__footer" *ngIf="allowedEdit">
    <button mat-stroked-button color="primary" (click)="onSubmit()">
      {{'ALL.save' | translate}}
    </button>
  </div>
</form>
