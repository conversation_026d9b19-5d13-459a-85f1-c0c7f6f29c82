.additional-tables-row {
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 32px;
  &__header {
    height: 53px;
    display: flex;
    align-items: center;
    padding: 4px 16px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-bottom: none;
    background: #fff;
    button {
      margin-left: auto
    }
  }
  &__body {
    padding: 4px 16px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-top: none;
    background: #fff;
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
  }
  &__item {
    flex: 1;
    max-width: 600px;
  }
}
