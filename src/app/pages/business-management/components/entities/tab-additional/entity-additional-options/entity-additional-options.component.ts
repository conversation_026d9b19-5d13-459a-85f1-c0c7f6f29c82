import { Component, Input } from '@angular/core';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';

import { Entity } from '../../../../../../common/models/entity.model';


@Component({
  selector: 'entity-additional-options',
  templateUrl: './entity-additional-options.component.html',
  styleUrls: ['./entity-additional-options.component.scss']
})

export class EntityAdditionalOptionsComponent {

  @Input()
  public set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
  }

  public get entity(): Entity {
    return this._entity;
  }

  @Input()
  public set settings( value: EntitySettingsModel ) {
    if (!value) return;
    this._settings = value;
  }

  public get settings(): EntitySettingsModel {
    return this._settings;
  }

  private _entity: Entity;
  private _settings: EntitySettingsModel;
}
