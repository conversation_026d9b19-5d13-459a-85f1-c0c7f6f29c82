<div class="additional-tables-row" style="margin-top: 32px">
  <div class="additional-tables-row__item">
    <div class="additional-tables-row__header">
      <div class="additional-tables-row__title">
        {{'ENTITY_SETUP.ADDITIONAL.paymentRetrySettings' | translate}}
      </div>
    </div>
    <div class="additional-tables-row__body">
      <entity-payment-retry [entity]="entity" [settings]="settings"></entity-payment-retry>
    </div>
  </div>

  <div class="additional-tables-row__item" *ngIf="entity.isMerchant">
    <div class="additional-tables-row__header">
      <div class="additional-tables-row__title">
        {{'ENTITY_SETUP.ADDITIONAL.gameLogoutOptions' | translate}}
      </div>
    </div>
    <div class="additional-tables-row__body">
      <entity-game-logout [entity]="entity"></entity-game-logout>
    </div>
  </div>

  <div class="additional-tables-row__item" *ngIf="entity.isMerchant">
    <div class="additional-tables-row__header">
      <div class="additional-tables-row__title">
        {{'ENTITY_SETUP.PROXY.merchantParams' | translate}}
      </div>
    </div>
    <div class="additional-tables-row__body">
      <entity-merchant-params [entity]="entity"></entity-merchant-params>
    </div>
  </div>
</div>





