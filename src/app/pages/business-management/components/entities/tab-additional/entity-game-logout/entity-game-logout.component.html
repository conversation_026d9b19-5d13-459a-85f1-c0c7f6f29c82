<div [formGroup]="form" class="entity-game-logout">
  <div class="entity-game-logout__row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.ADDITIONAL.selectType' | translate }}</mat-label>
      <mat-select formControlName="type">
        <mat-option *ngFor="let item of gameLogoutTypeOptions" [value]="item.id" [disabled]="item.disabled">
          {{ item.text | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="entity-game-logout__row">
    <mat-form-field appearance="outline">
      <mat-label>{{'ENTITY_SETUP.ADDITIONAL.maxRetryAttempts' | translate}}</mat-label>
      <input matInput type="number" step="1" formControlName="maxRetryAttempts" min="0">
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{'ENTITY_SETUP.ADDITIONAL.maxSessionTimeout' | translate}}</mat-label>
      <input matInput type="number" step="1" formControlName="maxSessionTimeout" min="0">
    </mat-form-field>
  </div>

  <div class="entity-game-logout__footer" *ngIf="allowedEdit">
    <button mat-stroked-button color="primary" [disabled]="this.loading" (click)="onSubmit()">
      <i *ngIf="loading" class="icon-spinner4 spinner"></i>
      {{'ALL.save' | translate}}
    </button>
  </div>
</div>
