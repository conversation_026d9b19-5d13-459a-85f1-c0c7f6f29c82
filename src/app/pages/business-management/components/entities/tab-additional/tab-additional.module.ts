import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { TouchspinModule } from '../../../../../common/components/touchspin/touchspin.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EntityAdditionalOptionsComponent } from './entity-additional-options/entity-additional-options.component';
import { EntityGameLogoutComponent } from './entity-game-logout/entity-game-logout.component';
import { EntityPaymentRetryComponent } from './entity-payment-retry/entity-payment-retry.component';
import { TabAdditionalComponent } from './tab-additional.component';

import { TabAdditionalRoutingModule } from './tab-additional.routing';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { EntityMerchantParamsComponent } from './entity-merchant-params/entity-merchant-params.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';


@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TranslateModule.forChild(),
        TabAdditionalRoutingModule,
        TouchspinModule,
        MatDividerModule,
        MatFormFieldModule,
        MatSelectModule,
        MatInputModule,
        MatButtonModule,
        MatSlideToggleModule,
        TrimInputValueModule,
    ],
  exports: [],
  declarations: [
    TabAdditionalComponent,
    EntityAdditionalOptionsComponent,
    EntityPaymentRetryComponent,
    EntityGameLogoutComponent,
    EntityMerchantParamsComponent,
  ],
  providers: [],
})
export class TabAdditionalModule {
}
