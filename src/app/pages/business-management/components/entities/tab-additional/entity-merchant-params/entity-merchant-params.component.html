<div class="entity-merchant-params">
  <div [formGroup]="form" class="entity-merchant-params__form">
    <div class="entity-merchant-params__row">
      <mat-slide-toggle formControlName="sameUrlForTerminalLoginAndTicket">
        {{'MERCHANT.PARAMETERS.sameUrlForTerminalLoginAndTicket' | translate}}
      </mat-slide-toggle>
    </div>

    <div class="entity-merchant-params__footer" *ngIf="allowedEdit">
      <button mat-stroked-button color="primary" [disabled]="loading" (click)="onSubmit()">
        <i *ngIf="loading" class="icon-spinner4 spinner"></i>
        {{'ALL.save' | translate}}
      </button>
    </div>
  </div>

  <div class="entity-merchant-params__additional" *ngIf="allowedEdit && filteredParams.length > 0">
    <h4 class="entity-merchant-params__additional-title">Additional Merchant Parameters</h4>
    <div class="entity-merchant-params__additional-fields">
      <div class="entity-merchant-params__row" *ngFor="let filteredParam of filteredParams">
        <mat-form-field appearance="outline" class="entity-merchant-params__readonly-field">
          <mat-label>{{ filteredParam.key }}</mat-label>
          <input matInput [value]="filteredParam.value || '-'" readonly>
        </mat-form-field>
      </div>
    </div>
  </div>
</div>
