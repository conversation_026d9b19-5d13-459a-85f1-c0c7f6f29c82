import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService, SwuiSelectOption } from '@skywind-group/lib-swui';
import { cloneDeep } from 'lodash';
import { forkJoin, Subject, timer } from 'rxjs';
import { switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';

export const paymentMethods: SwuiSelectOption[] = [
  {
    id: 'manual',
    text: 'Manual',
  },
  {
    id: 'credit',
    text: 'Credit API Payment',
  },
  {
    id: 'bonus',
    text: 'Bonus API Payment',
  },
];

@Component({
  selector: 'payment-settings',
  templateUrl: './payment-settings.component.html',
  styleUrls: ['./payment-settings.component.scss']
})
export class PaymentSettingsComponent implements OnInit, OnDestroy {

  @Input()
  set entity( val: Entity ) {
    if (!val) return;

    this._entity = val;
  }

  get entity(): Entity {
    return this._entity;
  }

  @Input() entitySettings: EntitySettingsModel;

  paymentMethodOptions: SwuiSelectOption[] = [];
  supportedPaymentMethodOptions: SwuiSelectOption[] = [];
  form: FormGroup;

  inheritedLabel = this.translate.instant('ENTITY_SETUP.ENGAGEMENT.PAYMENT_SETTINGS.inherited');
  readonly isSuperAdmin: boolean;

  readonly messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };

  private _entity: Entity;
  private readonly destroyed$ = new Subject<void>();
  private parentSupportedBonusPaymentMethodId: string;
  private parentSettings: EntitySettingsModel;
  private ownSettings: EntitySettingsModel;

  constructor( private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               authService: SwHubAuthService,
               formBuilder: FormBuilder
  ) {
    this.isSuperAdmin = authService.isSuperAdmin;

    this.form = formBuilder.group({
      paymentMethod: ['', Validators.required],
      supportedBonusPaymentMethod: ['', Validators.required]
    });
  }

  get paymentMethodControl(): FormControl {
    return this.form.get('paymentMethod') as FormControl;
  }

  get supportedPaymentMethodControl(): FormControl {
    return this.form.get('supportedBonusPaymentMethod') as FormControl;
  }

  ngOnInit() {
    this.supportedPaymentMethodControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.initPaymentMethodOptions(this.parentSettings?.bonusPaymentMethod, this.ownSettings?.bonusPaymentMethod);
      });

    forkJoin([
      this.entitySettingsService.getSettings(this.entity.path, true),
      this.entitySettingsService.getSettings(this.entity?.entityParent?.path)
    ])
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(
        ( [ownSettings, parentSettings] ) => {
          this.ownSettings = ownSettings;
          this.parentSettings = parentSettings;
          this.paymentMethodOptions = [];
          this.parentSupportedBonusPaymentMethodId = parentSettings?.supportedBonusPaymentMethod;
          this.initSupportedPaymentMethodOptions(parentSettings?.supportedBonusPaymentMethod, ownSettings?.supportedBonusPaymentMethod);

          if (!this.isSuperAdmin) {
            this.supportedPaymentMethodControl.disable({ emitEvent: false });
            this.paymentMethodControl.disable({ emitEvent: false });
          }
        });
  }

  initSupportedPaymentMethodOptions( parentBonusPaymentMethod: string, ownBonusPaymentMethod: string ) {
    this.supportedPaymentMethodOptions = cloneDeep(paymentMethods);

    let inheritedBonusPaymentMethod: SwuiSelectOption;

    let inheritedBonusPaymentMethodLabel = this.supportedPaymentMethodOptions.find(option => {
      return option.id === parentBonusPaymentMethod;
    })?.text;

    if (inheritedBonusPaymentMethodLabel) {
      inheritedBonusPaymentMethod = { id: 'inherit', text: inheritedBonusPaymentMethodLabel + this.inheritedLabel };

      this.supportedPaymentMethodOptions.push(inheritedBonusPaymentMethod);
    }

    if (this.entitySettings?.supportedBonusPaymentMethod) {
      const bonusPaymentMethod = !ownBonusPaymentMethod && inheritedBonusPaymentMethod ?
        inheritedBonusPaymentMethod.id :
        this.entitySettings.supportedBonusPaymentMethod;

      this.supportedPaymentMethodControl.patchValue(bonusPaymentMethod);
    }
  }

  initPaymentMethodOptions( parentBonusPaymentMethod: string, ownBonusPaymentMethod: string ) {
    this.paymentMethodOptions = cloneDeep(paymentMethods);

    let inheritedBonusPaymentMethod: SwuiSelectOption;

    let inheritedBonusPaymentMethodLabel = this.paymentMethodOptions.find(option => {
      return option.id === parentBonusPaymentMethod;
    })?.text;

    if (inheritedBonusPaymentMethodLabel && this.supportedPaymentMethodControl.value === 'inherit') {
      inheritedBonusPaymentMethod = { id: 'inherit', text: inheritedBonusPaymentMethodLabel + this.inheritedLabel };

      this.paymentMethodOptions = [...this.paymentMethodOptions, inheritedBonusPaymentMethod];
    }

    this.paymentMethodOptions.forEach(option => option.disabled = false);

    const supportedBonusPaymentMethod = this.supportedPaymentMethodControl.value === 'inherit'
      ? this.parentSupportedBonusPaymentMethodId
      : this.supportedPaymentMethodControl.value;

    switch (supportedBonusPaymentMethod) {
      case 'credit':
        this.paymentMethodOptions.find(option => option.id === 'bonus').disabled = true;
        break;
      case 'bonus':
        this.paymentMethodOptions.find(option => option.id === 'credit').disabled = true;
        break;
      default:
        this.paymentMethodOptions.find(option => option.id === 'credit').disabled = true;
        this.paymentMethodOptions.find(option => option.id === 'bonus').disabled = true;
        break;
    }

    if (this.paymentMethodControl.value) {
      timer(0)
        .pipe(take(1))
        .subscribe(() => {
          this.paymentMethodControl.patchValue(this.supportedPaymentMethodControl.value);
        });
    } else if (this.entitySettings?.bonusPaymentMethod) {
      const bonusPaymentMethod = !ownBonusPaymentMethod && inheritedBonusPaymentMethod ?
        inheritedBonusPaymentMethod.id :
        this.entitySettings.bonusPaymentMethod;

      this.paymentMethodControl.patchValue(bonusPaymentMethod);
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  save() {
    this.form.markAsTouched();

    if (!this.form.valid) {
      return;
    }

    const value = {
      bonusPaymentMethod: this.paymentMethodControl.value === 'inherit' ? null : this.paymentMethodControl.value,
      supportedBonusPaymentMethod: this.supportedPaymentMethodControl.value === 'inherit' ? null : this.supportedPaymentMethodControl.value,
      supportedPaymentMethod: null
    };

    this.entitySettingsService.patchSettings(value, this.entity.path)
      .pipe(
        switchMap(() => this.translate.get('ENTITY_SETUP.ENGAGEMENT.NOTIFICATIONS.paymentMethodChanged')),
        tap(( message: string ) => this.notifications.success(message, '')),
        takeUntil(this.destroyed$)
      ).subscribe(( updatedSettings ) => this.entitySettings = new EntitySettingsModel(updatedSettings));
  }
}
