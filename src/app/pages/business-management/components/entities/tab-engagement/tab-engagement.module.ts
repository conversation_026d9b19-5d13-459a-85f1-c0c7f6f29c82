import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { MatCurrencyRatesSetupModule } from '../../../../../common/components/mat-currency-rates-setup/mat-currency-rates-setup.module';
import { VerticalTabsModule } from '../../../../../common/components/vertical-tabs/vertical-tabs.module';
import { PaymentSettingsComponent } from './payment-settings/payment-settings.component';
import { TabEngagementComponent } from './tab-engagement.component';

import { TabEngagementRoutingModule } from './tab-engagement.routing';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule.forChild(),
    TabEngagementRoutingModule,
    MatFormFieldModule,
    MatInputModule,
    MatCurrencyRatesSetupModule,
    MatButtonModule,
    MatRadioModule,
    VerticalTabsModule,
    SwuiSelectModule,
    SwuiControlMessagesModule,
    ReactiveFormsModule,
  ],
  exports: [],
  declarations: [
    TabEngagementComponent,
    PaymentSettingsComponent,
  ],
  providers: [],
})
export class TabEngagementModule {
}
