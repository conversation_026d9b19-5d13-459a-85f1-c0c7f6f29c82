import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { Observable } from 'rxjs/Observable';
import { catchError, map, take } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../../common/components/bo-confirmation/bo-confirmation.component';

import { Entity } from '../../../../../common/models/entity.model';
import { EntityService } from '../../../../../common/services/entity.service';
import { EntityNotifications } from '../../../../../common/typings/entity-notifications';
import { SetupEntityService } from '../setup-entity.service';
import { ComponentCanDeactivate } from './hasUnsavedDataGuard/unsavedDataGuard';


@Component({
  selector: 'tab-notifications',
  templateUrl: './tab-notifications.component.html',
  styleUrls: ['./tab-notifications.component.scss']
})
export class TabNotificationsComponent implements ComponentCanDeactivate {
  readonly entity?: Entity;
  readonly notifications$: Observable<EntityNotifications | null>;
  private _isChangesSaved: boolean = false;

  constructor(service: EntityService<Entity>,
              { entity }: SetupEntityService,
              private readonly dialog: MatDialog,
              private readonly translate: TranslateService,
  ) {
    this.entity = entity;
    this.notifications$ = service.getInfo('notifications', this.entity.path).pipe(
      map((notifications: EntityNotifications) => {
        if (notifications) {
          delete notifications['_meta'];
          delete notifications['promotion'];
          if (!Object.keys(notifications)) {
            return {
              lowBalance: {
                emails: [],
                currencies: null
              },
              engagement: {
                emails: [],
                jackpot: null,
                tournament: null
              }
            };
          }
          return notifications;
        }

        return null;
      }),
      catchError(() => of(null))
    );
  }

  canDeactivate(): Observable<boolean> {
    return this._isChangesSaved ?
      this.dialog.open(BoConfirmationComponent, {
        width: '600px',
        disableClose: true,
        data: {
          message: this.translate.instant('ENTITY_SETUP.NOTIFICATIONS.changesUnsavedNotifications'),
        }
      }).afterClosed().pipe(
        take(1),
      ) : of(!this._isChangesSaved);
  }

  changesSaved(result: boolean) {
    this._isChangesSaved = result;
  }
}
