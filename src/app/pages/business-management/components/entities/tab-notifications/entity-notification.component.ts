import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { ReplaySubject, Subject } from 'rxjs';
import { switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { Entity } from '../../../../../common/models/entity.model';
import { EntityService } from '../../../../../common/services/entity.service';

import { EntityNotifications } from '../../../../../common/typings/entity-notifications';


@Component({
  selector: 'entity-notification',
  templateUrl: './entity-notification.component.html',
  styleUrls: ['./tab-notifications.component.scss']
})
export class EntityNotificationComponent {
  @Output() isChangesSaved: EventEmitter<boolean> = new EventEmitter();

  form: FormGroup;
  isChanged: boolean = false;
  private _entity: Entity;
  private _notifications$: ReplaySubject<EntityNotifications> = new ReplaySubject<EntityNotifications>(1);
  private _notifications: EntityNotifications;

  @Input()
  public set entity(value: Entity) {
    if (!value) return;
    this._entity = value;
  }

  public get entity(): Entity {
    return this._entity;
  }

  @Input()
  public set notifications(value: EntityNotifications) {
    if (!value) return;
    this._notifications$.next(value);
  }

  private readonly destroyed$ = new Subject<void>();

  constructor(private service: EntityService<Entity>,
              private notificationService: SwuiNotificationsService,
              private translateService: TranslateService,
              private fb: FormBuilder
  ) {
    this.initForm();
  }

  ngOnInit() {
    this._notifications$
      .pipe(
        take(1)
      ).subscribe((notifications: EntityNotifications) => {
        this._notifications = notifications;
        this.form.patchValue(notifications);
      });

    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(data => {
      this.isChanged = !this.deepEqual(this._notifications, data);
      this.isChangesSaved.emit(this.isChanged);
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  changesSaved(result: boolean) {
    this.isChangesSaved.emit(result);
  }

  sendData(): void {
    if (this.form.valid) {
      this.service.setInfo('notifications', this.entity.path, this.form.getRawValue())
        .pipe(
          tap(() => this.notificationService.success(this.translateService.instant('ENTITY_SETUP.NOTIFICATIONS.savedSuccess'), '')),
          switchMap(() => this.service.getInfo('notifications', this.entity.path)),
          takeUntil(this.destroyed$)
        ).subscribe((notifications: EntityNotifications) => {
        this._notifications$.next(notifications);
        this.isChanged = false;
        this.isChangesSaved.emit(this.isChanged);
      });
    }
  }

  get lowBalanceEmailsControl(): FormControl {
    return this.form.get('lowBalance').get('emails') as FormControl;
  }

  get lowBalanceCurrenciesControl(): FormControl {
    return this.form.get('lowBalance').get('currencies') as FormControl;
  }

  get engagementEmailsControl(): FormControl {
    return this.form.get('engagement').get('emails') as FormControl;
  }

  get engagementControl(): FormControl {
    return this.form.get('engagement').get('currencies') as FormControl;
  }

  get engagementJackpotControl(): FormControl {
    return this.form.get('engagement').get('jackpot') as FormControl;
  }

  get engagementTournamentControl(): FormControl {
    return this.form.get('engagement').get('tournament') as FormControl;
  }

  private initForm() {
    this.form = this.fb.group({
      lowBalance: this.fb.group({
        emails: [],
        currencies: []
      }),
      engagement: this.fb.group({
        emails: [null],
        jackpot: [],
        tournament: []
      })
    });
  }

  private deepEqual(object1: EntityNotifications, object2: EntityNotifications): boolean {
    const keys1 = Object.keys(object1);
    const keys2 = Object.keys(object2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (const key of keys1) {
      const val1 = object1[key];
      const val2 = object2[key];
      const areObjects = this.isObject(val1) && this.isObject(val2);
      if (
        areObjects && !this.deepEqual(val1, val2) ||
        !areObjects && val1 !== val2
      ) {
        return false;
      }
    }

    return true;
  }

  private isObject(object): boolean {
    return object != null && typeof object === 'object';
  }
}
