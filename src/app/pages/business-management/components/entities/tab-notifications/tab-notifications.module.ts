import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiCheckboxModule } from '@skywind-group/lib-swui';

import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { VerticalTabsModule } from '../../../../../common/components/vertical-tabs/vertical-tabs.module';
import { DisableIfNotAllowedModule } from '../../../../../common/directives/disable-if-not-allowed/disable-if-not-allowed-disable.module';
import { GameNotifyModule } from '../../../../gamehistory/components/game-notify/game-notify.module';
import { EntityNotificationCurrenciesListModule } from './entity-notification-currencies-list/entity-notification-currencies-list.module';
import { EntityNotificationEmailsListModule } from './entity-notification-emails-list/entity-notification-emails-list.module';
import { EntityNotificationReportPeriodModule } from './entity-notification-report-period/entity-notification-report-period.module';
import { EntityNotificationComponent } from './entity-notification.component';
import { UnsavedDataGuard } from './hasUnsavedDataGuard/unsavedDataGuard';
import { TabNotificationsComponent } from './tab-notifications.component';
import { TabNotificationsRoutingModule } from './tab-notifications.routing';


@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ControlMessagesModule,
    TranslateModule,
    DisableIfNotAllowedModule,
    TabNotificationsRoutingModule,

    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatTooltipModule,
    MatInputModule,
    MatCardModule,
    GameNotifyModule,
    MatTabsModule,
    SwuiCheckboxModule,
    MatDividerModule,
    VerticalTabsModule,
    EntityNotificationCurrenciesListModule,
    EntityNotificationReportPeriodModule,
    EntityNotificationEmailsListModule,
  ],
  declarations: [
    TabNotificationsComponent,
    EntityNotificationComponent,
  ],
  exports: [
    TabNotificationsComponent
  ],
  providers: [
    UnsavedDataGuard
  ]
})
export class TabNotificationsModule {
}
