<form [formGroup]="form">
<sw-vertical-tabs>
    <sw-vertical-tabs-item>
      <sw-vertical-tabs-label>
        {{'ENTITY_SETUP.NOTIFICATIONS.LOWBALANCE.tabTitle' | translate}}
      </sw-vertical-tabs-label>
      <sw-vertical-tabs-body>
        <div class="sw-es-tab">
          <div class="sw-es-tab__header">
            <div class="sw-es-tab__title">{{ 'ENTITY_SETUP.NOTIFICATIONS.LOWBALANCE.EMAIL.title' | translate}}</div>
          </div>
          <div class="sw-es-tab__body">
            <div class="notifications">
              <div class="reports">
                <div class="reports__text">{{'ENTITY_SETUP.NOTIFICATIONS.LOWBALANCE.EMAIL.description' | translate }}</div>
                <div formGroupName="lowBalance">
                  <entity-notification-emails-list [formControl]="lowBalanceEmailsControl"></entity-notification-emails-list>
                  <entity-notification-currencies-list [formControl]="lowBalanceCurrenciesControl" [currencies]="entity.currencies"></entity-notification-currencies-list>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="sw-es-tab__footer" *ngIf="isChanged">
          <button
            mat-flat-button
            color="primary"
            [disabled]="form.invalid"
            (click)="sendData()"
            class="mat-button-md">
            {{'ENTITY_SETUP.NOTIFICATIONS.save' | translate}}
          </button>
        </div>
      </sw-vertical-tabs-body>
    </sw-vertical-tabs-item>

    <sw-vertical-tabs-item>
      <sw-vertical-tabs-label>
        {{'ENTITY_SETUP.NOTIFICATIONS.ENGAGEMENT.tabTitle' | translate}}
      </sw-vertical-tabs-label>
      <sw-vertical-tabs-body>
        <div class="sw-es-tab">
          <div class="sw-es-tab__header">
            <div class="sw-es-tab__title">{{ 'ENTITY_SETUP.NOTIFICATIONS.ENGAGEMENT.title' | translate}}</div>
          </div>
          <div class="sw-es-tab__body">
            <div class="notifications">
              <div class="reports">
                <div class="reports__text">Select the reports you would like to receive email notifications for:</div>
                <div formGroupName="engagement">
                  <div class="reports__item">
                    <entity-notification-report-period
                      [formControl]="engagementJackpotControl"
                      [title]="'Must Win Jackpots winners report'">
                    </entity-notification-report-period>
                  </div>

                  <div class="reports__item">
                    <entity-notification-report-period
                      [formControl]="engagementTournamentControl"
                      [title]="'Tournaments, Lucky Envelopes winners report'">
                    </entity-notification-report-period>
                  </div>
                  <entity-notification-emails-list
                    [formControl]="engagementEmailsControl">
                  </entity-notification-emails-list>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="sw-es-tab__footer" *ngIf="isChanged">
          <button
            mat-flat-button
            color="primary"
            [disabled]="form.invalid"
            (click)="sendData()"
            class="mat-button-md">
            {{'ENTITY_SETUP.NOTIFICATIONS.save' | translate}}
          </button>
        </div>
      </sw-vertical-tabs-body>
    </sw-vertical-tabs-item>
</sw-vertical-tabs>
  </form>
