import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { UnsavedDataGuard } from './hasUnsavedDataGuard/unsavedDataGuard';
import { TabNotificationsComponent } from './tab-notifications.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: TabNotificationsComponent,
        canDeactivate: [UnsavedDataGuard]
      }
    ])
  ],
  exports: [
    RouterModule,
  ]
})
export class TabNotificationsRoutingModule {
}
