import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiCheckboxModule, SwuiControlMessagesModule, SwuiDatePickerModule, SwuiDateRangeModule } from '@skywind-group/lib-swui';
import { EntityNotificationReportPeriodComponent } from './entity-notification-report-period.component';


@NgModule({
  declarations: [EntityNotificationReportPeriodComponent],
  exports: [
    EntityNotificationReportPeriodComponent
  ],
    imports: [
        CommonModule,
        SwuiCheckboxModule,
        MatCheckboxModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatButtonModule,
        MatButtonToggleModule,
        SwuiDateRangeModule,
        SwuiControlMessagesModule,
        TranslateModule,
        SwuiDatePickerModule,
    ]
})
export class EntityNotificationReportPeriodModule {
}
