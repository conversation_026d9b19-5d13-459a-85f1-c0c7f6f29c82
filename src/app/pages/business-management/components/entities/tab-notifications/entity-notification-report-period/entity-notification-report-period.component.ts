import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors
} from '@angular/forms';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

interface NotificationReportPeriod {
  frequency: 'hourly' | 'daily' | 'weekly';
  range: string;
}

@Component({
  selector: 'entity-notification-report-period',
  templateUrl: './entity-notification-report-period.component.html',
  styleUrls: ['./entity-notification-report-period.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => EntityNotificationReportPeriodComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => EntityNotificationReportPeriodComponent),
      multi: true
    },
  ]
})
export class EntityNotificationReportPeriodComponent implements OnInit, ControlValueAccessor {
  @Input() title: string;

  form: FormGroup;
  scheduleControl: FormControl = new FormControl();
  minCalendarDate = moment().toISOString();
  frequency = [
    { id: 'hourly', text: 'Hourly' },
    { id: 'daily', text: 'Daily' },
    { id: 'weekly', text: 'Weekly' }
  ];

  onChange: (_: any) => void = (() => {
  });

  private readonly destroyed$ = new Subject<void>();

  constructor(private readonly fb: FormBuilder) {
    this.initForm();
  }

  ngOnInit(): void {
    this.scheduleControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((checked: boolean) => {
      if (!checked) {
        this.form.reset({
          frequency: 'hourly',
          range: {
            from: null,
            to: null
          }
        });
      } else {
        this.form.updateValueAndValidity();
      }
    });

    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(data => {
      if ('range' in data) {
        const { from, to } = data.range;
        data.range.from = from || null;
        data.range.to = to || null;
      }
      this.onChange(this.scheduleControl.value ? data : null);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onTouched: any = () => {
  }

  registerOnChange(fn: (_: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  validate(): ValidationErrors | null {
    return !this.scheduleControl.value || this.form.valid ? null : { invalidForm: { valid: false } };
  }

  writeValue(val: NotificationReportPeriod): void {
    if (!val) return;
    this.form.setValue(val);
    this.scheduleControl.setValue(true);
  }

  get rangeFromControl(): FormControl {
    return this.form.get('range').get('from') as FormControl;
  }

  get rangeToControl(): FormControl {
    return this.form.get('range').get('to') as FormControl;
  }

  private initForm() {
    this.form = this.fb.group({
      frequency: ['hourly'],
      range: this.fb.group({
          from: null,
          to: null
        })
    });
  }
}
