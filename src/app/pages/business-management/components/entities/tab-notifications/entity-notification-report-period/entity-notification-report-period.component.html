<div class="report-item">
  <mat-checkbox [formControl]="scheduleControl">
    {{title}}
  </mat-checkbox>
  <form [formGroup]="form">
    <ng-container *ngIf="scheduleControl.value">
      <div class="report-item__row">
        <div class="report-item__label">
          {{'ENTITY_SETUP.NOTIFICATIONS.emailFrequency' | translate }}
        </div>
        <mat-button-toggle-group formControlName="frequency" class="report-item__toggles">
          <mat-button-toggle *ngFor="let period of frequency" value="{{period.id}}">
            {{period.text}}
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>

      <div class="report-item__row">
        <div class="report-item__label">
          {{'ENTITY_SETUP.NOTIFICATIONS.sendEmailsFrom' | translate }}
        </div>
        <div formGroupName="range" class="report-item__field">
          <mat-form-field appearance="outline" class="validity-dates no-field-padding">
            <mat-icon matSuffix class="report-item__icon">date_range</mat-icon>
            <lib-swui-date-picker
              formControlName="from"
              [minDate]="minCalendarDate">
            </lib-swui-date-picker>
          </mat-form-field>
          <span style="margin-right: 8px; margin-left: 8px">{{'ENTITY_SETUP.NOTIFICATIONS.sendEmailsTo' | translate }}</span>
          <mat-form-field appearance="outline" class="validity-dates no-field-padding">
            <mat-icon matSuffix class="report-item__icon">date_range</mat-icon>
            <lib-swui-date-picker
              formControlName="to"
              [minDate]="minCalendarDate">
            </lib-swui-date-picker>
          </mat-form-field>
        </div>

      </div>
    </ng-container>
  </form>
</div>

