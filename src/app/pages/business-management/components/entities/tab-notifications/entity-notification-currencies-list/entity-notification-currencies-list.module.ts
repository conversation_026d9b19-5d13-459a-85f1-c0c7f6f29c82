import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule, SwuiIsControlInvalidModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EntityNotificationCurrenciesListComponent } from './entity-notification-currencies-list.component';



@NgModule({
  declarations: [EntityNotificationCurrenciesListComponent],
  exports: [EntityNotificationCurrenciesListComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        SwuiControlMessagesModule,
        MatFormFieldModule,
        MatInputModule,
        SwuiIsControlInvalidModule,
        MatTooltipModule,
        MatButtonModule,
        TranslateModule,
        MatIconModule,
        SwuiSelectModule,
        MatMenuModule,
        SwuiCurrencySymbolModule,
        TrimInputValueModule
    ]
})
export class EntityNotificationCurrenciesListModule { }
