import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { AbstractControl, ControlValueAccessor, FormArray, FormBuilder, FormControl, FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

import { FeatureCurrency } from '../../../../../../common/typings/entity-notifications';
import { transformCurrencyItem } from '../../../../../../common/core/currecy-transform';


@Component({
  selector: 'entity-notification-currencies-list',
  templateUrl: './entity-notification-currencies-list.component.html',
  styleUrls: ['./entity-notification-currencies-list.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => EntityNotificationCurrenciesListComponent),
      multi: true
    }
  ]
})
export class EntityNotificationCurrenciesListComponent implements OnInit, ControlValueAccessor {
  @Input()
  set currencies( val: string[] ) {
    if (!(val && Array.isArray(val))) {
      return;
    }
    this.processedCurrencies$.next(val.map(el => {
      return {
        id: el,
        text: el,
        disabled: false
      };
    }));
  }

  onChange: ( _: any ) => void = (() => {
  });

  filteredCurrencies: SwuiSelectOption[] = [];
  isDisabled = false;

  readonly form: FormGroup;
  readonly searchControl: FormControl = new FormControl();
  readonly processedCurrencies$ = new BehaviorSubject<SwuiSelectOption[]>([]);

  private readonly _destroyed$ = new Subject<void>();
  private _selectedCurrencies$ = new BehaviorSubject<FeatureCurrency[]>([]);

  constructor( private readonly fb: FormBuilder,
  ) {
    this.form = this.initForm();
    this.processedCurrencies$
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: SwuiSelectOption[] ) => {
        this.filteredCurrencies = [...val];
      });
  }

  ngOnInit(): void {
    this.currenciesArray.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(() => {
        const value = this.currenciesArray.getRawValue();
        this.currenciesArray.patchValue((value), { emitEvent: false });
        this.selectedCurrencies = value;
        const currencies = value.reduce(( res, curr ) => {
          return {
            ...res,
            ...curr
          };
        }, {});
        this.onChange(currencies);
      });

    this.searchControl.valueChanges
      .pipe(
        map<string, string>(searchString => searchString.toLowerCase()),
        takeUntil(this._destroyed$)
      )
      .subscribe(( search: string ) => {
        const found = this.processedCurrencies$.value.filter(( option: SwuiSelectOption ) => {
          return option.text && option.text.toLowerCase().indexOf(search) > -1;
        });
        this.filteredCurrencies = search ? found : this.processedCurrencies$.value;
      });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  initForm(): FormGroup {
    return this.fb.group({
      currencies: this.fb.array([])
    });
  }

  removeCurrency( i: number, currencyCode: string ) {
    this.currenciesArray.removeAt(i);
    const cur = this.processedCurrencies$.value.find(( el: SwuiSelectOption ) => el.id === currencyCode);
    if (cur) {
      cur.disabled = false;
    }
  }

  addCurrency( event: Event, currency: SwuiSelectOption ) {
    if (currency.disabled) {
      event.preventDefault();
      event.stopPropagation();
    } else {
      currency.disabled = true;
      this.currenciesArray.push(this.initArrayItem({ [currency.text]: { min: null } }));
    }
  }

  onCloseMenu() {
    this.filteredCurrencies = this.processedCurrencies$.value;
    this.searchControl.setValue('');
  }

  get currenciesArray(): FormArray {
    return this.form.get('currencies') as FormArray;
  }

  writeValue(val: FeatureCurrency): void {
    if (val) {
      const processedVal = Object.entries(val)
        .reduce((res, [key, value]) => {
          res.push({ [key]: value });

          return res;
        }, []);
      this.currenciesArray.clear();

      if (processedVal.length) {
        let selectedCurrencies = new Set();
        processedVal.forEach((item: FeatureCurrency) => {
          this.currenciesArray.push(this.initArrayItem(item));
          selectedCurrencies.add(Object.keys(item)[0]);
        });
        this.processedCurrencies$.value.forEach((el: SwuiSelectOption) => {
          if (selectedCurrencies.has(el.id)) {
            el.disabled = true;
          }
        });
      }
    }
  }

  onTouched: () => void = () => {
  }

  registerOnChange( fn: ( _: FeatureCurrency ) => void ) {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable() : this.form.enable();
  }

  stopPropagation( event: MouseEvent ) {
    event.stopPropagation();
  }

  getCurrencyCode( control: AbstractControl ): string {
    return Object.keys(control.value)[0];
  }

  getControlCurrencyCodeLabel( control: AbstractControl ): string {
    return this.getCurrencyCodeLabel(Object.keys(control.value)[0]);
  }

  getCurrencyCodeLabel( code: string ): string {
    return transformCurrencyItem(0, code).label;
  }

  private initArrayItem( val?: FeatureCurrency ): FormGroup {
    const key = Object.keys(val)[0];

    return this.fb.group({
      [key]:
        this.fb.group({
          min: [val ? val[key].min : null]
        })
    });
  }

  set selectedCurrencies( val: FeatureCurrency[] ) {
    this._selectedCurrencies$.next(val);
  }

  get selectedCurrencies(): FeatureCurrency[] {
    return this._selectedCurrencies$.value;
  }
}
