<div class="currencies">
  <div class="currencies__title">
    Min Balance
  </div>
  <form [formGroup]="form" *ngIf="!!currenciesArray.controls.length">
    <div formArrayName="currencies">
      <table class="sw-mat-table table-currencies">
        <tbody>
          <tr *ngFor="let item of currenciesArray.controls; let i=index" [formGroupName]="i">
            <td class="table-currencies__cell">
              {{getControlCurrencyCodeLabel(item)}}
            </td>
            <td class="table-currencies__cell table-currencies__cell--rate" [formGroupName]="getCurrencyCode(item)">
              <mat-form-field appearance="outline" class="no-field-padding table-currencies__input">
                <span matPrefix>{{getControlCurrencyCodeLabel(item) | currencySymbol}}</span>
                <input type="number" matInput min="0" formControlName="min">
              </mat-form-field>
            </td>
            <td class="table-currencies__cell">
              <button mat-icon-button (click)="removeCurrency(i, getCurrencyCode(item))" [disabled]="isDisabled">
                <mat-icon>close</mat-icon>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </form>
  <button
    *ngIf="!currenciesArray.controls.length"
    mat-flat-button
    color="primary"
    class="currencies__add mat-button-md"
    [disabled]="isDisabled"
    [matMenuTriggerFor]="currenciesMenu">
    <mat-icon>add</mat-icon>
    Add Currency
  </button>
  <mat-menu #currenciesMenu="matMenu" (close)="onCloseMenu()">
    <div class="cur-dropdown">
      <input trimValue
        [formControl]="searchControl"
        type="text"
        placeholder="Search"
        (click)="stopPropagation($event)"
        class="cur-dropdown__input">
      <div class="cur-dropdown__list">
        <div
          *ngFor="let currency of filteredCurrencies"
          class="cur-dropdown__item"
          [ngClass]="{'disabled': !!currency.disabled}"
          (click)="addCurrency($event, currency)">
          {{getCurrencyCodeLabel(currency.id) | uppercase}}
        </div>
      </div>
    </div>
  </mat-menu>
  <button
    *ngIf="!!currenciesArray.controls.length"
    mat-button
    color="primary"
    class="currencies__add mat-button-md"
    [disabled]="isDisabled"
    [matMenuTriggerFor]="currenciesMenu">
    <mat-icon>add</mat-icon>
    Add Currency
  </button>
</div>

