import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EntityNotificationEmailsListComponent } from './entity-notification-emails-list.component';



@NgModule({
    declarations: [EntityNotificationEmailsListComponent],
    exports: [
        EntityNotificationEmailsListComponent
    ],
    imports: [
        CommonModule,
        MatTooltipModule,
        MatButtonModule,
        MatIconModule,
        TranslateModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        MatInputModule,
        ControlMessagesModule,
        TrimInputValueModule
    ]
})
export class EntityNotificationEmailsListModule { }
