import { Component, forwardRef, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormArray, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validators
} from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ValidationService } from '../../../../../../common/services/validation.service';

@Component({
  selector: 'entity-notification-emails-list',
  templateUrl: './entity-notification-emails-list.component.html',
  styleUrls: ['./entity-notification-emails-list.component.scss'],
  providers: [
    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => EntityNotificationEmailsListComponent), multi: true },
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => EntityNotificationEmailsListComponent), multi: true },
  ]
})
export class EntityNotificationEmailsListComponent implements OnInit, ControlValueAccessor {
  form: FormGroup;

  _onChange: (_: any) => void = (() => {
  });

  private readonly destroyed$ = new Subject<void>();

  constructor(private readonly fb: FormBuilder) {
    this.form = this.fb.group({
      emails: this.fb.array([])
    });
  }

  _onTouched: any = () => {
  }

  writeValue(emails: string[] = []): void {
    emails?.forEach(item => {
      let control = this.initItem();
      control.setValue(item);
      this.emailsArray.push(control);
    });
  }

  registerOnChange(fn: (_: any) => void): void {
    this._onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this._onTouched = fn;
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  ngOnInit(): void {
    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(data => {
      this._onChange(data.emails);
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  addControl(event: Event): void {
    event.preventDefault();
    this.emailsArray.push(this.initItem());
  }

  removeControl(i: number): void {
    this.emailsArray.removeAt(i);
  }

  get emailsArray(): FormArray {
    return this.form.get('emails') as FormArray;
  }

  private initItem(): FormControl {
    return this.fb.control(null, {
      validators: [ValidationService.emailValidator, Validators.required]
    });
  }
}
