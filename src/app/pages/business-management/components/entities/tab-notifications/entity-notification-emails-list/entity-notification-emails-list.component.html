<form [formGroup]="form">
  <div class="notifications__list" formArrayName="emails">
    <div class="notifications__item" *ngFor="let email of emailsArray.controls; let i = index">
      <mat-form-field appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput trimValue type="text" class="no-field-padding" [formControl]="email">
        <mat-error><control-messages [control]="email"></control-messages></mat-error>
      </mat-form-field>
      <button
        style="padding-top: 3px"
        mat-icon-button
        (click)="removeControl(i)"
        matTooltip="{{'ENTITY_SETUP.NOTIFICATIONS.removeItem' | translate}}">
        <mat-icon>clear</mat-icon>
      </button>
    </div>
    <button
      *ngIf="!emailsArray.controls.length"
      mat-flat-button
      color="primary"
      class="mat-button-md"
      (click)="addControl($event)"
      matTooltip="{{'ENTITY_SETUP.NOTIFICATIONS.addNew' | translate}}">
      <mat-icon>add</mat-icon>
      Add Email
    </button>
    <button
      *ngIf="emailsArray.controls.length"
      mat-button
      color="primary"
      class="mat-button-md"
      (click)="addControl($event)"
      matTooltip="{{'ENTITY_SETUP.NOTIFICATIONS.addNew' | translate}}">
      <mat-icon>add</mat-icon>
      Add Email
    </button>
  </div>
</form>
