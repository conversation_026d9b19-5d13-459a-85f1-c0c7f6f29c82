import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { TabGameLimitsComponent } from './tab-game-limits.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: TabGameLimitsComponent,
        resolve: {
          currencies: CurrenciesResolver,
        }
      }
    ])
  ],
  exports: [
    RouterModule,
  ]
})
export class TabGameLimitsRoutingModule {

}
