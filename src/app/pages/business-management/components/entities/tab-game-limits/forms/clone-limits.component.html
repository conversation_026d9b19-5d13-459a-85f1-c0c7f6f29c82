<h2 mat-dialog-title>{{ 'ENTITY_SETUP.GAME_LIMITS.cloneLimitsTitle' | translate }}</h2>

<div fxLayout="row" style="display: block; width: 100%">
  <div fxFlex="50" fxLayoutAlign="start start" class="clone-info-box">
    <div class="info-box">
      <div>
        <h6 class="modal-title">
          {{ 'ENTITY_SETUP.GAME_LIMITS.from' | translate }}:
        </h6>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate }}</mat-label>
          <input matInput trimValue [value]="selectedGameGroupFrom" [disabled]="true">
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate }}</mat-label>
          <input matInput trimValue [value]="getGameName(selectedGameCodeFrom)" [disabled]="true">
        </mat-form-field>
      </div>
    </div>
  </div>
  <div fxFlex="50" fxLayoutAlign="start start" class="clone-info-box">
    <div class="info-box">
      <div>
        <h6 class="modal-title">
          {{ 'ENTITY_SETUP.GAME_LIMITS.to' | translate }}:
        </h6>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate }}:</mat-label>
          <lib-swui-select
            [placeholder]="'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate"
            [data]="gameGroupsSelectOptions"
            [showSearch]="true"
            [disableEmptyOption]="true"
            [formControl]="gameGroupToControl">
          </lib-swui-select>
          <mat-error>
            <lib-swui-control-messages
              [messages]="messageErrors"
              [control]="gameGroupToControl">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate }}:</mat-label>
          <lib-swui-select
            [placeholder]="'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate"
            [data]="gamesSelectOptions"
            [showSearch]="true"
            [disableEmptyOption]="true"
            [formControl]="gameToControl">
          </lib-swui-select>
          <mat-error>
            <lib-swui-control-messages
              [messages]="messageErrors"
              [control]="gameGroupToControl">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>
</div>

<mat-dialog-actions align="end">
  <button mat-button color="primary" class="margin-right4 mat-button-md"
          (click)="onNoClick()">{{ 'DIALOG.cancel' | translate }}</button>
  <button mat-flat-button color="primary"
          class="mat-button-md"
          [disabled]="cloneForm.invalid"
          (click)="onConfirmClick()">{{ 'DIALOG.save' | translate }}</button>
</mat-dialog-actions>
