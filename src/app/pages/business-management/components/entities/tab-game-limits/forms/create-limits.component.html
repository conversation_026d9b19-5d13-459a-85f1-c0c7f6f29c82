<h2 mat-dialog-title>{{ modalTitle | translate }}</h2>

<mat-dialog-content>
  <form [formGroup]="form">
    <div fxLayout="column" fxFlex="50" fxLayoutAlign="center center" class="margin-left24 margin-top12 display-table">
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.entityLabel' | translate }}</mat-label>
          <input matInput trimValue [value]="entityName" [disabled]="true">
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate }}:</mat-label>
          <lib-swui-select
            [placeholder]="'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate"
            [data]="gameGroupsSelectOptions"
            [showSearch]="true"
            [disableEmptyOption]="true"
            [formControl]="gameGroupControl">
          </lib-swui-select>
          <mat-error>
            <lib-swui-control-messages
              [messages]="messageErrors"
              [control]="gameGroupControl">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center"></div>
    </div>
    <div fxLayout="column" fxFlex="50" fxLayoutAlign="center center" class="margin-left12 margin-right24 margin-top12 display-table">
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate }}:</mat-label>
          <lib-swui-select
            [placeholder]="'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate"
            [data]="gamesSelectOptions"
            [showSearch]="true"
            [disableEmptyOption]="true"
            [formControl]="gameLabelControl">
          </lib-swui-select>
          <mat-error>
            <lib-swui-control-messages
              [messages]="messageErrors"
              [control]="gameLabelControl">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="start center">
        <mat-form-field appearance="outline" class="width100">
          <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.currencyLabel' | translate }}:</mat-label>
          <lib-swui-select
            [placeholder]="'ENTITY_SETUP.GAME_LIMITS.currencyLabel' | translate"
            [data]="currencySelectOptions"
            [showSearch]="true"
            [disableEmptyOption]="true"
            [formControl]="currencyControl">
          </lib-swui-select>
          <mat-error>
            <lib-swui-control-messages
              [messages]="messageErrors"
              [control]="currencyControl">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="end center">
        <button mat-button color="primary"
                [disabled]="!gameGroupControl.value || !gameLabelControl.value || !currencyControl.value"
                (click)="addCurrency(currencyControl.value)">
          {{ 'ENTITY_SETUP.GAME_LIMITS.addCurrency' | translate }}
        </button>
      </div>
    </div>
  </form>
</mat-dialog-content>
<div class="limits-body">
  <form [formGroup]="limitsForm">
    <ng-container formArrayName="items" *ngIf="limitsFormArray.controls.length; else emptyGrid">
      <mat-card-content class="accordion-body">
        <mat-expansion-panel *ngFor="let item of limitsFormArray.controls; let i=index;" expanded>
          <mat-expansion-panel-header>
            <div fxLayout="row" style="width: 100%">
              <mat-panel-title fxFlex="20">
                {{ item.get('currency').value }}
              </mat-panel-title>
              <mat-panel-description fxFlex="80">
                <ng-container *ngIf="limitsFormArray.controls[i].valid; else formValid">
                  <mat-icon color="primary">check_circle</mat-icon>
                  <span>The limits are correct</span>
                </ng-container>
                <ng-template #formValid>
                  <mat-icon color="warn">error_outline
                  </mat-icon>
                  <span>There are mistakes within</span>
                </ng-template>
              </mat-panel-description>
            </div>
          </mat-expansion-panel-header>

          <div *ngIf="limitsFormArray.controls[i].invalid" class="custom-error custom-error--error">
            <div fxLayout="row" fxLayoutAlign="start center" *ngIf="limitsFormArray.controls[i].get('stakeAll').errors">
              <div fxFlex="20" class="nowrap">
                <label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.coinBets' | translate }}</label>
              </div>
              <lib-swui-control-messages fxFlex="70"
                                         [messages]="messageErrors"
                                         [control]="limitsFormArray.controls[i].get('stakeAll')">
              </lib-swui-control-messages>
            </div>
            <div fxLayout="row" fxLayoutAlign="start center" *ngIf="limitsFormArray.controls[i].get('stakeDef').errors">
              <div fxFlex="20" class="nowrap">
                <label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.stakeDef' | translate }}</label>
              </div>
              <lib-swui-control-messages fxFlex="70"
                                         [messages]="messageErrors"
                                         [control]="limitsFormArray.controls[i].get('stakeDef')">
              </lib-swui-control-messages>
            </div>
          </div>

          <div fxLayout="row" fxLayoutAlign="start center">
            <mat-form-field appearance="outline" [formGroupName]="i" class="width100">
              <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.coinBets' | translate }}</mat-label>
              <textarea matInput trimValue formControlName="stakeAll" rows="3" type="text"></textarea>
            </mat-form-field>
          </div>

          <div fxLayout="row">
            <div fxLayout="column" fxFlex="50" fxLayoutAlign="end start" class="display-table">
              <div fxLayout="row" fxLayoutAlign="start center">
                <mat-form-field appearance="outline" class="width100">
                  <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.totalBetMultiplier' | translate }}:</mat-label>
                  <input matInput trimValue [value]="totalBetMultiplier" [disabled]="true">
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayoutAlign="start center">
                <mat-form-field appearance="outline" class="width100">
                  <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.minCoinBet' | translate }}:</mat-label>
                  <input matInput trimValue [value]="item.get('stakeMin').value" [disabled]="true">
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayoutAlign="start center">
                <mat-form-field appearance="outline" class="width100">
                  <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.minTotalBet' | translate }}:</mat-label>
                  <input matInput trimValue [value]="item.get('minTotalStake').value" [disabled]="true">
                </mat-form-field>
              </div>
            </div>
            <div fxLayout="column" fxFlex="50" fxLayoutAlign="end start" class="display-table margin-left12">
              <div fxLayout="row" fxLayoutAlign="start center">
                <mat-form-field appearance="outline" class="width100">
                  <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.maxCoinBet' | translate }}</mat-label>
                  <input matInput trimValue [value]="item.get('stakeMax').value" [disabled]="true">
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayoutAlign="start center">
                <mat-form-field appearance="outline" class="width100">
                  <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.maxTotalBet' | translate }}</mat-label>
                  <input matInput trimValue [value]="item.get('maxTotalStake').value" [disabled]="true">
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayoutAlign="start center">
                <mat-form-field appearance="outline" [formGroupName]="i" class="width100">
                  <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.stakeDef' | translate }}</mat-label>
                  <input matInput trimValue formControlName="stakeDef" type="text">
                </mat-form-field>
              </div>
            </div>
          </div>
          <mat-action-row>
            <div>
              <button mat-button color="primary"
                      [disabled]="isReadMode"
                      (click)="setCoinBets(item)">
                Set available coin bets
              </button>
            </div>
            <button mat-button color="primary"
                    [disabled]="isReadMode"
                    (click)="removeItem(item.get('currency').value)">
              <mat-icon>delete</mat-icon>
              {{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.remove' | translate }}
            </button>
          </mat-action-row>
        </mat-expansion-panel>
      </mat-card-content>
    </ng-container>
    <ng-template #emptyGrid>
      <h4>
        {{ 'COMPONENTS.GRID.EMPTY_LIST' | translate }}
      </h4>
    </ng-template>
  </form>
</div>

<mat-dialog-actions align="end">
  <button mat-button color="primary" class="margin-right4 mat-button-md"
          (click)="onNoClick()">{{ 'DIALOG.cancel' | translate }}</button>
  <button mat-flat-button color="primary"
          class="mat-button-md"
          [disabled]="isReadMode || limitsFormArray.invalid"
          (click)="onConfirmClick()">{{ 'DIALOG.save' | translate }}</button>
</mat-dialog-actions>
