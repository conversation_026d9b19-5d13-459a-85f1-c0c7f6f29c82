import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { Currency } from '../../../../../common/typings';
import { SetupEntityService } from '../setup-entity.service';

@Component({
  selector: 'tab-game-limits',
  templateUrl: 'tab-game-limits.component.html'
})

export class TabGameLimitsComponent {
  readonly entity?: Entity;
  readonly entitySettings?: EntitySettingsModel;
  readonly currencies?: Currency[];

  constructor(
    { snapshot: { data: { currencies } } }: ActivatedRoute,
    { entity, settings }: SetupEntityService,
  ) {
    this.entity = entity;
    this.entitySettings = settings;
    this.currencies = currencies;
  }
}
