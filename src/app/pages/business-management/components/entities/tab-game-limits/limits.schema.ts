import { SwuiGridField } from '@skywind-group/lib-swui';
import { GameLimits } from '../../../../../common/typings/game-limits';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'gameGroup',
    title: 'ENTITY_SETUP.GAME_LIMITS.TABLE.gameGroup',
    type: 'select',
    data: [],
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: any ) => row?.gamegroup?.name,
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
    showSearch: true,
  },
  {
    field: 'gameCode',
    title: 'ENTITY_SETUP.GAME_LIMITS.TABLE.game',
    type: 'select',
    data: [],
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: any ) => row?.game?.title,
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
    showSearch: true,
  },
  {
    field: 'currency',
    title: 'ENTITY_SETUP.GAME_LIMITS.TABLE.currency',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'list',
      valueFn: ( row: GameLimits ) => {
        return Object.keys(row.limits);
      },
      useTranslate: false
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'coinBets',
    title: 'ENTITY_SETUP.GAME_LIMITS.TABLE.coinBets',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'list',
      valueFn: ( row: any ) => {
        const limits = Object.entries(row.limits).map(( [currency, limit] ) => {
            return ({ ...limit as object, currency });
          }
        );

        return limits.reduce(( acc: string[], cur: any ) => {
          acc.push(cur?.stakeAll?.toString());
          return acc;
        }, []);
      },
      useTranslate: false
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
