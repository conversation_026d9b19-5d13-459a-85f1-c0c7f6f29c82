<lib-swui-schema-top-filter [schema]="filterSchema"></lib-swui-schema-top-filter>
<lib-swui-grid #grid
               [schema]="schema"
               [rowActions]="rowActions"
               [rowActionsColumnTitle]="''"
               [pageSize]="10">
  <div class="grid-head-button">
    <button mat-flat-button
            color="primary"
            (click)="showLimitsModal()"
            [disabled]="(grid.loading$ | async) || readOnly">
      {{ 'ENTITY_SETUP.GAME_LIMITS.addLimits' | translate }}
    </button>
  </div>
</lib-swui-grid>
