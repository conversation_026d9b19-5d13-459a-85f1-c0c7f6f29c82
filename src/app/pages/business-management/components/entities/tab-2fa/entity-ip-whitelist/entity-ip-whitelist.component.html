<div class="sec-block sec-block--whitelists">
  <div class="sec-block__header">
    <div class="sec-block__title">{{ 'ENTITY_SETUP.WHITELISTING.' + whitelistType + 'IpWhitelist' | translate }}</div>
    <a class="sec-block__button mat-button-md" (click)="onHistoryClick($event)">
      <mat-icon fontSet="material-icons-outline">view_timeline</mat-icon>
    </a>
  </div>
  <div class="sec-block__body">
    <lib-swui-grid
      #grid
      *ngIf="whitelist"
      [schema]="schema"
      [stickyHeader]="true"
      [pagination]="false"
      [columnsManagement]="false"
      [data]="whitelist.data"
      [bulkSelectionOnly]="true"
      [ignoreQueryParams]="true"
      [ignorePlainLink]="true"
      [rowActions]="rowActions"
      [bulkActions]="bulkActions"
      [disableRefreshAction]="true"
      [rowActionsColumnTitle]="''">
      <button mat-stroked-button color="warn" [ngClass]="{'btn-hidden':grid.selection.isEmpty()}"
              (click)="removeSelectedAddresses()">
        {{ 'ENTITY_SETUP.WHITELISTING.btnRemoveSelected' | translate }}
      </button>
      <download-csv [loading]="loadingCsv" (downloadCsv)="downloadCsv()"></download-csv>
    </lib-swui-grid>
  </div>
  <form [formGroup]="form" class="sec-block__form">
    <mat-form-field appearance="outline" style="width:100%">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.placeholderEnterIP' | translate }}</mat-label>
      <textarea matInput trimValue mat-autosize formControlName="address" required></textarea>
      <mat-error>
        <control-messages [control]="addressControl" [forceShow]="submitted"></control-messages>
      </mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline" style="width:100%">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.placeholderIssueId' | translate }}</mat-label>
      <input matInput trimValue type="text" formControlName="issueId" placeholder="ABC-1234">
      <mat-error>
        <control-messages [control]="issueIdControl" [forceShow]="submitted"></control-messages>
      </mat-error>
    </mat-form-field>
  </form>
  <div class="sec-block__footer">
    <button mat-stroked-button color="primary" (click)="addNewAddress()">
      {{ 'ENTITY_SETUP.WHITELISTING.btnAdd' | translate }}
    </button>
  </div>
</div>

