import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl } from '@angular/forms';
import { ValidationService } from '../../../../../../../common/services/validation.service';

interface RemoveConfirmDialogData {
  rows: string[];
}

@Component({
  selector: 'remove-confirm-dialog',
  templateUrl: 'remove-confirm-dialog.component.html'
})
export class RemoveConfirmDialogComponent {
  readonly issueIdControl = new FormControl('', ValidationService.issueIdValidator);

  constructor( @Inject(MAT_DIALOG_DATA) public data: RemoveConfirmDialogData,
               private readonly dialogRef: MatDialogRef<RemoveConfirmDialogComponent, string[]> ) {
  }

  doConfirm() {
    if (this.issueIdControl.valid) {
      this.dialogRef.close(this.issueIdControl.value);
    }
  }
}
