import { SwuiGridField } from '@skywind-group/lib-swui';
import { WhitelistIp } from '../../../../../../common/models/whitelist-ip.model';


export const SCHEMA: SwuiGridField[] = [
  {
    field: 'address',
    title: 'ENTITY_SETUP.WHITELISTING.gridSiteTitle',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    alignment: {
      th: 'full-width',
      td: 'full-width',
    },
    td: {
      type: 'calc',
      titleFn: ( row: WhitelistIp ) => {
        let title = row.address;
        if (row.isParentItem()) {
          title += ' (inherited)';
        }
        if (row.isDraftItem()) {
          title += ' (not saved)';
        }
        return title;
      },
      classFn: ( row ) => {
        let extraClasses;
        if (row.isDraftItem()) {
          extraClasses = 'text-warning';
        }
        return extraClasses;
      },
    }
  },
  {
    field: 'issueId',
    title: 'ACTIVITY_LOG.GRID.initiatorIssueId',
    type: 'issue',
    isList: true,
    isViewable: false,
    isSortable: false,
  },
];
