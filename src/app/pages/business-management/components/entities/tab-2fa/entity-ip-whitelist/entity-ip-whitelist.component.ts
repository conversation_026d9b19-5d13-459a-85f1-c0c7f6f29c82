import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { combineLatest, iif, of, Subject, throwError } from 'rxjs';
import { catchError, debounceTime, filter, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import {
  BulkAction,
  RowAction,
  SelectionRowAvailable, SwHubEntityService,
  SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN,
  SwuiGridComponent,
  SwuiGridField,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';

import { Entity } from '../../../../../../common/models/entity.model';
import { EntityWhitelistDetailed, Whitelist, WhitelistIp } from '../../../../../../common/models/whitelist-ip.model';
import { EntityService, IpWhitelistType } from '../../../../../../common/services/entity.service';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { SCHEMA } from './ip-whitelist.schema';
import { MatDialog } from '@angular/material/dialog';
import { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';
import { ActivityLogService } from '../../../../../users/components/activityLog/activityLog.service';
import { HttpParams } from '@angular/common/http';
import { Audit } from '../../../../../../common/typings';
import { Observable } from 'rxjs/Observable';
import moment from 'moment';
import { CsvSchema, CsvService } from '../../../../../../common/services/csv.service';
import { Router } from '@angular/router';


const rowAvailable: SelectionRowAvailable = {
  available: ( addr: WhitelistIp ) => addr.canBeDeleted()
};

@Component({
  selector: 'entity-ip-whitelist, [entity-ip-whitelist]',
  templateUrl: './entity-ip-whitelist.component.html',
  styleUrls: ['./entity-ip-whitelist.component.scss'],
  providers: [
    ActivityLogService,
    {
      provide: SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN,
      useValue: rowAvailable
    },
  ]
})
export class EntityIpWhitelistComponent implements OnInit, OnDestroy {
  @Input() entity?: Entity;
  @Input() whitelistType?: IpWhitelistType;

  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<WhitelistIp>;

  readonly addressControl = new FormControl('', [Validators.required, ValidationService.ipv4AddressMaskListValidator]);
  readonly issueIdControl = new FormControl({ value: '', disabled: true }, ValidationService.issueIdValidator);
  readonly form = new FormGroup({
    address: this.addressControl,
    issueId: this.issueIdControl
  });

  readonly schema: SwuiGridField[] = SCHEMA.filter(( { isList } ) => isList);
  readonly rowActions: RowAction[] = [new RowAction({
    icon: 'delete',
    title: '',
    inMenu: false,
    fn: ( { address }: WhitelistIp ) => this.removeAddresses([address]),
    availableFn: ( addr: WhitelistIp ) => addr.canBeDeleted(),
    canActivateFn: ( addr: WhitelistIp ) => addr.canBeDeleted()
  })];
  readonly bulkActions: BulkAction[] = [new BulkAction({
    title: 'Select Ip Address', // stub bulk action for selection availability,
  })];

  whitelist: Whitelist | null;
  submitted = false;
  loading = true;
  loadingCsv = false;

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly entityService: EntityService<Entity>,
               private readonly activityLogService: ActivityLogService,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly csvService: CsvService,
               private readonly dialog: MatDialog,
               private readonly router: Router,
               private readonly swHubEntityService: SwHubEntityService
  ) {
  }

  ngOnInit(): void {
    this.setWhitelist$().pipe(take(1)).subscribe();
    this.initForm();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  downloadCsv() {
    this.loadingCsv = true;
    const fileName = `Game launch IP Whitelist ${moment().format('YYYY-MM-DD HH:MM')}`;
    const schema: CsvSchema[] = [
      {
        name: 'entity',
        title: 'ENTITY_SETUP.WHITELISTING.entity',
        transform: () => this.entity.name,
      },
      {
        name: 'address',
        title: 'ENTITY_SETUP.WHITELISTING.gridSiteTitle',
        transform: function( title: string, row: WhitelistIp ) {
          return row.isParentItem() ? title + ' (inherited)' : title;
        },
      },
      {
        name: 'issueId',
        title: 'ACTIVITY_LOG.GRID.initiatorIssueId',
      },
    ];
    this.loadWhitelist$().pipe(
      catchError(( err ) => {
        this.loadingCsv = false;
        return throwError(err);
      }),
      take(1)
    ).subscribe(whitelist => {
      this.csvService.toCsv(schema, whitelist.data, fileName);
      this.loadingCsv = false;
    });
  }

  addNewAddress() {
    this.submitted = true;
    if (this.form.valid && this.whitelist) {
      const issueId = this.issueIdControl.value;
      iif(
        () => this.whitelist.isDraftWhitelist(),
        this.entityService.createWhitelistedIPs(this.whitelist.draftList, this.entity.path, this.whitelistType, issueId),
        this.entityService.updateWhitelistedIPs(this.whitelist.draftList, this.entity.path, this.whitelistType, issueId)
      ).pipe(
        switchMap(() => this.setWhitelist$()),
        take(1),
      ).subscribe(() => {
        this.notifications.success(this.translate.instant('ENTITY_SETUP.WHITELISTING.updateWhitelist'));
        this.form.reset();
        this.submitted = false;
      });
    }
  }

  removeSelectedAddresses() {
    this.removeAddresses(this.grid.selection.selected.map(( { address } ) => address));
  }

  onHistoryClick(event: MouseEvent) {
    event.preventDefault();
    this.swHubEntityService.use(this.entity.id, true);
    this.router.navigate(['/pages', 'users', 'activity-log'],
      {
        queryParams: {
          operation__contains: `/entities/${this.entity.path}/settings/ip-whitelist/bo`, sortOrder: 'DESC',
          'sortBy': 'ts', path: this.entity.path
        }
      });
  }

  private removeAddresses( rows: string[] ) {
    this.dialog.open(RemoveConfirmDialogComponent, {
      width: '600px',
      data: { rows },
      disableClose: true
    }).afterClosed().pipe(
      filter(response => response !== false),
      switchMap(issueId => this.entityService.removeWhitelistedIPs(rows, this.entity.path, this.whitelistType, issueId)),
      switchMap(() => this.setWhitelist$()),
      take(1)
    ).subscribe(() => {
      this.notifications.success(this.translate.instant('ENTITY_SETUP.WHITELISTING.removedWhitelist'));
    });
  }

  private setWhitelist$(): Observable<any> {
    this.loading = true;
    return this.loadWhitelist$().pipe(
      tap(whitelist => {
        this.whitelist = whitelist;
        this.grid?.selection?.clear();
        this.loading = false;
      })
    );
  }

  private loadWhitelist$(): Observable<Whitelist> {
    const path = this.entity.path;
    const params = new HttpParams({
      fromObject: {
        operation__contains: `/entities/${path}/settings/ip-whitelist/${this.whitelistType}`,
        sortOrder: 'DESC',
        sortBy: 'ts',
        limit: 10000
      }
    });
    return combineLatest([
      this.entityService.getWhitelistedIPs(path, this.whitelistType).pipe(
        catchError(() => of<EntityWhitelistDetailed>({ own: [] })),
      ),
      this.activityLogService.getGridData(params, { path }).pipe(
        map(( { body } ) => body),
        catchError(() => of<Audit[]>([])),
      )
    ]).pipe(
      map(( [whitelist, audits] ) => new Whitelist(whitelist, audits))
    );
  }

  private initForm() {
    this.addressControl.valueChanges.pipe(
      debounceTime(500),
      takeUntil(this.destroyed$)
    ).subscribe(( value ) => {
      if (this.whitelist === null) {
        this.whitelist = new Whitelist();
      }
      if (value && this.addressControl.valid) {
        this.whitelist.setDraftAddresses(value);
        this.issueIdControl.enable({ emitEvent: false });
      } else {
        if (value === '') {
          this.whitelist.setDraftAddresses('');
        }
        this.issueIdControl.disable({ emitEvent: false });
      }
    });
  }
}
