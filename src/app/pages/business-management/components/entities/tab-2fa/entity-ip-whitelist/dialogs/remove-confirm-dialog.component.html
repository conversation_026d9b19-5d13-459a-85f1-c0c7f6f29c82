<h2 mat-dialog-title="">
  {{'ENTITY_SETUP.WHITELISTING.removedWhitelistConfirmation' | translate}}

</h2>
<mat-dialog-content class="mat-typography">
  <mat-list>
    <mat-list-item *ngFor="let row of data.rows">
      <strong>{{ row }}</strong>
    </mat-list-item>
  </mat-list>
  <mat-form-field appearance="outline" style="width:100%">
    <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.placeholderIssueId' | translate }}</mat-label>
    <input matInput trimValue type="text" [formControl]="issueIdControl" placeholder="ABC-1234">
    <mat-error><control-messages [control]="issueIdControl"></control-messages></mat-error>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" [mat-dialog-close]="false">
    {{ 'ALL.decline' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="doConfirm()" [disabled]="issueIdControl.invalid">
    {{'ALL.confirm' | translate}}
  </button>
</mat-dialog-actions>
