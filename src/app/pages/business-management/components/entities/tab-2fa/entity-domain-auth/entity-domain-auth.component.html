<div class="sec-block">
  <div class="sec-block__header">
    <div class="sec-block__title">
      {{'ENTITY_SETUP.DOMAINS.formTitle' | translate}}
    </div>
  </div>
  <div class="sec-block__body">
    <mat-form-field style="width:100%" appearance="outline">
      <mat-label>{{'ENTITY_SETUP.DOMAINS.domains' | translate}}</mat-label>
      <textarea matInput trimValue class="form-control" rows="5" [formControl]="domainsFormControl"></textarea>
    </mat-form-field>

    <ng-container *ngFor="let domainControl of domainsFormArray.controls; let i = index">
      <mat-error *ngIf="domainControl.invalid" style="display:flex">
        <strong>{{domainControl.value}}</strong>:&nbsp;<control-messages [control]="domainControl" [forceShow]="true"></control-messages>
      </mat-error>
    </ng-container>
  </div>
  <div class="sec-block__footer">
    <button mat-stroked-button color="primary" (click)="saveDomain()" [disabled]="domainsFormArray?.invalid">
      {{'DIALOG.save' | translate}}
    </button>
  </div>
  <div class="sec-block__sub"*ngIf="!entity?.domains?.length">
    <div *ngIf="entity?.parentDomains?.length">
      <b>{{'ENTITY_SETUP.DOMAINS.parentDomains' | translate}}:</b> {{entity.parentDomains.join(', ')}}
    </div>
  </div>
</div>

