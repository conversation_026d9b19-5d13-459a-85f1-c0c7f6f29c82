import { Component, Input, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { debounceTime, first, switchMap } from 'rxjs/operators';

import { Entity } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { ValidationService } from '../../../../../../common/services/validation.service';


@Component({
  selector: 'entity-domain-auth',
  templateUrl: 'entity-domain-auth.component.html',
  styleUrls: ['entity-domain-auth.component.scss']
})

export class EntityDomainAuthComponent implements OnInit {

  domainsFormControl = new FormControl('');
  domainsFormArray: FormArray = this.fb.array([]);

  private _entity: Entity;

  @Input() set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
    if (this._entity.domains) {
      this.domainsFormControl.setValue(this._entity.domains.join('\n'));
    }
  }

   get entity(): Entity {
    return this._entity;
  }

  constructor(
    private fb: FormBuilder,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    private entityService: EntityService<Entity>,
  ) {
    this.initForm();
  }

  ngOnInit() {
  }

  initForm() {
    this.domainsFormControl.valueChanges
      .pipe(
        debounceTime(500)
      )
      // Once value in textarea changes populate FormArray
      .subscribe(newValue => {
        const domains = newValue.split('\n');
        if (domains && domains.length) {
          this.clearFormArray(this.domainsFormArray);
          domains.filter(domain => !!domain)
            .forEach(domain =>
              this.domainsFormArray.push(
                this.fb.control(domain, ValidationService.entityDomainValidator())
              )
            );
        }
      });
  }

  saveDomain(): void {
    this._entity.domains = this.domainsFormArray.value;

    this.entityService.updateEntityItem(this._entity)
      .pipe(
        first(),
        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.saved'))
      )
      .subscribe( message => this.notifications.success(message, ''));

  }

  private clearFormArray( formArray: FormArray ): void {
    while (formArray.controls.length) {
      formArray.removeAt(0);
    }
  }

}
