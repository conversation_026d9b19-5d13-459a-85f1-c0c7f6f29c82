import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';

import { EntityDomainAuthComponent } from './entity-domain-auth.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    ControlMessagesModule,
  ],
  declarations: [
    EntityDomainAuthComponent
  ],
  exports: [
    EntityDomainAuthComponent
  ],
  providers: [],
})
export class EntityDomainAuthModule {
}

