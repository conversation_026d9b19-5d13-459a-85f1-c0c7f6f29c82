$color-border: rgba(0, 0, 0, 0.12);

.sec-block {
  &__header {
    display: flex;
    align-items: center;
    height: 63px;
    padding: 8px 16px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border: 1px solid $color-border;
    border-bottom: none;
    background: #fff;
  }
  &__title {
    font-size: 18px;
    font-weight: 500;
  }
  &__button {
    margin-left: auto;
  }
  &__body {
    padding: 0;
    background: #fff;
    border-left: 1px solid $color-border;
    border-right: 1px solid $color-border;
    mat-error {
      font-size: 12px;
    }
  }
  &__footer {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px;
    background: #fff;
    border: 1px solid $color-border;
    border-top: none;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
  }
  &__form {
    margin: 16px 16px 0;
  }
}
