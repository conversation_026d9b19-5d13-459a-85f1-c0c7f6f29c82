<button mat-stroked-button [matMenuTriggerFor]="levelsMenu" [color]="getLevelColor(checkWebsiteWhitelistedLevel)"
        [matTooltip]="'ENTITY_SETUP.WHITELISTING.checkWebsiteWhitelisted' | translate">
  Level: {{ checkWebsiteWhitelistedLevel || 'Loading' }}
</button>
<mat-menu #levelsMenu>
  <button mat-menu-item *ngFor="let level of availableLevels"
          (click)="changeLevel(level)" style="text-transform: capitalize"
          [ngClass]="'mat-'+getLevelColor(checkWebsiteWhitelistedLevel)">
    <mat-icon>
      {{ checkWebsiteWhitelistedLevel === level ? 'radio_button_checked' : 'radio_button_unchecked' }}
    </mat-icon>
    <span class="level-label">{{ level }}</span>
  </button>
  <button mat-menu-item (click)="resetLevel()">
    <mat-icon>replay</mat-icon>reset
  </button>
</mat-menu>

<!--<ng-container *ngIf="false">-->
<!--  <div dropdown [insideClick]="true" placement="bottom" class="options-inline">-->
<!--    <div class="btn-group">-->
<!--      <button class="btn btn-xs btn-default" style="text-transform: capitalize;">-->
<!--        Level: <span class="text-bold" [ngClass]="{'text-warning-300': checkWebsiteWhitelistedLevel === 'warning',-->
<!--          'text-danger-300': checkWebsiteWhitelistedLevel === 'error'} ">-->
<!--          {{ checkWebsiteWhitelistedLevel || 'Loading' }}</span>-->
<!--      </button>-->
<!--      <button class="btn btn-xs btn-default dropdown-toggle" dropdownToggle>-->
<!--        <span class="caret"></span>-->
<!--      </button>-->
<!--    </div>-->

<!--    <ul *dropdownMenu class="dropdown-menu active" style="min-width:300px">-->
<!--      <li>-->
<!--        <span style="padding: 10px 15px">-->
<!--          {{ 'ENTITY_SETUP.WHITELISTING.checkWebsiteWhitelisted' | translate }}-->
<!--        </span>-->
<!--        <i class="icon-spinner spinner" [ngClass]="{'hidden':!levelChangeLoading}"></i>-->
<!--      </li>-->
<!--      <li>-->
<!--        <div class="btn-group btn-group-justified" data-toggle="buttons" style="padding: 10px 15px">-->
<!--          <label class="btn btn-xs" *ngFor="let level of availableLevels" style="text-transform: capitalize;"-->
<!--                 [ngClass]="checkLevelClasses(level)" >-->
<!--            <input type="radio" name="options" value="{{ level }}" (click)="changeLevel(level)"> {{ level }}-->
<!--          </label>-->
<!--          <label class="btn btn-xs bg-slate-400">-->
<!--            <input type="radio" name="reset" (click)="resetLevel()"> <i class="icon-reset"></i>Reset-->
<!--          </label>-->
<!--        </div>-->
<!--      </li>-->
<!--    </ul>-->
<!--  </div>-->

<!--</ng-container>-->
