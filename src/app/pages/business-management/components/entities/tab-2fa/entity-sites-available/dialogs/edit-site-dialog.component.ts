import { Component, Inject, OnInit } from '@angular/core';
import { A<PERSON>tractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { Site, SITE_STATUS_LIST_TRANSLATE } from '../../../../../../../common/models/site.model';


export interface EditSiteDialogData {
  site: Site;
}

@Component({
  selector: 'edit-site-dialog',
  templateUrl: './edit-site-dialog.component.html',
  styleUrls: ['./edit-site-dialog.component.scss']
})
export class EditSiteDialogComponent implements OnInit {

  site: Site;
  statuses = SITE_STATUS_LIST_TRANSLATE;

  form: FormGroup;
  submitted: boolean;

  constructor(
    public fb: FormBuilder,
    public dialogRef: MatDialogRef<EditSiteDialogComponent, Site>,
    @Inject(MAT_DIALOG_DATA) public data: EditSiteDialogData,
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.initSite();
  }

  submit() {
    this.submitted = true;
    this.form.markAllAsTouched();

    if (this.form.valid) {
      const site = { ...this.site };

      Object.entries<AbstractControl>(this.form.controls).forEach(( [name, control] ) => {
        if (control.dirty) {
          site[name] = control.value || null;
        }
      });

      site.isDefault = !!site.isDefault;

      if (this.site && this.site.status === this.form.get('status').value) {
        delete site['status'];
      }

      this.dialogRef.close(site);
    }
  }

  private initForm() {
    this.form = this.fb.group({
      url: ['', Validators.required],
      isDefault: false,
      title: [''],
      status: [Site.STATUS_NORMAL, Validators.required],
      operatorSiteGroupName: [''],
      externalCode: ['']
    });
  }

  private initSite() {
    const { site } = this.data;
    if (site) {
      if (site.status && site.status !== Site.STATUS_INFO) {
        this.statuses = this.statuses.filter(item => item.id !== Site.STATUS_INFO);
      } else {
        this.statuses = SITE_STATUS_LIST_TRANSLATE;
      }

      this.site = site;

      this.form.patchValue(site);
    }
  }
}
