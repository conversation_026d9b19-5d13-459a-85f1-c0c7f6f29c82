import { SwuiGridField } from '@skywind-group/lib-swui';
import { Site, SITE_STATUS_LIST } from '../../../../../../common/models/site.model';


const SCHEMA: SwuiGridField[] = [
  {
    field: 'title',
    title: 'ENTITY_SETUP.WHITELISTING.gridSiteTitle',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
  },
  {
    field: 'url',
    title: 'ENTITY_SETUP.WHITELISTING.gridSiteUrl',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    td: {
      type: 'string',
    },
  },
  {
    field: 'operatorSiteGroupName',
    title: 'ENTITY_SETUP.WHITELISTING.operatorSiteGroupName',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    td: {
      type: 'string',
    },
  },
  {
    field: 'externalCode',
    title: 'ENTITY_SETUP.WHITELISTING.externalCode',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    td: {
      type: 'string',
    },
  },
  {
    field: 'status',
    title: 'ENTITY_SETUP.WHITELISTING.gridSiteStatus',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: true,
    data: SITE_STATUS_LIST,
    td: {
      type: 'calc',
      titleFn: ( row: any ) => {
        let title = 'ENTITY_SETUP.WHITELISTING.siteStatusInfo';
        const statusTitles = {
          'normal': 'ENTITY_SETUP.WHITELISTING.siteStatusActive',
          'suspended': 'ENTITY_SETUP.WHITELISTING.siteStatusInactive',
        };
        if (row.status && row.status in statusTitles) {
          title = statusTitles[row.status];
        }
        return title;
      },
      classFn: ( row: any ) => {
        return row.status === 'normal' ? 'sw-chip sw-chip-green' : (
          row.status === 'suspended' ? 'sw-chip sw-chip-gray' : 'sw-chip'
        );
      }
    }
  },
  {
    field: 'isDefault',
    title: 'ENTITY_SETUP.WHITELISTING.isDefault',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    td: {
      type: 'radioButton',
      valueFn: ( row: Site ) => row.isDefault,
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
