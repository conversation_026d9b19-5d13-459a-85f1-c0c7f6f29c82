<h2 mat-dialog-title="">
  {{'ENTITY_SETUP.WHITELISTING.confirmationSelectedRemoved' | translate}}

</h2>
<mat-dialog-content class="mat-typography">
  <mat-list>
    <mat-list-item *ngFor="let site of data.rows">
      <ng-container *ngIf="site.title; else noTitle">
        <strong [matTooltip]="site.title">{{ site.url }}</strong>
      </ng-container>
      <ng-template #noTitle>
        <strong>{{ site.url }}</strong>
      </ng-template>
    </mat-list-item>
  </mat-list>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{ 'ALL.decline' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="doConfirm()">
    {{'ALL.confirm' | translate}}
  </button>
</mat-dialog-actions>
