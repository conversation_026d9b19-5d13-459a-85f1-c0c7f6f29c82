import { Component, Input, OnInit } from '@angular/core';
import { finalize, take } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { Site } from '../../../../../../common/models/site.model';
import { CheckWebsiteWhitelistedLevelType, SiteService, whitelistLevels } from '../../../../../../common/services/site.service';

@Component({
  selector: 'whitelist-levels',
  templateUrl: 'whitelist-levels.component.html',
  styleUrls: ['whitelist-levels.component.scss']
})

export class WhitelistLevelsComponent implements OnInit {

  @Input() entity: Entity;

  availableLevels: CheckWebsiteWhitelistedLevelType[] = [
    whitelistLevels.NONE,
    whitelistLevels.WARNING,
    whitelistLevels.ERROR,
  ];

  checkWebsiteWhitelistedLevel: CheckWebsiteWhitelistedLevelType;
  levelChangeLoading: boolean = false;

  constructor( private siteService: SiteService<Site> ) {
  }

  ngOnInit() {
    this.fetchExtendedInfo();
  }

  getLevelColor( level: CheckWebsiteWhitelistedLevelType ): string {
    const colors = {
      [whitelistLevels.NONE]: 'primary',
      [whitelistLevels.WARNING]: 'warn',
      [whitelistLevels.ERROR]: 'accent',
    };

    return level in colors ? colors[level] : '';
  }


  changeLevel( level: CheckWebsiteWhitelistedLevelType ) {
    this.levelChangeLoading = true;
    this.siteService.setCheckWebSiteWhitelisted(this.entity.path, level)
      .pipe(finalize(() => this.levelChangeLoading = false))
      .subscribe(() => this.checkWebsiteWhitelistedLevel = level);
  }

  resetLevel() {
    this.levelChangeLoading = true;
    this.siteService.resetCheckWebSiteWhitelisted(this.entity.path)
      .pipe(finalize(() => this.levelChangeLoading = false))
      .subscribe(() => this.fetchExtendedInfo());
  }

  private fetchExtendedInfo() {
    this.siteService.getCheckWebSiteWhitelisted(this.entity.path)
      .pipe(take(1))
      .subscribe(( { level } ) => {
        this.checkWebsiteWhitelistedLevel = level;
      });
  }
}
