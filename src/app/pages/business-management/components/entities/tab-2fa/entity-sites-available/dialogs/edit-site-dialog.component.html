<h2 mat-dialog-title>
  {{ ( site?.id ? 'ENTITY_SETUP.WHITELISTING.MODALS.editSite' : 'ENTITY_SETUP.WHITELISTING.MODALS.addSite') | translate }}
</h2>
<mat-dialog-content class="mat-typography">

  <div class="site-edit--form" [formGroup]="form">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.MODALS.siteUrl' | translate }}</mat-label>
      <input matInput trimValue type="text" formControlName="url">
      <mat-error><control-messages [control]="form.get('url')" [forceShow]="submitted"></control-messages></mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.MODALS.siteTitle' | translate }}</mat-label>
      <input matInput trimValue type="text" formControlName="title">
      <mat-error><control-messages [control]="form.get('title')" [forceShow]="submitted"></control-messages></mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.MODALS.operatorSiteGroupName' | translate }}</mat-label>
      <input matInput trimValue type="text" formControlName="operatorSiteGroupName">
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.MODALS.externalCode' | translate }}</mat-label>
      <input matInput trimValue type="text" formControlName="externalCode">
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.MODALS.status' | translate }}</mat-label>
      <mat-select formControlName="status">
        <mat-option *ngFor="let status of statuses" [value]="status.code">
          {{ status.displayName | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-checkbox class="site-edit--form-checkbox" formControlName="isDefault">
      {{'ENTITY_SETUP.WHITELISTING.MODALS.default' | translate}}
    </mat-checkbox>
  </div>

</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{ 'ENTITY_SETUP.WHITELISTING.MODALS.btnClose' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="submit()">
    {{ 'ENTITY_SETUP.WHITELISTING.MODALS.btnSave' | translate }}
  </button>
</mat-dialog-actions>

