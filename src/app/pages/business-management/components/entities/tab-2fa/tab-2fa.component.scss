.sec-row {
  display: flex;
  flex-direction: column;
  &__wrapper {
    display: flex;
    gap: 32px;
  }
  &__header {
    display: flex;
    align-items: center;
    padding: 4px 16px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-bottom: none;
    background: #fff;
    button {
      margin-left: auto
    }
  }
  &__title {
    font-size: 18px;
    font-weight: 500;
    padding: 0 16px 16px;
  }
  &__item {
    flex: 1;
  }

}
