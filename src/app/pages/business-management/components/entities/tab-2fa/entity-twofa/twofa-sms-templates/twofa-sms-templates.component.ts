import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';

import {
  createDefaultTwoFASmsTemplate, EntitySettingsModel, handleFormUseDefault, ProcessedSmsTemplatesList, processTemplate, SmsTemplatesList,
  TWOFA_AVAILABLE_LANGUAGES,
} from '../../../../../../../common/models/entity-settings.model';


@Component({
  selector: 'twofa-sms-templates',
  templateUrl: './twofa-sms-templates.component.html',
})

export class TwofaSmsTemplatesComponent {

  @Output() formValue: EventEmitter<SmsTemplatesList> = new EventEmitter();
  @Output() isValid: EventEmitter<boolean> = new EventEmitter();

  public smsTemplateForm: FormGroup;
  public languages: { key: string, title: string }[] = TWOFA_AVAILABLE_LANGUAGES;

  private _settings: EntitySettingsModel;

  @Input()
  public set settings( value: EntitySettingsModel ) {
    if (!value) return;
    this._settings = value;
    this.setSmsTemplateForm(this._settings.twoFactorAuthSettings.smsTemplates);
  }

  @Input()
  public set isActivated ( value: boolean ) {
    if (!value && this._settings){
      this.setSmsTemplateForm(this._settings.twoFactorAuthSettings.smsTemplates);
    }
  }

  public get settings(): EntitySettingsModel {
    return this._settings;
  }

  constructor( private fb: FormBuilder) {
    this.smsTemplateForm = this.initSmsTemplateForm();
    handleFormUseDefault(this.smsTemplateForm);
    this.smsTemplateForm.valueChanges.subscribe(data => {
      this.isValid.emit(this.smsTemplateForm.valid);
      this.formValue.emit(this.setSmsTemplateFormToEmit(data));
    });
  }

  public initSmsTemplateForm(): FormGroup {
    const smsTemplateForm = this.fb.group({});
    this.languages.map(el => {
      smsTemplateForm.addControl(el.key, this.initSmsTemplateItem());
    });
    return smsTemplateForm;
  }

  private initSmsTemplateItem(): FormGroup {
    return this.fb.group({
      textMessage: ['', Validators.required],
      useDefault: false
    });
  }

  private setSmsTemplateForm(smsTemplates: SmsTemplatesList): void {
    const processedSmsTemplates = {};

    this.languages.map(lang => {
      if (smsTemplates && lang.key in smsTemplates) {
        processedSmsTemplates[lang.key] = {
          textMessage: smsTemplates[lang.key]
        };
      } else if (!smsTemplates || !smsTemplates['default']){
        processedSmsTemplates['default'] = createDefaultTwoFASmsTemplate();
      } else if (smsTemplates && !(lang.key in smsTemplates)) {
        this.smsTemplateForm.get(lang.key).reset();
        this.smsTemplateForm.get(lang.key).get('useDefault').setValue(true);
      }
    });

    this.smsTemplateForm.patchValue(processedSmsTemplates);
  }

  private setSmsTemplateFormToEmit(template: ProcessedSmsTemplatesList): SmsTemplatesList {
    const processedTpl = processTemplate(template);
    let templateToEmit: SmsTemplatesList = {};
    Object.keys(processedTpl).forEach( templateKey => {
      templateToEmit[templateKey] = processedTpl[templateKey]['textMessage'];
    });
    return templateToEmit;
  }

}
