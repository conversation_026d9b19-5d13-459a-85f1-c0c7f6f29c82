<mat-tab-group [formGroup]="smsTemplateForm">
  <mat-tab *ngFor="let lang of languages" [formGroupName]="lang.key">
    <ng-template mat-tab-label>
      <span [ngClass]="{'text-danger':!smsTemplateForm.get(lang.key).valid}">{{lang.title}}</span>
    </ng-template>

    <div  *ngIf="lang.key !== 'default'">
      <mat-checkbox formControlName="useDefault">
        {{'ENTITY_SETUP.TWOFA_SETUP.useDefault' | translate}}
      </mat-checkbox>
    </div>
    <mat-form-field appearance="outline" style="width:100%">
      <mat-label>{{'ENTITY_SETUP.TWOFA_SETUP.smsTemplateBody' | translate}}</mat-label>
      <textarea matInput trimValue
        cols="30" rows="10"
        formControlName="textMessage" style="max-width: 100%; width: 100%">
      </textarea>
      <mat-error>
        <control-messages
          [forceShow]="!smsTemplateForm.get(lang.key).valid"
          [control]="smsTemplateForm.get(lang.key).get('textMessage')">
        </control-messages>
      </mat-error>
    </mat-form-field>
  </mat-tab>
</mat-tab-group>
