<mat-tab-group [formGroup]="mailTemplateForm">
  <mat-tab *ngFor="let lang of languages" [formGroupName]="lang.key">
    <ng-template mat-tab-label>
      <span [ngClass]="{'text-danger':!mailTemplateForm.get(lang.key).valid}">{{lang.title}}</span>
    </ng-template>

    <div *ngIf="lang.key !== 'default'">
      <mat-checkbox formControlName="useDefault">
        {{'ENTITY_SETUP.TWOFA_SETUP.useDefault' | translate}}
      </mat-checkbox>
    </div>

    <mat-form-field appearance="outline" style="width:100%">
      <mat-label>{{'ENTITY_SETUP.TWOFA_SETUP.emailTemplateFrom' | translate}}</mat-label>
      <input matInput trimValue type="text" formControlName="from">
      <mat-error>
        <control-messages [forceShow]="!mailTemplateForm.get(lang.key).valid"
          [control]="mailTemplateForm.get(lang.key).get('from')">
        </control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" style="width:100%">
      <mat-label>{{'ENTITY_SETUP.TWOFA_SETUP.emailTemplateSubject' | translate}}</mat-label>
      <input matInput trimValue type="text" formControlName="subject">
      <mat-error>
        <control-messages [forceShow]="!mailTemplateForm.get(lang.key).valid"
                          [control]="mailTemplateForm.get(lang.key).get('subject')">
        </control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" style="width:100%">
      <mat-label>{{'ENTITY_SETUP.TWOFA_SETUP.emailTemplateBody' | translate}}</mat-label>
      <textarea matInput trimValue cols="30" rows="10" formControlName="html">
      </textarea>
      <mat-error>
        <control-messages [forceShow]="!mailTemplateForm.get(lang.key).valid"
                          [control]="mailTemplateForm.get(lang.key).get('html')">
        </control-messages>
      </mat-error>
    </mat-form-field>
  </mat-tab>
</mat-tab-group>
