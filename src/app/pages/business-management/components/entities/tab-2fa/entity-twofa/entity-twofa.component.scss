.sec-block {
  &__title {
    font-size: 18px;
    font-weight: 500;
  }
  &__header {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-bottom: none;
    background: #fff;
  }
  &__button {
    margin-left: auto;
  }
  &__body {
    mat-expansion-panel {
      margin-bottom: 0 !important;
      border: 1px solid rgba(0, 0, 0, 0.12);
      border-radius: 0;
      &:not(:last-child) {
        border-bottom: none;
      }
    }
  }
  &__footer {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px;
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-top: none;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
  }
  &__status {
    font-size: 18px;
    font-weight: 500;
    &--disabled {
      color: #f44336;
    }
    &--enabled {
      color: #009688;
    }
  }
}
