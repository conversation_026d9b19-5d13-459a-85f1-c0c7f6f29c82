import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ControlMessagesModule } from '../../../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { EntityTwofaComponent } from './entity-twofa.component';
import { TwofaEmailTemplatesComponent } from './twofa-email-templates/twofa-email-templates.component';

import { TwofaSmsTemplatesComponent } from './twofa-sms-templates/twofa-sms-templates.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';


@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TranslateModule.forChild(),
        ControlMessagesModule,
        MatButtonModule,
        MatExpansionModule,
        MatSlideToggleModule,
        MatInputModule,
        MatCheckboxModule,
        MatTabsModule,
        TrimInputValueModule,
    ],
  exports: [
    EntityTwofaComponent,
  ],
  declarations: [
    EntityTwofaComponent,
    TwofaSmsTemplatesComponent,
    TwofaEmailTemplatesComponent,
  ],
  providers: [],
})
export class EntityTwofaModule {
}
