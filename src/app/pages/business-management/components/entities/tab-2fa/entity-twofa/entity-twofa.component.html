<div class="sec-block">
  <div class="sec-block__header">
    <div class="sec-block__title">{{ 'ENTITY_SETUP.TWOFA_SETUP.twofaStatusPrefix' | translate }}&nbsp;</div>
    <span
      class="sec-block__status"
      [ngClass]="{'sec-block__status--disabled': twoFADisabled, 'sec-block__status--enabled': !twoFADisabled}">
    {{ (twoFADisabled ? 'ENTITY_SETUP.TWOFA_SETUP.twofaStatusInactive' : 'ENTITY_SETUP.TWOFA_SETUP.twofaStatusActive') | translate }}
  </span>
    <button
      mat-stroked-button
      class="sec-block__button mat-button-md"
      (click)="toggleTwoFAStatus($event)"
      [color]="twoFADisabled ? 'primary': 'warn'"
      [disabled]="toggleLoading">
      <i class="icon-spinner spinner" *ngIf="toggleLoading"></i>
      {{(twoFADisabled ? 'ENTITY_SETUP.TWOFA_SETUP.btnActivate' : 'ENTITY_SETUP.TWOFA_SETUP.btnDeactivate') | translate}}
    </button>
  </div>
  <div class="sec-block__body" [formGroup]="authOptionsForm">
    <mat-accordion>
      <mat-expansion-panel
        class="mat-elevation-z0"
        [disabled]="twoFADisabled"
        (opened)="handleOpenChangeSms(true)"
        (closed)="handleOpenChangeSms(false)">

        <mat-expansion-panel-header>
          <mat-panel-title class="panel-column">
            {{ 'ENTITY_SETUP.TWOFA_SETUP.twofaTypeSMS' | translate }}
          </mat-panel-title>
          <mat-panel-description class="panel-column">
            <mat-slide-toggle formControlName="sms"></mat-slide-toggle>
          </mat-panel-description>
        </mat-expansion-panel-header>

        <twofa-sms-templates
          [settings]="settings"
          (formValue)="handleSmsTemplateFormValue($event)"
          (isValid)="handleIsSmsFormValid($event)"
          [isActivated]="authOptionsForm.get('sms').value">
        </twofa-sms-templates>
        <div class="help-block no-margin-bottom">
          <small>
            <span class="text-semibold">{{'ENTITY_SETUP.TWOFA_SETUP.availableVariables' | translate}}: </span>
            username, authCode
          </small>
          <br>
          <small>
            <span class="text-semibold">{{'ENTITY_SETUP.TWOFA_SETUP.exampleTemplate' | translate}}: </span>
            <span ngNonBindable>{{username}}</span> here is your sms <span ngNonBindable>{{authCode}}</span> auth code
          </small>
        </div>

      </mat-expansion-panel>

      <mat-expansion-panel
        class="mat-elevation-z0"
        [disabled]="twoFADisabled"
        (opened)="handleOpenChangeMails(true)"
        (closed)="handleOpenChangeMails(false)">

        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ 'ENTITY_SETUP.TWOFA_SETUP.twofaTypeEMail' | translate }}
          </mat-panel-title>
          <mat-panel-description>
            <mat-slide-toggle formControlName="email"></mat-slide-toggle>
          </mat-panel-description>
        </mat-expansion-panel-header>

        <twofa-email-templates
          [settings]="settings"
          (formValue)="handleMailTemplateFormValue($event)"
          (isValid)="handleIsMailFormValid($event)"
          [isActivated]="authOptionsForm.get('email').value">
        </twofa-email-templates>
        <div class="help-block no-margin-bottom">
          <small><span class="text-semibold">
        {{'ENTITY_SETUP.TWOFA_SETUP.availableVariables' | translate}}: </span>username, authCode</small>
          <br>
          <small><span class="text-semibold">{{'ENTITY_SETUP.TWOFA_SETUP.exampleTemplate' | translate}}: </span>
            <span ngNonBindable>{{username}}</span> here is your <span ngNonBindable>{{authCode}}</span> auth code
          </small>
        </div>
      </mat-expansion-panel>

      <mat-expansion-panel class="mat-elevation-z0" #panel [disabled]="twoFADisabled" [hideToggle]="true" (opened)="panel.close()">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ 'ENTITY_SETUP.TWOFA_SETUP.twofaTypeGoogleAuth' | translate }}
          </mat-panel-title>
          <mat-panel-description>
            <mat-slide-toggle formControlName="google"></mat-slide-toggle>
          </mat-panel-description>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
  <div class="sec-block__footer">
    <button
      mat-stroked-button
      color="primary"
      (click)="save2FASettings($event)"
      [disabled]="twoFADisabled">
      {{ 'DIALOG.save' | translate }}
    </button>
  </div>
</div>
