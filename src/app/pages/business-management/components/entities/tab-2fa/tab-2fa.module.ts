import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { SiteService } from '../../../../../common/services/site.service';
import { EntityDomainAuthModule } from './entity-domain-auth/entity-domain-auth.module';
import { EntityIpWhitelistModule } from './entity-ip-whitelist/entity-ip-whitelist.module';
import { EntitySitesAvailableModule } from './entity-sites-available/entity-sites-available.module';

import { EntityTwofaModule } from './entity-twofa/entity-twofa.module';
import { Tab2FaComponent } from './tab-2fa.component';
import { Tab2FaRoutingModule } from './tab-2fa.routing';


@NgModule({
  imports: [
    CommonModule,
    Tab2FaRoutingModule,
    ReactiveFormsModule,
    EntityTwofaModule,
    EntitySitesAvailableModule,
    EntityIpWhitelistModule,
    EntityDomainAuthModule,
  ],
  declarations: [
    Tab2FaComponent,
  ],
  providers: [
    SiteService,
  ]
})
export class Tab2FaModule { }
