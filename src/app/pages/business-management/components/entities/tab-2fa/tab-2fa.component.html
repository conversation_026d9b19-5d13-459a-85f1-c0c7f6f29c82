<div class="sec-row" *ngIf="entity && entitySettings" style="margin-bottom: 32px; margin-top: 24px">
  <div class="sec-row__title">Authorization</div>
  <div class="sec-row__wrapper">
    <div class="sec-row__item">
      <entity-twofa [entity]="entity" [settings]="entitySettings"></entity-twofa>
    </div>
    <div class="sec-row__item" *ngIf="!entity.isRoot()">
      <entity-domain-auth [entity]="entity"></entity-domain-auth>
    </div>
  </div>
</div>

<ng-container *ngIf="entity && isSuperadmin">
  <div class="sec-row">
    <div class="sec-row__title">Whitelists</div>
    <div class="sec-row__wrapper">
      <div entity-ip-whitelist class="sec-row__item" [entity]="entity" whitelistType="bo"></div>
      <!--<div entity-ip-whitelist class="sec-row__item" [entity]="entity" whitelistType="user"></div>-->
    </div>
  </div>

  <div class="sec-row">
    <div class="sec-row__title"></div>
    <div class="sec-row__wrapper">
      <div id="entity-sites-available-anchor" entity-sites-available class="sec-row__item" [entity]="entity"
           [settings]="entitySettings"></div>
    </div>
  </div>
</ng-container>
