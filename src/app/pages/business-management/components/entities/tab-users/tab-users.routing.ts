import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { TabUsersComponent } from './tab-users.component';
import { BriefResolver } from '../../../../../common/services/resolvers/brief.resolver';


@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: TabUsersComponent,
        resolve: {
          brief: BriefResolver,
        }
      }
    ])
  ],
  exports: [
    RouterModule,
  ]
})
export class TabUsersRoutingModule {

}
