import { Schema<PERSON><PERSON>er<PERSON>atchEnum, SwuiGridField } from '@skywind-group/lib-swui';


const USER_STATUS_CLASS_MAP = {
  normal: 'sw-chip sw-chip-green',
  locked_by_auth: 'sw-chip sw-chip-red',
  suspended: 'sw-chip'
};

export const USER_STATUS_LIST = [
  { id: 'normal', code: 'normal', displayName: 'ENTITY_SETUP.USERS.statusActive' },
  { id: 'suspended', code: 'suspended', displayName: 'ENTITY_SETUP.USERS.statusInactive' }
];

export const USER_DISPLAY_STATUS_LIST = [
  { id: 'normal', code: 'normal', displayName: 'ENTITY_SETUP.USERS.statusActive' },
  { id: 'suspended', code: 'suspended', displayName: 'ENTITY_SETUP.USERS.statusInactive' },
  { id: 'locked_by_auth', code: 'locked_by_auth', displayName: 'ENTITY_SETUP.USERS.locked_by_auth' }
];

export const USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST = [
  { id: 'minutely', displayName: 'ENTITY_SETUP.USERS.PERIOD_TYPE.minutely' },
  { id: 'hourly', displayName: 'ENTITY_SETUP.USERS.PERIOD_TYPE.hourly' },
  { id: 'daily', displayName: 'ENTITY_SETUP.USERS.PERIOD_TYPE.daily' },
  { id: 'weekly', displayName: 'ENTITY_SETUP.USERS.PERIOD_TYPE.weekly' },
  { id: 'monthly', displayName: 'ENTITY_SETUP.USERS.PERIOD_TYPE.monthly' },
  { id: 'yearly', displayName: 'ENTITY_SETUP.USERS.PERIOD_TYPE.yearly' }
];

export const USER_TYPES_MAP = {
  bo: 'bo',
  operatorApi: 'operator_api'
};

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'entity',
    title: 'ENTITY_SETUP.USERS.entity',
    type: 'string',
    isViewable: true,
    isFilterable: false,
    isList: true
  },
  {
    field: 'fullName',
    title: 'ENTITY_SETUP.USERS.name',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    filterMatch: SchemaFilterMatchEnum.Contains,
    td: {
      type: 'string',
      truncate: {
        maxLength: 20,
        isEllipsis: true,
      }
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'username',
    title: 'ENTITY_SETUP.USERS.username',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
    td: {
      type: 'string',
      truncate: {
        maxLength: 50,
        isEllipsis: true,
      }
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'roleTitle',
    class: 'role-cell',
    title: 'ENTITY_SETUP.USERS.role',
    type: 'string',
    disabled: true,
    placeholder: 'In progress...',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    isHoverTitle: true,
    td: {
      type: 'string',
      truncate: {
        maxLength: 20,
        isEllipsis: true,
      }
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'email',
    title: 'ENTITY_SETUP.USERS.email',
    type: 'string',
    placeholder: '<EMAIL>',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
    form: {
      pattern: '^\\S+@\\S+$',
    },
    filterMatch: SchemaFilterMatchEnum.Contains,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },

  // Dates
  {
    field: 'createdAt',
    title: 'ENTITY_SETUP.USERS.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'updatedAt',
    title: 'ENTITY_SETUP.USERS.modified',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'status',
    title: 'ENTITY_SETUP.USERS.status',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    data: USER_STATUS_LIST,
    td: {
      type: 'status',
      statusList: USER_STATUS_LIST,
      displayStatusList: USER_DISPLAY_STATUS_LIST,
      classMap: USER_STATUS_CLASS_MAP,
      readonlyFn: row => row.status === 'locked_by_auth'
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
];

export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
