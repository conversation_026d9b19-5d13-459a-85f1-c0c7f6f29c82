import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { MatUserEditorModule } from '../../../../../common/components/mat-user-editor/user-editor.module';
import { RoleService } from '../../../../../common/services/role.service';
import { UserActionsService } from '../../../../../common/services/user-actions.service';
import { UserService } from '../../../../../common/services/user.service';
import { TabUsersDialogsModule } from './dialogs/tab-users-dialogs.module';
import { EntityUsersComponent } from './entity-users.component';

import { TabUsersComponent } from './tab-users.component';
import { TabUsersRoutingModule } from './tab-users.routing';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    TabUsersRoutingModule,
    MatUserEditorModule,
    SwuiGridModule,
    MatButtonModule,
    TabUsersDialogsModule,
  ],
  declarations: [
    TabUsersComponent,
    EntityUsersComponent,
  ],
  providers: [
    UserService,
    RoleService,
    UserActionsService,
  ]
})
export class TabUsersModule {
}
