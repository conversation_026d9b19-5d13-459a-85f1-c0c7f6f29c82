<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.USERS.MODALS.confirmationRequired' | translate }}
</h2>
<mat-dialog-content class="mat-typography">
  <div>{{ 'ENTITY_SETUP.USERS.MODALS.twofaUserResetNotification' | translate: { username: data.user.username } }}</div>
  <div>{{ 'ENTITY_SETUP.USERS.MODALS.pleaseSelectAndConfirm2FAReset' | translate }}</div>
  <mat-list>
    <mat-list-item *ngFor="let type of types">
<!--      <div class="checkbox">-->
<!--        <label>-->
<!--          <div class="checker">-->
<!--            <span [class.checked]="selected.has(type)">-->
<!--              <input class="styled" type="checkbox"-->
<!--                     [checked]="selected.has(type)" (change)="toggleSelectType(type)">-->
<!--            </span>-->
<!--          </div>-->
<!--          {{ type.displayName | translate }}-->
<!--        </label>-->
<!--      </div>-->
      <mat-checkbox [ngModel]="selected.has(type)" (ngModelChange)="toggleSelectType(type)">
        {{ type.displayName | translate }}
      </mat-checkbox>
    </mat-list-item>
  </mat-list>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>
    {{ 'ENTITY_SETUP.USERS.MODALS.btnClose' | translate }}
  </button>
  <button mat-button cdkFocusInitial (click)="confirmReset()"
          [disabled]="!selected.size" [class.disabled]="!selected.size">
    {{ 'ENTITY_SETUP.USERS.MODALS.btnConfirmReset' | translate }}
  </button>
</mat-dialog-actions>
