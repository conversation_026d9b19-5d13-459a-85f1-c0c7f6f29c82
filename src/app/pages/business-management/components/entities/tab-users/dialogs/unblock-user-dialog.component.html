<h2 mat-dialog-title>{{ 'ENTITY_SETUP.USERS.MODALS.unblockUserModalTitle' | translate}}</h2>
<mat-dialog-content class="mat-typography">
  <div *ngIf="!isUserLocked()">User is not locked</div>
  <div *ngIf="data.user.blocking.loginTillDate !== null">
    {{ 'ENTITY_SETUP.USERS.MODALS.userLockedLoginTillDate' | translate:{username: data.user.username} }}
    <button mat-stroked-button color="warn" type="submit" (click)="confirmUnblock(types.loginLock)">
      {{ 'ENTITY_SETUP.USERS.MODALS.btnUnblock' | translate}}
    </button>
  </div>
  <div *ngIf="data.user.blocking.changePasswordTillDate !== null">
    {{  'ENTITY_SETUP.USERS.MODALS.userLockedChangePasswordTillDate' | translate }}
    <span class="m-10" [title]="data.user.blocking.changePasswordTillDate">{{getFormattedDate(data.user.blocking.changePasswordTillDate)}}</span>
    <button mat-stroked-button color="warn" type="submit" (click)="confirmUnblock(types.changePasswordLock)">
      {{ 'ENTITY_SETUP.USERS.MODALS.btnUnblock' | translate}}
    </button>
  </div>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button
    tabindex="-1"
    mat-button
    color="primary"
    class="mat-button-md"
    mat-dialog-close>
    {{ 'DIALOG.close' | translate }}
  </button>
</mat-dialog-actions>
