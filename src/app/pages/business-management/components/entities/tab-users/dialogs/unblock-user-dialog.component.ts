import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SettingsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UserBlocking } from '../../../../../../common/typings';

export interface UnblockUserDialogData {
  user: UserBlocking;
}
export interface UnblockUserDialogResult extends UnblockUserDialogData {
  type: string;
}

@Component({
  selector: 'unblock-user-dialog',
  templateUrl: 'unblock-user-dialog.component.html'
})
export class UnblockUserDialogComponent implements OnInit {

  types: {
    loginLock: string;
    changePasswordLock: string;
  } = {
    loginLock: 'login-lock',
    changePasswordLock: 'change-password-lock',
  };

  format: string;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    public dialogRef: MatDialogRef<UnblockUserDialogComponent, UnblockUserDialogResult>,
    @Inject(MAT_DIALOG_DATA) public data: UnblockUserDialogData,
    private settings: SettingsService,
  ) {
  }

  ngOnInit() {
    this.settings.appSettings$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((appSettings) => this.format = `${appSettings.dateFormat} ${appSettings.timeFormat}`);
  }


  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  confirmUnblock(type ) {
    this.dialogRef.close({ user: this.data.user, type });
  }

  getFormattedDate( date ) {
    return moment(date).format(this.format);
  }

  isUserLocked() {
    return this.data.user.blocking.loginTillDate !== null || this.data.user.blocking.changePasswordTillDate !== null;
  }
}
