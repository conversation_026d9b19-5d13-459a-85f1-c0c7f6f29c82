<entity-page-panel
  [entity]="entity"
  [pageTitle]="pageTitle()">
</entity-page-panel>

<div class="cascade-games-body">
  <hints message="ADD_GAMES_CASCADE.hintText"></hints>

  <lib-swui-games-select-manager
    (selectedItemsChanged)="handleSelectedItemsChange($event)"
    [hideLabelsTab]="true"
    [availableGames]="games$ | async"></lib-swui-games-select-manager>

  <div fxLayout="row" fxLayoutAlign="end center" class="padding-top16">
    <button mat-flat-button
            color="primary"
            [disabled]="!selected || !selected.length"
            (click)="saveGames()">
      {{ 'DIALOG.save' | translate }}
    </button>
  </div>
</div>
