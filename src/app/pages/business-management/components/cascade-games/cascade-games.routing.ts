import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PERMISSIONS_LIST } from '../../../../app.constants';
import { BriefResolver } from '../../../../common/services/resolvers/brief.resolver';
import { EntitySettingsResolver } from '../../../../common/services/resolvers/entity-settings.resolver';
import { ShortStructureResolver } from '../../../../common/services/resolvers/structure.resolver';
import { SetupEntityDetailsResolver } from '../entities/resolvers/setup-entity-details.resolver';
import { SetupEntityParentResolver } from '../entities/resolvers/setup-entity-parent.resolver';
import { CascadeGamesComponent } from './cascade-games.component';


const routes = [
  {
    path: 'add',
    component: CascadeGamesComponent,
    data: {
      title: 'Add Games by Cascade',
      permissions: PERMISSIONS_LIST.ENTITY,
    },
    resolve: {
      entity: SetupEntityDetailsResolver,
      parent: SetupEntityParentResolver,
      brief: BriefResolver,
      entitySettings: EntitySettingsResolver,
      shortStructure: ShortStructureResolver,
    }
  },
  {
    path: 'add/:path',
    component: CascadeGamesComponent,
    data: {
      title: 'Add Games by Cascade',
      permissions: PERMISSIONS_LIST.ENTITY,
    },
    resolve: {
      entity: SetupEntityDetailsResolver,
      parent: SetupEntityParentResolver,
      brief: BriefResolver,
      entitySettings: EntitySettingsResolver,
      shortStructure: ShortStructureResolver,
    }
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ],
})
export class CascadeGamesRouting {
}
