import { Component, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  fromGameInfo, GameSelectItem, GamesSelectManagerService, PERMISSIONS_NAMES, SelectItemGameInfo, SwHubAuthService, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { of, Subject } from 'rxjs';
import { Observable } from 'rxjs/Observable';
import { map, takeUntil, tap } from 'rxjs/operators';
import { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';
import { GameService } from '../../../../common/services/game.service';
import { Entity, isLiveGame } from '../../../../common/typings';
import { SetupEntityService } from '../entities/setup-entity.service';

@Component({
  selector: 'cascade-games',
  templateUrl: 'cascade-games.component.html',
  styleUrls: [
    './cascade-games.component.styl'
  ],
  providers: [
    SetupEntityService,
  ],
})
export class CascadeGamesComponent implements OnDestroy {
  readonly messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };
  readonly games$: Observable<GameSelectItem[]>;
  selected: SelectItemGameInfo[] = [];
  entity: Entity;

  private readonly entityPath?: string;
  private readonly destroyed$ = new Subject<void>();

  constructor(
    { snapshot }: ActivatedRoute,
    private readonly service: GameService,
    private readonly notificationService: SwuiNotificationsService,
    private readonly translate: TranslateService,
    private readonly gamesSelectManagerService: GamesSelectManagerService,
    private swHubAuthService: SwHubAuthService,
    entityService: SetupEntityService,
  ) {
    entityService.initSnapshot(snapshot.data);
    this.entity = entityService.entity;
    this.entityPath = entityService.entity?.path;

    const parentPath = entityService.entity?.entityParent.path;
    this.games$ = parentPath ?
      this.service.getAllGames(parentPath, true, true)
        .pipe(
          map(games => {
            if (this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME_ADD])) {
              games = games.filter(game => !isLiveGame(game));
            }

            return games.map(game => fromGameInfo(game));
          })
        ) :
      of([]);
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  handleSelectedItemsChange( { games }: any ) {
    this.selected = games.map(( { data } ) => data);
  }

  saveGames() {
    if (this.entityPath && this.selected?.length) {
      const gamesCode = this.selected.map(( { code } ) => code);
      this.service.cascadeAddGames(this.entityPath, gamesCode).pipe(
        tap(() => {
            const message = this.translate.instant('ADD_GAMES_CASCADE.gamesAddedSuccessfully', {
              count: gamesCode.length
            });
            this.notificationService.success(message, '');
            this.gamesSelectManagerService.setupSelectedGames([]);
          }
        ),
        takeUntil(this.destroyed$)
      ).subscribe();
    }
  }

  pageTitle(): string {
    return this.translate.instant('ADD_GAMES_CASCADE.title', { name: (this.entity?.title || this.entity?.name) });
  }
}
