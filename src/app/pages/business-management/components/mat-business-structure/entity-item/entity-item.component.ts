import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { RowAction, SettingsService, SwHubAuthService } from '@skywind-group/lib-swui';
import { BehaviorSubject, fromEvent, Subject, Subscription, zip } from 'rxjs';
import { first, map, take, takeUntil } from 'rxjs/operators';

import { PERMISSIONS_LIST, PERMISSIONS_NAMES } from '../../../../../app.constants';
import { Entity, Entity as EntityModel } from '../../../../../common/models/entity.model';
import { CountryService } from '../../../../../common/services/country.service';
import { EntityLabelsService } from '../../../../../common/services/entity-labels.service';
import { EntityService } from '../../../../../common/services/entity.service';
import { LanguagesService } from '../../../../../common/services/languages.service';
import { Balance, Country, Language } from '../../../../../common/typings';
import { LabelGroupInfo } from '../../../../../common/typings/label';
import { bsDialogs, BusinessStructureService } from '../business-structure.service';
import { EntityLabelsDialogComponent } from '../dialogs/entity-labels-dialog/entity-labels-dialog.component';
import { RegionalItem } from '../dialogs/mat-regional-dialog/mat-regional-item/mat-regional-item.component';
import { ShowLimitsModalComponent } from '../dialogs/show-limits-modal/show-limits-modal.component';
import { StructureEntityModel } from '../structure-entity.model';

@Component({
  selector: '[entity-item]',
  templateUrl: './entity-item.component.html',
  styleUrls: ['./entity-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityItemComponent implements OnInit, OnChanges, OnDestroy {

  @Input() expanded = false;

  @Input() disabledCurrencyTooltips = false;

  @Input() canChangeTest = false;

  @Input() set entity( val: StructureEntityModel ) {
    if (!val) {
      return;
    }
    console.log(val);
    this._entity = val;
    this.isEntitySuspended = val.changeStatusState !== 'normal';
    this.canChangeStatus = val.changeStatusState === 'blocked';
    this.isEntityRowDisabled = this.isEntitySuspended && !val.isRoot();
    this.initRowActions = this.setRowActions();
  }

  get entity() {
    return this._entity;
  }

  set initRowActions( val: RowAction[] ) {
    if (!val) {
      return;
    }
    this._rowActions = val;
  }

  get rowActions() {
    return this._rowActions;
  }

  set isEntitySuspended( val: boolean ) {
    this._isEntitySuspended = val;
  }

  get isEntitySuspended(): boolean {
    return this._isEntitySuspended;
  }

  set canChangeStatus( val: boolean ) {
    this._canChangeStatus = val;
  }

  get canChangeStatus(): boolean {
    return this._canChangeStatus;
  }

  @Output() onClick = new EventEmitter<string>();

  @ViewChild(MatTooltip) tooltip: MatTooltip;

  entityItemStatusMap = {
    'normal': {
      class: 'success',
      color: 'primary',
      title: 'BUSINESS_STRUCTURE.WIDGETS.active'
    },
    'suspended': {
      class: 'default',
      color: 'accent',
      title: 'BUSINESS_STRUCTURE.WIDGETS.inactive'
    },
    'maintenance': {
      class: 'warning',
      color: 'warn',
      title: 'BUSINESS_STRUCTURE.WIDGETS.maintenance'
    },
    'blocked_by_admin': {
      class: 'danger',
      color: 'warn',
      title: 'BUSINESS_STRUCTURE.WIDGETS.blocked'
    },
    'test': {
      class: 'danger',
      color: 'blue',
      title: 'BUSINESS_STRUCTURE.WIDGETS.test'
    }
  };
  isAvailableItemsLoaded = false;
  countriesHash: { [code: string]: Country };

  merchantLoading = false;
  balancesLoading = true;
  statusUpdating = false;
  entityUpdate = false;
  infoText = '';
  isEntityRowDisabled: boolean = false;
  availableItems: BehaviorSubject<RegionalItem[]> = new BehaviorSubject([]);

  isSuperAdmin = false;
  editDisabled$ = new BehaviorSubject(false);
  canChangeState = false;
  menuHasItems = false;

  readonly isBrand: boolean;
  private _isEntitySuspended = false;
  private _canChangeStatus = false;
  private _entity: StructureEntityModel;
  private readonly destroyed$ = new Subject<void>();
  private tooltipSubscription = new Subscription();
  private numberOfClicks = 0;
  private _rowActions: RowAction[] = [];

  constructor(
    private route: ActivatedRoute,
    public readonly bsService: BusinessStructureService,
    private readonly authService: SwHubAuthService,
    private readonly entityService: EntityService<StructureEntityModel>,
    private cd: ChangeDetectorRef,
    private dialog: MatDialog,
    private entityLabelsService: EntityLabelsService,
    private readonly languageService: LanguagesService<Language>,
    private readonly countryService: CountryService<Country>,
    private readonly settingsService: SettingsService,
    private readonly router: Router,
  ) {
    const { brief } = this.route.snapshot.data;
    const briefEntity = new EntityModel(brief);
    this.isBrand = briefEntity.type === Entity.TYPE_BRAND || briefEntity.type === Entity.TYPE_MERCHANT;
    this.isSuperAdmin = this.authService.isSuperAdmin;
  }

  ngOnChanges( { entity }: SimpleChanges ) {
    if (entity?.currentValue) {
      const decoded = this.entity.decryptedBrand && `id (decoded): ${this.entity.decryptedBrand}\n` || '';

      this.infoText = `${decoded}id (encoded): ${this.entity.id}\npath: ${this.entity.path}`;
    }
  }

  ngOnInit(): void {
    this.canChangeState = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_CHANGESTATE]) && !this.entity.isRoot();

    this.menuHasItems = ((this.canChangeState && this.entity.status !== 'test')
        || (['test'].includes(this.entity.status) && this.canChangeTest && !this.entity.isReseller()))
      && this.allowedStatuses() &&  !this.entity.isRoot();
  }

  handleOpenItemsMenu( event ) {
    this.availableItems.next([]);
    this.isAvailableItemsLoaded = false;
    let source;
    switch (event.target.id) {
      case 'currency':
        source =
          this.entityService.getBalances(this.entity.path)
            .pipe(
              map<Balance, RegionalItem[]>(( balances: Balance ) => {
                return Object.keys(balances).reduce(( acc: RegionalItem[], key: string ) => {
                  const currencyFormat = this.settingsService.appSettings.currencyFormat;
                  const currencySymbol = new Intl.NumberFormat(currencyFormat, {
                    style: 'currency',
                    currency: key,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  }).format(0).replace(/\d/g, '').trim();

                  const balanceFormat = new Intl.NumberFormat(currencyFormat, {
                    style: 'currency',
                    currency: key
                  }).format(balances[key].main).replace(currencySymbol, '').replace(/\s+/g, '');

                  acc.push({
                    code: balanceFormat,
                    displayName: key
                  });
                  return acc;
                }, []);
              })
            );
        break;
      case 'country':
        source = this.countryService.getList('', this.entity.path);
        break;
      case 'language':
        source = this.languageService.getList('', this.entity.path)
          .pipe(
            map(( languages: Language[] ) => {
              return languages.map(language => ({
                code: language.code,
                displayName: language.name
              }));
            })
          );
        break;
      default:
        break;
    }
    source.pipe(take(1)).subscribe(( items ) => {
      this.availableItems.next(items);
      this.isAvailableItemsLoaded = true;
      this.cd.detectChanges();
    });
  }

  onInfoClick() {
    if (!this.tooltip) {
      return;
    }
    this.tooltipSubscription.unsubscribe();
    this.tooltip.show();

    const tooltip = document.getElementsByTagName('mat-tooltip-component')[0];

    this.tooltipSubscription = fromEvent(tooltip, 'click')
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(e => {
        e.stopPropagation();
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.tooltipSubscription.unsubscribe();
  }

  isAddCascadeGamesAllowed(): boolean {
    return this.authService.areGranted(PERMISSIONS_LIST.GAMES_CASCADE_ADD);
  }

  isEntityEditAllowed( entity: StructureEntityModel ): boolean {
    let allowed = false;
    if (entity && !entity.isRoot()) {
      allowed = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_EDIT]);
    }
    return allowed;
  }

  isEntityInfoAllowed( entity: StructureEntityModel ): boolean {
    let allowed = false;
    if (entity && !entity.isRoot()) {
      allowed = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_INFO]);
    }
    return allowed;
  }

  handleOpenEdit( event ) {
    event.preventDefault();
    this.singleClick(() => this.bsService.openDialog(bsDialogs.ENTITY_EDIT, this.entity));
  }

  handleOpenEditRegional( event ) {
    event.preventDefault();
    this.editDisabled$.next(true);
    this.bsService.openDialog(bsDialogs.EDIT_REGIONAL, this.entity, event.target.id)
      .pipe(take(1))
      .subscribe(() => {
        this.editDisabled$.next(false);
      });
  }

  handleOpenConfirmSetStatus( event, confirmStatus ) {
    event.preventDefault();
    this.bsService.openDialog(bsDialogs.SET_STATUS_CONFIRM, this.entity, { confirmStatus });
  }

  allowedStatuses(): boolean {
    if (!this.isSuperAdmin) {
      if (this.entity.status === 'test') {
        return this.canChangeTest;
      } else {
        return this.entity.status !== 'blocked_by_admin' && (this.canChangeStatus || !this.isEntitySuspended);
      }
    }
    return this.isSuperAdmin;
  }

  fetchBalances( event ) {
    event.preventDefault();

    const hasBalancePermission = this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_BALANCE]);

    if (hasBalancePermission) {
      this._fetchBalances();
    } else {
      this.balancesLoading = false;
      this.entity.currencies.forEach(currency => {
        this.entity.balances[currency] = {};
      });
    }
  }

  isCreditDebitAllowed(): boolean {
    return this.authService.allowedTo([PERMISSIONS_NAMES.FINANCE_CREDIT, PERMISSIONS_NAMES.FINANCE_DEBIT]);
  }

  isCountryAddAllowed(): boolean {
    return this.authService.allowedTo([PERMISSIONS_NAMES.COUNTRY_ADD]);
  }

  isShowGameLimitsAllowed(): boolean {
    return this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_VIEW);
  }

  isLabelsManagementAllowed(): boolean {
    return (this.entity.isRoot() && this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW])) ||
      (!this.entity.isRoot() && this.authService.allowedTo([PERMISSIONS_NAMES.ENTITYLABELS_VIEW]));
  }

  onItemClick( entity: Entity ) {
    this.onClick.emit(entity.id);
  }

  isAddChildAllowed(): boolean {
    return this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_CREATE]);
  }

  private _fetchBalances() {
    const parentHasBalances = !this.entity.isRoot() && this.entity.entityParent.hasDefinedBalances();
    if (this.entity && this.entity.hasDefinedBalances() && (parentHasBalances || this.entity.isRoot())) {
      this.balancesLoading = false;
      return;
    }

    zip(
      this.entityService.getBalances(this.entity.path),
      this.entityService.getBalances(this.entity.entityParent.path)
    ).pipe(
      first(),
    ).subscribe(
      ( [balances, parentBalances] ) => {
        this.entity.balances = balances;
        if (this.entity.entityParent) {
          this.entity.entityParent.balances = parentBalances;
        }
        this.balancesLoading = false;

        this.cd.detectChanges();
      },
      err => {
        console.error(err);
      }
    );
  }

  private singleClick( callback ) {
    this.numberOfClicks++;
    if (this.numberOfClicks <= 1) {
      callback();
      setTimeout(() => {
        this.numberOfClicks = 0;
      }, 2000);
    }
  }

  private setRowActions(): RowAction[] {
    const entityRowActions: RowAction[] = [];
    if (this.entity?.type === 'entity' && this.isAddChildAllowed()) {
      const addChildren = new RowAction({
        title: 'BUSINESS_STRUCTURE.WIDGETS.addChild',
        icon: 'add_box',
        fn: () => this.bsService.openDialog(bsDialogs.ENTITY_ADD, this.entity),
        canActivateFn: () => true
      });

      entityRowActions.push(addChildren);
    }
    if (!this.entity?.isMaster() && !this.isBrand && this.authService.allowedTo(PERMISSIONS_LIST.ENTITY_MOVE)) {
      const move = new RowAction({
        title: 'BUSINESS_STRUCTURE.WIDGETS.move',
        icon: 'format_line_spacing',
        fn: () => this.router.navigate(['/pages/business-management/entities/move/move', this.entity.key]),
        canActivateFn: () => true
      });

      entityRowActions.push(move);
    }
    if (!this.entity?.isMaster() && this.isAddCascadeGamesAllowed() && !this.isBrand && !this.entity?.isRoot()) {
      const addCascade = new RowAction({
        title: 'BUSINESS_STRUCTURE.WIDGETS.addCascade',
        icon: 'library_add',
        fn: () => this.router.navigate(['/pages/business-management/entities/cascade-games/add', this.entity.path]),
        canActivateFn: () => true
      });

      entityRowActions.push(addCascade);
    }
    if (this.isShowGameLimitsAllowed()) {
      const addCascade = new RowAction({
        title: 'BUSINESS_STRUCTURE.WIDGETS.showGameLimits',
        icon: 'attach_money',
        fn: () => this.dialog.open(ShowLimitsModalComponent, {
          width: '80vw',
          panelClass: 'limits-dialog-body',
          data: this.entity,
          disableClose: true
        }),
        canActivateFn: () => true,
      });

      entityRowActions.push(addCascade);
    }
    if (this.isLabelsManagementAllowed()) {
      const labels = new RowAction({
        title: 'BUSINESS_STRUCTURE.WIDGETS.labels',
        icon: 'label',
        fn: () => this.entityLabelsService.getLabelGroups('entity')
          .pipe(
            take(1),
          ).subscribe(( groups: LabelGroupInfo[] ) => {
            return this.dialog.open(EntityLabelsDialogComponent, {
              width: '90vw',
              disableClose: true,
              data: {
                entity: this.entity,
                labelGroupsSelectOptions: groups.map(group => {
                  return { id: group.id, text: group.group };
                }),
              },
            });
          }),
        canActivateFn: () => true
      });

      entityRowActions.push(labels);
    }
    return entityRowActions;
  }
}
