<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.REGIONAL.MODALS.manageLanguages' | translate }}
</h2>

<mat-form-field class="sw-dialog-search no-field-padding" appearance="outline">
  <input
    matInput trimValue
    (keyup)="applyFilter($event.target['value'])"
    [placeholder]="'ENTITY_SETUP.REGIONAL.MODALS.placeholderSearch' | translate">
</mat-form-field>

<mat-dialog-content class="sw-dialog-content">
  <table mat-table [dataSource]="dataSource">
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>
        <mat-checkbox [(ngModel)]="allSelected" (change)="allSelectedChanged($event)" [disabled]="!!nameFilter">
        </mat-checkbox>
      </th>
      <td mat-cell *matCellDef="let language">
        <mat-checkbox [(ngModel)]="language.selected" [disabled]="entity?.defaultLanguage === language.code">
        </mat-checkbox>
      </td>
    </ng-container>

    <ng-container matColumnDef="code">
      <th mat-header-cell *matHeaderCellDef>
        {{ (allSelected ? 'ENTITY_SETUP.REGIONAL.unSelectAll' : 'ENTITY_SETUP.REGIONAL.selectAll') | translate }}
      </th>
      <td mat-cell *matCellDef="let language">
        {{ language.name + ' (' + (language.code | uppercase) + ')' }}
      </td>
    </ng-container>

    <ng-container matColumnDef="amount">
      <td mat-footer-cell *matFooterCellDef colspan="2">
        {{ 'ENTITY_SETUP.REGIONAL.MODALS.selected' | translate }}:
        <span>
          <strong>{{ selectedItems.length }}</strong>
        </span>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    <tr mat-footer-row *matFooterRowDef="['amount']; sticky: true"></tr>
  </table>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-button
    color="primary"
    class="mat-button-md"
    mat-dialog-close>
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button
    mat-flat-button
    color="primary"
    class="mat-button-md"
    cdkFocusInitial
    (click)="applyChanges()">
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
