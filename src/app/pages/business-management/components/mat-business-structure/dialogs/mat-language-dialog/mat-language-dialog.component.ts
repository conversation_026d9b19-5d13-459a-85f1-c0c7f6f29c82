import { ChangeDetectionStrategy, Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { Entity } from '../../../../../../common/models/entity.model';
import { Language } from '../../../../../../common/typings';
import { StructureEntityModel } from '../../structure-entity.model';
import { codeArrayToObjectReducer } from '../../../../../../common/services/entity.service';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';

export interface MatLanguageDialogData {
  entity: StructureEntityModel;
  languages: Language[];
}

export class LanguageItem implements Language {
  _meta: any;
  code: string;
  direction: string;
  name: string;
  nativeName: string;

  selected: boolean;

  constructor( data: Language ) {
    this.code = data.code || '';
    this.direction = data.direction || '';
    this.name = data.name || '';
    this.nativeName = data.nativeName || '';
  }
}

@Component({
  selector: 'mat-language-dialog',
  templateUrl: './mat-language-dialog.component.html',
  styleUrls: ['./mat-language-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MatLanguageDialogComponent implements OnInit {

  languages: Language[] = [];
  entity: Entity;

  @Output() languagesChanged: EventEmitter<any> = new EventEmitter();

  nameFilter: string;
  displayedColumns: string[] = ['name', 'code'];
  dataSource: MatTableDataSource<LanguageItem>;
  allSelected = false;

  private hash: { [code: string]: Language };
  private defaultLanguage: LanguageItem;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatLanguageDialogData,
    public dialogRef: MatDialogRef<MatLanguageDialogComponent>,
  ) {
    this.languages = this.data.languages;
    this.entity = this.data.entity;
  }

  ngOnInit() {
    this.buildHash();
    this.initAvailableLanguages();
  }

  applyFilter( filterValue: string ) {
    this.nameFilter = filterValue;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.allSelected = this.dataSource.filteredData.every(item => item.selected);
  }

  applyChanges() {
    this.dialogRef.close({
      entity: this.entity,
      selected: this.selectedItems.map(item => item.code)
    });
  }

  get selectedItems(): LanguageItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  get availableLanguageCodes(): string[] {
    let languageCodes = this.entity.entityParent.languages;
    const rootParent = this.entity.entityParent.isRoot();
    const rootLanguagesMismatch = languageCodes.length !== this.languages.length;

    if (rootParent && rootLanguagesMismatch) {
      languageCodes = Object.keys(this.hash);
    }

    return languageCodes;
  }

  allSelectedChanged( changeEvent: MatCheckboxChange ) {
    this.dataSource.data.map(item => item.selected = false);

    if (changeEvent.checked) {
      this.dataSource.filteredData.map(item => item.selected = true);
    } else {
      this.defaultLanguage.selected = true;
    }
  }

  private initAvailableLanguages() {
    const items = this.availableLanguageCodes
      .map(( code ) => {
        const item = new LanguageItem(this.hash[code]);
        item.selected = this.entity.languages.indexOf(code) > -1;

        if (code === this.entity.defaultLanguage) {
          this.defaultLanguage = item;
        }

        return item;
      });
    this.dataSource = new MatTableDataSource(items);
  }

  private buildHash() {
    this.hash = this.languages.reduce(codeArrayToObjectReducer, {});
  }
}
