import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { SetupHintDialogComponent } from './setup-hint-dialog.component';


@NgModule({
  declarations: [
    SetupHintDialogComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
  ]
})
export class SetupHintDialogModule { }
