import { Component, HostBinding, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Subject, timer } from 'rxjs';
import { filter, map, takeUntil, tap } from 'rxjs/operators';
import { StructureEntityModel } from '../../structure-entity.model';
import { AUTO_CLOSE_DURATION_SECONDS } from '../mat-entity-edit-dialog/mat-entity-edit-dialog.component';

@Component({
  selector: 'setup-hint-dialog',
  templateUrl: './setup-hint-dialog.component.html',
  styleUrls: ['./setup-hint-dialog.component.scss']
})
export class SetupHintDialogComponent implements OnInit, OnDestroy {

  entity: StructureEntityModel;
  countDown: number;
  @HostBinding('attr.tabindex')
  tabindex = 0;

  private destroyed$ = new Subject<void>();
  private autoCloseRemainingSeconds = AUTO_CLOSE_DURATION_SECONDS;

  constructor(@Inject(MAT_DIALOG_DATA) public data: { entity: StructureEntityModel },
              public dialogRef: MatDialogRef<SetupHintDialogComponent>,
              private router: Router) {
    this.entity = data.entity;
    this.countDown = this.autoCloseRemainingSeconds;
  }

  ngOnInit(): void {
    timer(this.autoCloseRemainingSeconds, 1000)
      .pipe(
        map(i => this.autoCloseRemainingSeconds - i),
        tap( i => this.countDown = i),
        filter( i => i === 0),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => this.dialogRef.close());
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  redirectToNewEntitySetup() {
    this.router.navigate(['/pages/business-management/entities/setup', this.entity.path, 'p'])
      .then(() => this.dialogRef.close());
  }
}
