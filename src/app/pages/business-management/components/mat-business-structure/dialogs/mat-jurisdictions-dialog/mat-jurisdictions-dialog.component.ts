import { Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { Entity } from '../../../../../../common/models/entity.model';
import { MatTableDataSource } from '@angular/material/table';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Jurisdiction } from '../../../../../../common/typings/jurisdiction';
import { JurisdictionItem } from './jurisdiction-item.model';
import { MatCheckboxChange } from '@angular/material/checkbox';

export interface MatJurisdictionDialogData {
  entity: Entity;
  entityJurisdictions: Jurisdiction[];
  parentJurisdictions: Jurisdiction[];
  masterJurisdictions: Jurisdiction[];
}

@Component({
  selector: 'mat-jurisdictions-dialog',
  templateUrl: 'mat-jurisdictions-dialog.component.html',
  styleUrls: [
    './mat-jurisdictions-dialog.component.scss',
  ]
})
export class MatJurisdictionsDialogComponent implements OnInit {

  @Output() jurisdictionsChanged: EventEmitter<any> = new EventEmitter();

  displayedColumns: string[] = ['code', 'title'];
  dataSource: MatTableDataSource<JurisdictionItem>;
  allSelected = false;
  notReseller = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatJurisdictionDialogData,
    public dialogRef: MatDialogRef<MatJurisdictionsDialogComponent, string[]>,
  ) {
  }

  ngOnInit() {
    this.initJurisdictionsData();
  }

  get selectedItems(): JurisdictionItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  applyChanges() {
    this.dialogRef.close(this.selectedItems.map(item => item.code));
  }

  allSelectedChanged( event: MatCheckboxChange ) {
    this.dataSource.data
      .filter(item => !item.disabled)
      .forEach(item => item.selected = event.checked);
  }

  selectionChanged( event: MatCheckboxChange ) {
    if (this.notReseller) {
      this.dataSource.data
        .filter(item => item.code !== event.source.value)
        .forEach(item => item.selected = false);
    }
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.allSelected = this.dataSource.filteredData.every(item => item.selected);
  }

  private initJurisdictionsData() {
    const { entityJurisdictions, parentJurisdictions, masterJurisdictions, entity } = this.data;

    const selectedCodes = entityJurisdictions.map(item => item.code);
    const masterCodes = masterJurisdictions.map(item => item.code);
    const parentCodes = parentJurisdictions.map(item => item.code);
    const data = masterJurisdictions.length ? masterJurisdictions : parentJurisdictions;

    this.notReseller = entity.isReseller() === false;

    this.dataSource = new MatTableDataSource(
      data.map(( item ) => {
        const mapped = new JurisdictionItem(item);
        mapped.selected = selectedCodes.indexOf(item.code) > -1;
        mapped.disabled = masterCodes.indexOf(item.code) > -1 && parentCodes.indexOf(item.code) === -1;
        return mapped;
      })
    );
  }
}
