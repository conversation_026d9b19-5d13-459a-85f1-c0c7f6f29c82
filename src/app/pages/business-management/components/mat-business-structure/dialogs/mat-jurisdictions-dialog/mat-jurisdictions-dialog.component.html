<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.REGIONAL.MODALS.manageJurisdictions' | translate }}
</h2>

<mat-dialog-content class="sw-dialog-content">
  <mat-form-field class="sw-dialog-search no-field-padding" appearance="outline">
    <input
      matInput trimValue
      (keyup)="applyFilter($event.target['value'])"
      [placeholder]="'ENTITY_SETUP.REGIONAL.MODALS.placeholderSearch' | translate">
  </mat-form-field>
  <div class="table-wrapper">
    <table mat-table [dataSource]="dataSource">
      <ng-container matColumnDef="code">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox [(ngModel)]="allSelected" (change)="allSelectedChanged($event)" *ngIf="!notReseller">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let jurisdiction">
          <mat-checkbox [(ngModel)]="jurisdiction.selected" [disabled]="jurisdiction.disabled" [value]="jurisdiction.code"
                        (change)="selectionChanged($event)">
          </mat-checkbox>
        </td>

      </ng-container>

      <ng-container matColumnDef="title">
        <th mat-header-cell *matHeaderCellDef>
          {{ 'ENTITY_SETUP.REGIONAL.jurisdictionName' | translate }}
        </th>
        <td mat-cell *matCellDef="let jurisdiction" [ngStyle]="jurisdiction.disabled ? {'color':'gray'} : {}">
          {{ jurisdiction.title + ' (' + jurisdiction.code + ')' }}
        </td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <td mat-footer-cell *matFooterCellDef colspan="2">
          <ng-container *ngIf="selectedItems?.length; else jurisdictionsErrorLabel">
            {{ 'ENTITY_SETUP.REGIONAL.MODALS.selected' | translate }}:
            <span>
            <strong>{{ selectedItems.length }}</strong>
          </span>
          </ng-container>
          <ng-template #jurisdictionsErrorLabel>
          <span class="mat-error">
            {{ 'ENTITY_SETUP.REGIONAL.MODALS.jurisdictionsErrorLabel' | translate }}
          </span>
          </ng-template>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      <tr mat-footer-row *matFooterRowDef="['amount']; sticky: true"></tr>
    </table>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-button
    color="primary"
    class="mat-button-md"
    mat-dialog-close>
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button
    mat-flat-button
    color="primary"
    class="mat-button-md"
    cdkFocusInitial
    [disabled]="!selectedItems?.length"
    (click)="applyChanges()">
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
