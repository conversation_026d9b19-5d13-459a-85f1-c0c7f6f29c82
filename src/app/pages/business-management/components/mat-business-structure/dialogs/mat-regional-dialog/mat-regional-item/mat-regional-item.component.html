<div>
  <ng-content></ng-content>
  <mat-form-field class="sw-dialog-search no-field-padding" appearance="outline">
    <input
      matInput trimValue
      (keyup)="applyFilter($event.target['value'])"
      [placeholder]="'ENTITY_SETUP.REGIONAL.MODALS.placeholderSearch' | translate">
  </mat-form-field>
  <a class="checked-toggle" *ngIf="selectedItems?.length" (click)="selectedOnlyToggle()">{{ toggleTexts[selectedOnly] | translate }}</a>
</div>
<mat-dialog-content class="sw-dialog-content">
  <table mat-table [dataSource]="dataSource">
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>
        <mat-checkbox
          [disabled]="disabledSelectAll"
          [(ngModel)]="allSelected" (change)="allSelectedChanged($event)">
        </mat-checkbox>
      </th>
      <td mat-cell *matCellDef="let item">
        <mat-checkbox
          [value]="item.code"
          (change)="handleChange($event)"
          [(ngModel)]="item.selected"
          [disabled]="entityDefaultItem === item.code">
        </mat-checkbox>
      </td>
    </ng-container>

    <ng-container matColumnDef="code">
      <th mat-header-cell *matHeaderCellDef>
        Name
      </th>
      <td mat-cell *matCellDef="let items">
        {{ items.displayName }} ( {{ items.code }} )
      </td>
    </ng-container>

    <ng-container matColumnDef="amount">
      <td mat-footer-cell *matFooterCellDef colspan="2">
        {{ 'ENTITY_SETUP.REGIONAL.MODALS.selected' | translate }}:
        <span>
          <strong>{{ selectedItems?.length }}</strong>
        </span>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    <tr mat-footer-row *matFooterRowDef="['amount']; sticky: true"></tr>
  </table>
</mat-dialog-content>

