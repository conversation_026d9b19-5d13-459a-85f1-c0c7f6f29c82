<form [formGroup]="form" class="sw-dialog-content">
  <h2 mat-dialog-title>
    {{ 'BUSINESS_STRUCTURE.editRegionalEntitySettings' | translate: {title: entity?.title} }}
  </h2>

  <mat-dialog-content>
    <mat-tab-group animationDuration="0ms"
                   (selectedIndexChange)="onSelectedIndexChange($event)"
                   [selectedIndex]="selectedIndex">

      <mat-tab *ngIf="isCurrenciesAvailable" label="currency">
        <ng-template mat-tab-label>{{'BUSINESS_STRUCTURE.currency' | translate}}</ng-template>
        <balance-form #balanceForm
                      [entity]="entity"
                      [allowZero]="true"
                      [currencies]="availableCurrencyCodes"
                      [selectedCurrencyCode]="entity?.defaultCurrency"
                      (formInvalidSubmitted)="onInvalidSubmitForm($event)">
        </balance-form>
      </mat-tab>

      <mat-tab *ngIf="false" label="country">
        <ng-template mat-tab-label>{{'BUSINESS_STRUCTURE.country' | translate}}</ng-template>
        <mat-regional-item
          formControlName="countries"
          [entityDefaultItem]="entity?.defaultCountry"
          [availableItemCodes]="availableCountryCodes">
        </mat-regional-item>
      </mat-tab>

      <mat-tab label="language">
        <ng-template mat-tab-label>{{'BUSINESS_STRUCTURE.language' | translate}}</ng-template>
        <mat-regional-item
          formControlName="languages"
          [entityDefaultItem]="entity?.defaultLanguage"
          [availableItemCodes]="availableLanguageCodes">
        </mat-regional-item>
      </mat-tab>

      <mat-tab label="jurisdictions" *ngIf="isJurisdictionsAvailable">
        <ng-template mat-tab-label>{{'BUSINESS_STRUCTURE.jurisdiction' | translate}}</ng-template>
        <mat-regional-item
          formControlName="jurisdictions"
          [regionalTabName]="'jurisdictions'"
          [entityType]="entity.type"
          [availableItemCodes]="availableJurisdictions">
          <div *ngIf="form.get('jurisdictions').errors" class="custom-error custom-error--error">
            {{ getError(form.get('jurisdictions')) | translate }}
          </div>
        </mat-regional-item>
      </mat-tab>

    </mat-tab-group>
  </mat-dialog-content>

  <mat-dialog-actions style="margin-top: 16px" align="end">
    <button
      mat-button
      color="primary"
      class="mat-button-md"
      mat-dialog-close>
      {{ 'DIALOG.cancel' | translate }}
    </button>
    <button
      mat-flat-button
      color="primary"
      class="mat-button-md"
      cdkFocusInitial
      (click)="onApply()">
      {{ 'DIALOG.save' | translate }}
    </button>
  </mat-dialog-actions>
</form>
