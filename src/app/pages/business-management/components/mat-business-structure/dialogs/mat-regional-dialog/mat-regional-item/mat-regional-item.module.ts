import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { MatRegionalItemComponent } from './mat-regional-item.component';


@NgModule({
  declarations: [
    MatRegionalItemComponent
  ],
  exports: [
    MatRegionalItemComponent
  ],
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatCheckboxModule,
    FormsModule,
    TranslateModule,
    MatDialogModule,
    MatProgressSpinnerModule,
  ]
})
export class MatRegionalItemModule { }
