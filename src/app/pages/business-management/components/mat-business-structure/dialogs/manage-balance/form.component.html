<form [formGroup]="form">
  <div class="form-group">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.REGIONAL.MODALS.selectTransactionDirection' | translate }}</mat-label>
      <mat-select formControlName="transactionDirection">
        <mat-option *ngFor="let direction of transactionDirectionList" value="{{ direction }}">
          {{ ('ENTITY_SETUP.REGIONAL.MODALS.' + direction.toLowerCase()) | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="form-group" *ngIf="parents.length > 1">
    <mat-form-field appearance="outline">
      <mat-label [ngSwitch]="transactionDirection">
        <ng-container *ngSwitchCase="'deposit'">{{ 'ENTITY_SETUP.REGIONAL.MODALS.creditFrom' | translate }}</ng-container>
        <ng-container *ngSwitchCase="'withdrawal'">{{ 'ENTITY_SETUP.REGIONAL.MODALS.debitTo' | translate }}</ng-container>
      </mat-label>
      <mat-select formControlName="activeParentPath">
        <mat-option *ngFor="let parent of parents" [value]="parent.path">
          {{ parent.name }}
        </mat-option>
      </mat-select>
      <mat-error>
        <control-messages [control]="form.get('activeParentPath')" [forceShow]="submitted"></control-messages>
      </mat-error>
    </mat-form-field>
  </div>
  <input trimValue *ngIf="parents.length === 1" type="hidden" formControlName="activeParentPath">


  <div class="form-group">
    <mat-form-field appearance="outline">
      <mat-label>{{ (_selectedCurrencyCode
        ? 'ENTITY_SETUP.REGIONAL.MODALS.selectedCurrency'
        : 'ENTITY_SETUP.REGIONAL.MODALS.chooseCurrency') | translate }}
      </mat-label>
      <mat-select formControlName="currency">
        <mat-option *ngFor="let currencyCode of entity?.currencies" [value]="currencyCode">
          {{ getCurrency(currencyCode)?.displayName }} ({{ getCurrency(currencyCode)?.label }})
        </mat-option>
      </mat-select>
      <mat-error>
        <control-messages [control]="form.get('currency')" [forceShow]="submitted"></control-messages>
      </mat-error>
    </mat-form-field>
    <label class="mt-10 no-margin-bottom" *ngIf="selectedCurrency">
      {{ 'ENTITY_SETUP.REGIONAL.MODALS.yourBalance' | translate }}:
      <strong>{{ getBalance(selectedCurrency) | number: '1.2-2' }} {{ selectedCurrencyLabel}}</strong>
    </label><br/>
    <label class="mt-10 no-margin-bottom" *ngIf="selectedCurrency">
      {{ 'ENTITY_SETUP.REGIONAL.MODALS.parentBalance' | translate }}:
      <strong>{{ availableBalance | number: '1.2-2' }} {{ selectedCurrencyLabel}}
      </strong>
    </label>
    <div>
      <label class="mt-10 no-margin-bottom" *ngIf="selectedCurrency">
        <ng-container *ngIf="transactionDirection === deposit; else withdrawalAvailable">
          {{ 'ENTITY_SETUP.REGIONAL.MODALS.availableToDeposit' | translate }}:
          <strong>{{ availableBalance | number: '1.2-2' }} {{ selectedCurrencyLabel}}
          </strong>
        </ng-container>
        <ng-template #withdrawalAvailable>
          {{ 'ENTITY_SETUP.REGIONAL.MODALS.availableToWithdraw' | translate }}:
          <strong>{{ getBalance(selectedCurrency) | number: '1.2-2' }} {{ selectedCurrencyLabel}}
          </strong>
        </ng-template>
      </label>
    </div>
  </div>

  <div class="form-group">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.REGIONAL.MODALS.transfer' | translate }}</mat-label>
      <input matInput type="number" min="0" formControlName="balance">
      <div matSuffix style="position: relative; bottom: 8px;" class="pl-5">{{ selectedCurrencyLabel }}</div>
      <mat-error>
        <control-messages [control]="form.get('balance')" [forceShow]="submitted"
                          [message]="{'min': ('ENTITY_SETUP.REGIONAL.MODALS.error_balanceMin' | translate),
                        'notEquals': ('ENTITY_SETUP.REGIONAL.MODALS.error_balanceNotEquals' | translate )}">
        </control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <label class="mt-10" *ngIf="selectedCurrency">{{ 'ENTITY_SETUP.REGIONAL.MODALS.totalBalance' | translate }}:
    <strong>
      {{ previewTotal(form.get('balance').value) | number: '1.2-2' }} {{ selectedCurrencyLabel }}
    </strong>
  </label>

</form>
