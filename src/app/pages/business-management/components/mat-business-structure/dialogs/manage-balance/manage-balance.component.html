<h2 mat-dialog-title>{{ 'ENTITY_SETUP.REGIONAL.MODALS.manageBalance' | translate }}</h2>
<mat-dialog-content class="mat-typography">
  <balance-form [entity]="entity" #balanceForm
                [selectedCurrencyCode]="selectedCurrencyCode"
                [currencies]="currencies"
                (formSubmitted)="onFormSubmit($event)">
  </balance-form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <div class="spinner-button-container">
    <div class="spinner-container" *ngIf="loading">
      <mat-spinner diameter="24"></mat-spinner>
    </div>
    <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="balanceForm.applyChanges()" [disabled]="loading">
      {{ 'DIALOG.save' | translate }}
    </button>
  </div>
</mat-dialog-actions>
