.sw-dialog-search {
  width: 100%;
  margin-bottom: 16px;
}
.sw-dialog-content{
  padding: 0;
  max-height: 45vh;
  mat-form-field {
    width: 100%;
  }

  table {
    width: 100%;
    max-height: 400px;
    overflow: auto;
  }

  mat-checkbox {
    position: relative;
    bottom: -2px;
  }

  .mat-row {
    min-height: 44px;
  }

  .mat-cell {
    font-size: 14px;
    color: #2a2c44;
    height: 44px !important;
    &:first-child {
      width: 56px;
      padding-right: 16px;
    }
  }

  .mat-header-row {
    min-height: 42px;
  }
  .mat-header-cell {
    font-size: 16px;
    color: #2a2c44;
    height: 42px !important;
    &:first-child {
      width: 56px;
      padding-right: 16px;
    }
  }

  .mat-footer-cell {
    font-size: 14px;
    color: rgba(0,0,0,.54);;
  }
}


