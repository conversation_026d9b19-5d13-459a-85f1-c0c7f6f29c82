import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DynamicFormOptionData, SWUI_CONTROL_MESSAGES } from '@skywind-group/lib-swui';
import { BehaviorSubject, Observable, of, Subject } from 'rxjs';
import { filter, map, switchMap, take, takeUntil } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';
import { MerchantTypeSchema } from '../../../../../../common/services/merchant-types.service';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { Jurisdiction } from '../../../../../../common/typings/jurisdiction';
import { EditEntitySubmitData } from './mat-entity-edit-dialog.component';

function initMerchantForm( fb: FormBuilder ): FormGroup {
  return fb.group({
    type: ['', Validators.required],
    code: ['', Validators.required],
    params: fb.group({}),
    storePlayerInfo: false,
  });
}

function setDynamicFormValues( typeSchema: MerchantTypeSchema, params: { [field: string]: any } ): DynamicFormOptionData | undefined {
  if (typeSchema) {
    const schema = typeSchema.schema;
    Object.keys(schema).forEach(key => {
      const option = schema[key];
      option.value = params[key];
    });

    if ('serverUrl' in schema) {
      schema.serverUrl['validation'] = {
        validators: [Validators.required, ValidationService.fullUrlValidation]
      };
    }

    return schema;
  }
  return undefined;
}

const messages = {
  'required': 'VALIDATION.required',
  'spacesAreNotAllowed': 'VALIDATION.spacesAreNotAllowed',
  'urlIsNotCorrect': 'VALIDATION.urlIsNotCorrect',
  'urlFormat': 'ALL.urlFormat',
};

@Component({
  selector: 'entity-edit-form',
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.scss'],
  providers: [
    {
      provide: SWUI_CONTROL_MESSAGES,
      useValue: messages
    }
  ]
})
export class EntityEditFormComponent implements OnInit, OnDestroy {

  @Input() ownSettings: EntitySettingsModel;
  @Input() settings: EntitySettingsModel;

  @Output() formSubmitted = new EventEmitter<EditEntitySubmitData>();

  readonly form: FormGroup;

  submitted = false;
  merchantMode = false;
  brandMode = false;
  addGames = false;
  editMode = false;

  merchantTypeSchemas: MerchantTypeSchema[];
  merchantParamsSchema: DynamicFormOptionData;
  availableJurisdiction: SelectOptionModel[] = [];

  entityTypes: SelectOptionModel[] = [];
  merchantTypesData: SelectOptionModel[] = [];
  playerPasswordChangeEnabledData: SelectOptionModel[] = [
    { id: 'inherited', text: 'ALL.inherited' },
    { id: 'true', text: 'BUSINESS_STRUCTURE.MODAL_GENERAL.enabled' },
    { id: 'false', text: 'BUSINESS_STRUCTURE.MODAL_GENERAL.disabled' }
  ];

  private _readonly: boolean;
  private readonly merchantForm: FormGroup;
  private readonly entity$ = new BehaviorSubject<Entity | undefined>(undefined);
  private _supportedMerchantTypes: SelectOptionModel[];

  get readonly(): boolean {
    return this._readonly;
  }

  @Input()
  set readonly( value: boolean ) {
    this._readonly = value;
    if (this._readonly === true) {
      this.form.disable();
    } else {
      if (this.form.disabled) {
        this.form.enable();
      }
    }
  }

  @Input()
  set value( { entity, merchantTypeSchemas }: { entity: Entity; merchantTypeSchemas: MerchantTypeSchema[] } ) {
    if (!entity) {
      return;
    }

    this.entity$.next(entity);
    this.merchantTypeSchemas = merchantTypeSchemas;
    this.editMode = !!entity.key;

    if (entity.type === Entity.TYPE_MERCHANT) {
      this.merchantMode = true;
      this.checkInjectMerchantForm();
      if (entity.merchant) {
        const typeSchema = merchantTypeSchemas.find(( { type } ) => type === entity.merchant.type);
        this.merchantParamsSchema = setDynamicFormValues(typeSchema, entity.merchant.params);
      }
    }

    if (entity.type === Entity.TYPE_BRAND) {
      this.brandMode = true;
    }

    this.form.patchValue(entity);

    if (entity.type !== Entity.TYPE_MERCHANT) {
      if (this.ownSettings
        && 'isPlayerPasswordChangeEnabled' in this.ownSettings
        && this.ownSettings.isPlayerPasswordChangeEnabled !== null
      ) {
        this.form.get('settings').get('isPlayerPasswordChangeEnabled')
          .setValue(this.ownSettings.isPlayerPasswordChangeEnabled?.toString());
      } else {
        this.form.get('settings').get('isPlayerPasswordChangeEnabled')
          .setValue('inherited');
      }
    }

    if (this.editMode) {
      this.form.get('name').disable();
      this.form.get('type').disable();

      if (this.merchantMode) {
        this.form.get('merchant.code').disable();
        this.form.get('merchant.type').disable();
      }
    }
  }

  get entity(): Entity | undefined {
    return this.entity$.value;
  }

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly jurisdiction: JurisdictionService,
               fb: FormBuilder,
               private service: EntityService<Entity>
  ) {
    this.form = this.initForm(fb);
    this.merchantForm = initMerchantForm(fb);
    this.setEntityTypes();
  }

  ngOnInit(): void {
    if (!this.editMode) {
      this.entity$.pipe(
        switchMap(entity => entity ? this.jurisdiction.getEntityJurisdictions(entity.entityParent.path) : of([])),
        map<Jurisdiction[], SelectOptionModel[]>(( jurisdictions: Jurisdiction[] ) => {
          return jurisdictions.map(jurisdiction => ({
            id: jurisdiction.code,
            text: jurisdiction.title
          }));
        }),
        takeUntil(this.destroyed$)
      ).subscribe(( data: SelectOptionModel[] ) => {
        this.availableJurisdiction = data;
      });
    }

    this.merchantTypesData = this.merchantTypes.map(( type: string ) => ({
      id: type,
      text: type
    }));

    this.form.get('type').valueChanges
      .pipe(
        filter(( type ) => type),
        takeUntil(this.destroyed$))
      .subscribe(( type: string ) => {
        this.merchantMode = type === Entity.TYPE_MERCHANT;
        this.brandMode = type === Entity.TYPE_BRAND;
        this.checkInjectMerchantForm();
      });

    this.merchantForm.get('type').valueChanges
      .pipe(
        filter(( type ) => type),
        takeUntil(this.destroyed$))
      .subscribe(( merchantType: string ) => {
        const typeSchema = this.merchantTypeSchemas.find(( { type } ) => type === merchantType);
        this.merchantParamsSchema = setDynamicFormValues(typeSchema, this.entity?.merchant?.params || {});
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get jurisdictionsControl(): FormControl {
    return this.form.get('jurisdictionCode') as FormControl;
  }

  get merchantTypes(): string[] {
    if (this.merchantTypeSchemas) {
      return this.merchantTypeSchemas.map(( { type } ) => type).sort();
    }
    return [];
  }

  get supportedMerchantTypes(): SelectOptionModel[] {
    if (!this._supportedMerchantTypes) {
      this._supportedMerchantTypes = [];
      if (this.entity && this.entity.parentId) {
        let source$: Observable<Entity>;
        if (this.entity.entityParent.isRoot()) {
          source$ = this.service.getBrief();
        } else {
          source$ = this.service.getItem(this.entity.entityParent.path);
        }

        source$.pipe(
          map(( entity: Entity ) => entity.merchantTypes.map(type => new SelectOptionModel(type, type))),
          take(1)
        ).subscribe(merchantTypes => this._supportedMerchantTypes = merchantTypes);
      } else if (this.entity && Array.isArray(this.entity.merchantTypes)) {
        this._supportedMerchantTypes = this.entity.merchantTypes
          .map(type => new SelectOptionModel(type, type));
      }
    }
    return this._supportedMerchantTypes;
  }

  submitFormData() {
    this.submitted = true;
    this.form.markAllAsTouched();

    if (this.editMode) {
      this.form.removeControl('jurisdictionCode');
    }

    if (this.form.valid) {
      let entity;
      if (this.editMode) {
        const value = this.form.value;

        if (![Entity.TYPE_MERCHANT, Entity.TYPE_BRAND].includes(this.form.getRawValue().type) || this.editMode) {
          delete value.webSiteUrl;
        }

        if (this.merchantMode) {
          const params = Object.assign({}, this.entity.merchant.params, value.merchant.params);
          const merchant = Object.assign({}, this.entity.merchant, value.merchant, { params });
          entity = Object.assign({}, this.entity, value, { merchant });
        } else {
          entity = Object.assign({}, this.entity, value);
        }
      } else {
        const { defaultCountry, defaultLanguage, defaultCurrency, path: parentPath } = this.entity.entityParent;
        entity = Object.assign({}, this.entity, this.form.value, {
          defaultCountry, defaultCurrency, defaultLanguage,
        });
        entity.countries = [defaultCountry];
        entity.languages = [defaultLanguage];
        entity.currencies = [defaultCurrency];
        entity.path = parentPath !== ':' ? `${parentPath}${entity.name}:` : `${entity.name}:`;
        entity.jurisdictionCode = this.jurisdictionsControl.value;
      }
      const ppcv = this.form.get('settings.isPlayerPasswordChangeEnabled').value;
      entity.settings.isPlayerPasswordChangeEnabled = ppcv === 'inherited' ? null : ppcv === 'true';
      entity.settings.playerPrefix = this.form.get('settings.playerPrefix').value;
      this.formSubmitted.emit({ entity, addGames: this.addGames });
    }
  }

  get merchantParamsGroup(): FormGroup {
    return this.form.get('merchant.params') as FormGroup;
  }

  private setEntityTypes() {
    this.entityTypes = [
      { id: 'entity', text: 'BUSINESS_STRUCTURE.MODAL_GENERAL.reseller' },
      { id: 'brand', text: 'BUSINESS_STRUCTURE.MODAL_GENERAL.operatorWallet' }
    ];
    if (this.merchantTypes) {
      this.entityTypes.push({ id: 'merchant', text: 'BUSINESS_STRUCTURE.MODAL_GENERAL.operatorSeamless' });
    }
  }

  private checkInjectMerchantForm() {
    if (this.merchantMode) {
      this.form.setControl('merchant', this.merchantForm);
    } else if (this.form.controls) {
      this.form.removeControl('merchant');
    }
  }

  private initForm( fb: FormBuilder ): FormGroup {
    return fb.group({
      title: ['', Validators.required],
      type: ['', Validators.required],
      merchantTypes: [[]],
      jurisdictionCode: ['', Validators.required],
      webSiteUrl: ['', this.webSiteUrlValidators.bind(this)],
      name: ['', [Validators.required, ValidationService.noWhitespaceValidation]],
      status: ['', Validators.required],
      description: '',
      settings: fb.group({
        storePlayerInfo: false,
        isPlayerPasswordChangeEnabled: 'inherited',
        playerPrefix: null
      })
    });
  }

  private webSiteUrlValidators( control: AbstractControl ) {
    const form = control.parent;

    if (![Entity.TYPE_MERCHANT, Entity.TYPE_BRAND].includes(form?.getRawValue()?.type) || this.editMode) {
      return null;
    }

    return Validators.required(control);
  }
}
