<div class="entity-update" fxLayout="column" fxLayoutGap="8px grid">
  <form [formGroup]="form" fxLayout="row wrap" fxLayoutGap="8px grid">

    <mat-form-field appearance="outline" fxFlex="100">
      <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.title' | translate}}</mat-label>
      <input matInput trimValue type="text" formControlName="title"
             placeholder="{{'BUSINESS_STRUCTURE.MODAL_GENERAL.placeholderTitle' | translate}}">
      <mat-error>
        <lib-swui-control-messages [control]="form.get('title')" [force]="submitted"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" fxFlex="50">
      <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.name' | translate}}</mat-label>
      <input matInput trimValue type="text" formControlName="name"
             placeholder="{{'BUSINESS_STRUCTURE.MODAL_GENERAL.placeholderName' | translate}}">
      <mat-error>
        <lib-swui-control-messages [control]="form.get('name')" [force]="submitted"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" fxFlex="50">
      <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.type' | translate}}</mat-label>
      <lib-swui-select
        formControlName="type"
        [emptyOptionPlaceholder]="'BUSINESS_STRUCTURE.MODAL_GENERAL.optionEmpty'"
        [data]="entityTypes"
        [showSearch]="false">
      </lib-swui-select>
      <mat-error>
        <lib-swui-control-messages [control]="form.get('type')" [force]="submitted"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <ng-container *ngIf="brandMode && settings?.playerPrefixEnabled" formGroupName="settings">
      <mat-form-field appearance="outline" fxFlex="50">
        <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.playerPrefix' | translate}}</mat-label>
        <input matInput trimValue type="text" formControlName="playerPrefix"
               placeholder="{{'BUSINESS_STRUCTURE.MODAL_GENERAL.playerPrefix' | translate}}">
      </mat-form-field>
    </ng-container>

    <mat-form-field *ngIf="!merchantMode && !brandMode && supportedMerchantTypes.length" appearance="outline" fxFlex="100">
      <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.supportedMerchantTypes' | translate}}</mat-label>
      <lib-swui-chips-autocomplete formControlName="merchantTypes" [items]="supportedMerchantTypes">
      </lib-swui-chips-autocomplete>
      <mat-error>
        <lib-swui-control-messages [control]="form.get('merchantTypes')" [force]="submitted"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <ng-container *ngIf="!editMode">
      <mat-form-field appearance="outline" fxFlex="50">
        <mat-label>{{ 'ENTITY_SETUP.REGIONAL.titleJurisdictions' | translate }}:</mat-label>
        <lib-swui-select [formControl]="jurisdictionsControl"
                         [emptyOptionPlaceholder]="'BUSINESS_STRUCTURE.MODAL_GENERAL.optionEmpty'"
                         [data]="availableJurisdiction"
                         [showSearch]="true">
        </lib-swui-select>
        <mat-error>
          <lib-swui-control-messages [control]="jurisdictionsControl" [force]="submitted"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <mat-form-field *ngIf="brandMode || merchantMode" appearance="outline" fxFlex="50">
        <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.webSiteUrl' | translate}}</mat-label>
        <input matInput trimValue type="text" formControlName="webSiteUrl"
               placeholder="{{'BUSINESS_STRUCTURE.MODAL_GENERAL.webSiteUrl' | translate}}">
        <mat-error>
          <lib-swui-control-messages [control]="form.get('webSiteUrl')" [force]="submitted"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </ng-container>

    <ng-container *ngIf="merchantMode" formGroupName="merchant">
      <mat-form-field appearance="outline" fxFlex="50">
        <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.code' | translate}}</mat-label>
        <input matInput trimValue type="text" formControlName="code"
               placeholder="{{'BUSINESS_STRUCTURE.MODAL_GENERAL.placeholderCode' | translate}}">
        <mat-error>
          <lib-swui-control-messages [control]="form.get('merchant.code')" [force]="submitted"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" fxFlex="50">
        <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.supportedMerchantTypes' | translate}}</mat-label>
        <lib-swui-select
          formControlName="type"
          [emptyOptionPlaceholder]="'BUSINESS_STRUCTURE.MODAL_GENERAL.optionEmpty'"
          [data]="merchantTypesData"
          [showSearch]="true">
        </lib-swui-select>
        <mat-error>
          <lib-swui-control-messages [control]="form.get('merchant.type')" [force]="submitted"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </ng-container>

    <ng-container *ngIf="!merchantMode" formGroupName="settings">
      <mat-form-field appearance="outline" fxFlex="50">
        <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.isPlayerPasswordChangeEnabled' | translate}}</mat-label>
        <lib-swui-select
          formControlName="isPlayerPasswordChangeEnabled"
          [emptyOptionPlaceholder]="'BUSINESS_STRUCTURE.MODAL_GENERAL.optionEmpty'"
          [data]="playerPasswordChangeEnabledData"
          [showSearch]="false">
        </lib-swui-select>
      </mat-form-field>
    </ng-container>

    <div fxFlex="100" *ngIf="merchantMode && merchantParamsSchema">
      <lib-dynamic-form
        [options]="merchantParamsSchema"
        [form]="merchantParamsGroup"
        [readonly]="readonly"
        [submitted]="submitted">
      </lib-dynamic-form>
    </div>

    <div *ngIf="merchantMode || brandMode" fxFlex="100" formGroupName="settings">
      <mat-slide-toggle formControlName="storePlayerInfo">
        {{'BUSINESS_STRUCTURE.MODAL_GENERAL.storePlayerInfo' | translate}}
      </mat-slide-toggle>
    </div>

    <mat-form-field appearance="outline" fxFlex="100">
      <mat-label>{{'BUSINESS_STRUCTURE.MODAL_GENERAL.description' | translate}}</mat-label>
      <textarea matInput trimValue rows="5"
                placeholder="{{'BUSINESS_STRUCTURE.MODAL_GENERAL.description' | translate}}"
                formControlName="description" [readonly]="readonly">
    </textarea>
      <mat-error>
        <lib-swui-control-messages [control]="form.get('description')" [force]="submitted"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

  </form>
  <div class="entity-update__row">
    <mat-checkbox *ngIf="!editMode" [(ngModel)]="addGames" [disabled]="readonly">
      {{'PROMO.MODALS.btnAddGames' | translate}}
    </mat-checkbox>
  </div>
</div>




