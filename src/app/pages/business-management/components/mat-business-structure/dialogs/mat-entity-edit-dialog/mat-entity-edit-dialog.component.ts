import { Component, <PERSON><PERSON><PERSON><PERSON>, Inject, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';
import { MerchantTypeSchema } from '../../../../../../common/services/merchant-types.service';
import { StructureEntityModel } from '../../structure-entity.model';
import { EntityEditFormComponent } from './form.component';
import { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';

export const AUTO_CLOSE_DURATION_SECONDS: number = 60;

export interface EditEntitySubmitData {
  entity: StructureEntityModel;
  addGames?: boolean;
}

export interface MatEntityEditDialogData {
  entity: StructureEntityModel;
  merchantTypeSchemas: MerchantTypeSchema[];
  ownSettings?: EntitySettingsModel;
}

@Component({
  selector: 'mat-entity-edit-dialog',
  templateUrl: './mat-entity-edit-dialog.component.html',
  styleUrls: ['./mat-entity-edit-dialog.component.scss'],
})
export class MatEntityEditDialogComponent implements OnDestroy {

  @ViewChild('entityForm') public entityForm: EntityEditFormComponent;
  entity: StructureEntityModel;
  merchantTypeSchemas: MerchantTypeSchema[];
  loading = false;
  readonlyForm = false;
  settings$: Observable<EntitySettingsModel>;
  ownSettings: EntitySettingsModel;
  countDown: number;
  @HostBinding('attr.tabindex')
  tabindex = 0;

  private destroy$ = new Subject();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatEntityEditDialogData,
    public dialogRef: MatDialogRef<MatEntityEditDialogComponent>,
    public router: Router,
    entityService: EntitySettingsService<EntitySettingsModel>
  ) {
    this.entity = this.data.entity;
    this.ownSettings = Object.assign({}, this.data.ownSettings);
    this.merchantTypeSchemas = this.data.merchantTypeSchemas;
    this.settings$ = entityService.getSettings(this.entity.getEntityParent().path);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  submitForm() {
    this.entityForm.submitFormData();
  }

  onFormSubmitted( data: EditEntitySubmitData ) {
    this.ownSettings.isPlayerPasswordChangeEnabled = data.entity.settings.isPlayerPasswordChangeEnabled;
    this.dialogRef.close(data);
  }
}
