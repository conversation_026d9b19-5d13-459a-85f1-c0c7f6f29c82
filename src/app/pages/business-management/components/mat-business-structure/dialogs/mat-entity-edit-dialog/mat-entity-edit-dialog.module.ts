import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatDynamicFormModule, SwitcheryModule, SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from 'src/app/common/directives/trim-input-value/trim-input-value.module';
import { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';
import { EntityEditFormComponent } from './form.component';
import { MatEntityEditDialogComponent } from './mat-entity-edit-dialog.component';

export const matModules = [
  MatDialogModule,
  MatCardModule,
  MatButtonModule,
  MatFormFieldModule,
  MatInputModule,
  MatCheckboxModule,
  FlexLayoutModule,
  MatSlideToggleModule,
  SwuiChipsAutocompleteModule,
];

@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        FormsModule,
        ReactiveFormsModule,
        RouterModule,

        SwuiControlMessagesModule,
        SwitcheryModule,

      ...matModules,
      MatDynamicFormModule,
      SwuiSelectModule,
      TrimInputValueModule
    ],
  declarations: [
    MatEntityEditDialogComponent,
    EntityEditFormComponent,
  ],
  entryComponents: [
    MatEntityEditDialogComponent,
  ],
  providers: [JurisdictionService]
})
export class MatEntityEditDialogModule {
}
