<h2 mat-dialog-title>{{ entity?.path ? 'Edit Entity' : 'Create Entity' }}</h2>

<mat-dialog-content>
  <entity-edit-form
    [ownSettings]="ownSettings"
    [settings]="settings$ | async"
    [value]="{entity: entity, merchantTypeSchemas: merchantTypeSchemas}" #entityForm
    (formSubmitted)="onFormSubmitted($event)"
    [readonly]="loading || readonlyForm">
  </entity-edit-form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button class="mat-button-md link" mat-dialog-close *ngIf="!loading">
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" (click)="submitForm()" [disabled]="loading">
    <i *ngIf="loading" class="icon-spinner2 spinner"></i>
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
