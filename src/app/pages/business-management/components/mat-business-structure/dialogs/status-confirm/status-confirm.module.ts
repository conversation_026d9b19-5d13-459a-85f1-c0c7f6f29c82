import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { StatusConfirmComponent } from './status-confirm.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';

export const matModules = [
  MatDialogModule,
  MatButtonModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    ...matModules,
  ],
  declarations: [StatusConfirmComponent],
  entryComponents: [StatusConfirmComponent],
})
export class StatusConfirmModule {
}
