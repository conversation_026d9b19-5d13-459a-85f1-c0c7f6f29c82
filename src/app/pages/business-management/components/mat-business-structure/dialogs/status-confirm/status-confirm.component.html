<h2 mat-dialog-title>
  {{ 'BUSINESS_STRUCTURE.MODAL_STATUS.setStatus' | translate: { statusTitle: getStatusTitle(data.confirmStatus) } }}
</h2>
<mat-dialog-content class="mat-typography">
  <div [innerHTML]="'BUSINESS_STRUCTURE.MODAL_STATUS.changeStatus' | translate:
          { statusTitle: getStatusTitle(data.confirmStatus) }">
  </div>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{ 'ENTITY_SETUP.REGIONAL.MODALS.btnCancel' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="confirm()">
    {{ 'ENTITY_SETUP.REGIONAL.MODALS.btnApply' | translate }}
  </button>
</mat-dialog-actions>
