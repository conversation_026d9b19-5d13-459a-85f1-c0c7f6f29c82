import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Entity } from '../../../../../../common/models/entity.model';

export interface StatusConfirmDialogData {
  entity: Entity;
  confirmStatus: string;
}

@Component({
  selector: 'status-confirm',
  templateUrl: './status-confirm.component.html',
  styleUrls: ['./status-confirm.component.scss']
})
export class StatusConfirmComponent {

  private statusTitleMap = {
    'normal': 'Active',
    'suspended': 'Inactive',
    'maintenance': 'Maintenance',
    'blocked_by_admin': 'Blocked',
    'test': 'Test'
  };

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: StatusConfirmDialogData,
    public dialogRef: MatDialogRef<StatusConfirmComponent, StatusConfirmDialogData>,
  ) {
  }

  confirm() {
    this.dialogRef.close(this.data);
  }

  getStatusTitle( status ) {
    return this.statusTitleMap[status];
  }
}
