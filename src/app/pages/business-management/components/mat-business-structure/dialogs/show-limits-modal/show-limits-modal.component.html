<button tabindex="-1" mat-icon-button class="close-button-x" (click)="onNoClick()">
  <mat-icon class="close-icon">close</mat-icon>
</button>
<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.GAME_LIMITS.showGameLimits' | translate }}
</h2>

<div fxLayout="row" fxLayoutAlign="end center">
  <button *ngIf="isExportLimitsAllowed && entitySettings?.flatReportsEnabled"
          mat-flat-button
          class="mat-button-md export-button"
          (click)="exportAsXLSX()">
    {{ 'ENTITY_SETUP.GAME_LIMITS.exportToExcel' | translate }}
  </button>
</div>

<div *ngIf="gameGroupsSelectOptions?.length; else noLimitsAvailable">
  <div fxLayout="row" fxLayoutAlign="start center">
    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{'ENTITY_SETUP.GAME_LIMITS.defaultGameGroupLabel' | translate}}</mat-label>
      <lib-swui-select
        [placeholder]="'ENTITY_SETUP.GAME_LIMITS.defaultGameGroupLabel' | translate"
        [data]="gameGroupsSelectOptions"
        [showSearch]="true"
        [disableEmptyOption]="true"
        [disabled]="!isEditGameGroupAllowed()"
        [formControl]="defaultGameGroup">
      </lib-swui-select>
    </mat-form-field>
    <div class="margin-bottom24 margin-left12">
      <button mat-flat-button color="primary"
              (click)="submit()"
              [disabled]="!isEditGameGroupAllowed()">
        {{ 'DIALOG.save' | translate }}
      </button>
    </div>
  </div>

  <form [formGroup]="form">
    <div fxLayout="row">
      <div fxLayout="row" fxLayoutAlign="start center" class="margin-left12 margin-right24 width100">
        <div fxLayout="row" fxFlex="50" fxLayoutAlign="start center">
          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate }}</mat-label>
            <lib-swui-select
              [placeholder]="'ENTITY_SETUP.GAME_LIMITS.gameGroupLabel' | translate"
              [data]="gameGroupsSelectOptions"
              [showSearch]="true"
              [disableEmptyOption]="true"
              [formControl]="gameGroupControl">
            </lib-swui-select>
          </mat-form-field>
        </div>
        <div fxLayout="row" fxFlex="50" fxLayoutAlign="start center" class="margin-left24">
          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{ 'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate }}</mat-label>
            <lib-swui-select
              [placeholder]="'ENTITY_SETUP.GAME_LIMITS.gameLabel' | translate"
              [data]="gamesSelectOptions"
              [showSearch]="true"
              [disableEmptyOption]="true"
              [formControl]="gameControl">
            </lib-swui-select>
          </mat-form-field>
        </div>
      </div>
    </div>
  </form>

  <div class="example-container mat-elevation-z0" *ngIf="limits?.length;">
    <div class="example-table-container">
      <table mat-table [dataSource]="limits" class="example-table"
             matSort matSortActive="created" matSortDisableClear matSortDirection="desc">

        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.currency' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{row?.currency}}</td>
        </ng-container>
        <ng-container matColumnDef="stakeAll">
          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.coinBets' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{joinStakeAll(row?.stakeAll)}}</td>
        </ng-container>
        <ng-container matColumnDef="stakeMin">
          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.minCoinBet' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{row?.stakeMin}}</td>
        </ng-container>
        <ng-container matColumnDef="stakeMax">
          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.maxCoinBet' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{row?.stakeMax}}</td>
        </ng-container>
        <ng-container matColumnDef="maxTotalStake">
          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.maxTotalBet' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{row?.maxTotalStake}}</td>
        </ng-container>
        <ng-container matColumnDef="stakeDef">
          <th mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.GAME_LIMITS.TABLE.stakeDef' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{row?.stakeDef}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </div>
  <div *ngIf="!limits?.length && !loading">
    <div class="custom-error custom-error--warn margin-bottom16">
      <span *ngIf="!loading">{{ 'ENTITY_SETUP.GAME_LIMITS.noLimitsFound' | translate }}</span>
    </div>
  </div>
</div>

<ng-template #noLimitsAvailable class="block-message">
  <div class="custom-error custom-error--warn margin-bottom32">
    {{ 'ENTITY_SETUP.GAME_LIMITS.noLimitsAvailable' | translate }}
  </div>
</ng-template>
