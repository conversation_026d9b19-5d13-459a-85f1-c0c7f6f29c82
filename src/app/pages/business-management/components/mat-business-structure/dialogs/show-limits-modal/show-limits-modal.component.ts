import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';

import { PERMISSIONS_LIST } from '../../../../../../app.constants';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { GameGroup } from '../../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';
import { ExcelService } from '../../../../../../common/services/excel.service';
import { FlatReportsService } from '../../../../../../common/services/flat-reports.service';
import { GameGroupService } from '../../../../../../common/services/game-group.service';
import { GameService } from '../../../../../../common/services/game.service';
import { Game } from '../../../../../../common/typings';
import { FlatReport, FlatReportGameGroupInfo, FlatReportGameInfo, FlatReportLimits, GAME_GROUP_CUSTOMIZATION } from '../../../../../../common/typings/flat-report';
import { StructureEntityModel } from '../../structure-entity.model';


@Component({
  selector: 'show-limits-modal',
  templateUrl: './show-limits-modal.component.html',
  styleUrls: ['./show-limits-modal.component.scss'],
})
export class ShowLimitsModalComponent implements OnInit {

  operatorColumn = 'Operator';
  gameGroupColumn = 'Game Group';
  defaultGameGroupColumn = 'Default Game Group';
  gameColumn = 'Game';
  currencyColumn = 'Currency';
  customizationColumn = 'Default/Customized';
  coinBets = 'Coin Bets';
  minCoinBet = 'Min Coin Bet';
  maxCoinBet = 'Max Coin Bet';
  maxTotalBet = 'Max Total Bet';
  stakeDef = 'Stake Def';

  entitySettings: EntitySettingsModel;

  displayedColumns: string[] = ['currency', 'stakeAll', 'stakeMin', 'stakeMax', 'maxTotalStake', 'stakeDef'];
  entity: Entity;
  limits: any[] = [];
  gameGroupsSelectOptions: SelectOptionModel[] = [];
  initialGamesSelectOptions: SelectOptionModel[] = [];
  gamesSelectOptions: SelectOptionModel[] = [];

  form: FormGroup;
  defaultGameGroup = new FormControl(['']);

  loading = false;

  isExportLimitsAllowed = false;

  private readonly destroyed$ = new Subject<void>();

  constructor( public dialogRef: MatDialogRef<ShowLimitsModalComponent>,
               @Inject(MAT_DIALOG_DATA) public initialState: StructureEntityModel,
               private fb: FormBuilder,
               private gameGroupService: GameGroupService,
               private gameService: GameService,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private authService: SwHubAuthService,
               private excelService: ExcelService,
               private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
               private flatReportsService: FlatReportsService,
  ) {
    this.entity = this.initialState;

    this.isExportLimitsAllowed = this.authService.allowedTo(PERMISSIONS_LIST.FLAT_REPORTS);

    this.entitySettingsService.getSettings(this.entity.path)
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( data: EntitySettingsModel ) => this.entitySettings = data);
  }

  ngOnInit() {
    this.buildGameGroupsSelectOptions();
    this.buildGamesSelectOptions();
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get gameGroupControl(): FormControl {
    return this.form.get('gameGroup') as FormControl;
  }

  get gameControl(): FormControl {
    return this.form.get('game') as FormControl;
  }

  isEditGameGroupAllowed(): boolean {
    return this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_EDIT);
  }

  joinStakeAll( stakeAll: number[] ): string {
    return stakeAll.join(', ');
  }

  submit() {
    const defaultGameGroupName = this.gameGroupsSelectOptions.find(group => group.id === this.defaultGameGroup.value).text;
    this.gameGroupService.setDefaultGameGroup(this.entity.path, defaultGameGroupName).subscribe(
      () => {
        this.notifications.success(
          this.translate.instant('ENTITY_SETUP.GAME_GROUP.notificationDefaultGameGroupChanged',
            { gameGroup: this.defaultGameGroup.value })
        );
      }
    );
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  exportAsXLSX() {
    this.flatReportsService.getTheLatestUpdatedFlatReport(this.entity.path)
      .pipe(
        switchMap(( flatReport: FlatReport ) => {
          return this.gameService.getAllGames(this.entity.path, false, true)
            .pipe(
              map(( games: Game[] ) => ({ flatReport, games })),
            );
        }),
        takeUntil(this.destroyed$),
      )
      .subscribe(( { flatReport, games } ) => {
        if (flatReport) {
          const resultArray = [];
          const report = flatReport.report;

          Object.entries(report)?.map(( [gameCode, gameGroups]: [string, FlatReportGameInfo] ) => {
            Object.entries(gameGroups)?.map(( [gameGroup, currencies]: [string, FlatReportGameGroupInfo] ) => {
              Object.entries(currencies)?.map(( [currency, limits]: [string, FlatReportLimits] ) => {
                let row = {};

                let customization = 'Default';
                if (Array.isArray(limits?.limitsCustomizations) && limits?.limitsCustomizations.length) {
                  if (limits?.limitsCustomizations.length === 1) {
                    customization = limits?.limitsCustomizations[0] === GAME_GROUP_CUSTOMIZATION ?
                      'Customized by game group' :
                      'Customized by filters';
                  } else {
                    customization = 'Customized by game group and filters';
                  }
                }

                row[this.operatorColumn] = this.entity.name;
                row[this.gameGroupColumn] = gameGroup;
                row[this.defaultGameGroupColumn] = gameGroup === this.defaultGameGroup.value ? 'Yes' : 'No';
                row[this.gameColumn] = games?.find(( game: Game ) => game.code === gameCode)?.title;
                row[this.currencyColumn] = currency;
                row[this.customizationColumn] = customization;
                row[this.coinBets] = limits?.limits?.stakeAll.join(', ');
                row[this.minCoinBet] = limits?.limits?.stakeMin;
                row[this.maxCoinBet] = limits?.limits?.stakeMax;
                row[this.maxTotalBet] = limits?.limits?.maxTotalStake;
                row[this.stakeDef] = limits?.limits?.stakeDef;

                resultArray.push(row);
              });
            });
          });

          resultArray.sort(( prev, curr ) => prev[this.gameGroupColumn].localeCompare(curr[this.gameGroupColumn]));

          this.excelService.exportAsExcelFile(resultArray, this.entity.path.replace(':', '') + '_limits');

          this.notifications.success(this.translate.instant('ENTITY_SETUP.GAME_LIMITS.flatReportWasExported'));
        } else {
          this.notifications.warning(this.translate.instant('ENTITY_SETUP.GAME_LIMITS.flatReportNotFound'));
        }
      });
  }

  private buildGameGroupsSelectOptions() {
    this.gameGroupService.getGameGroupsList(this.entity.path)
      .pipe(
        filter(data => !!data && data.length > 0),
        takeUntil(this.destroyed$)
      ).subscribe(
      data => {
        this.gameGroupsSelectOptions = data.map(( gameGroup: GameGroup ) => new GameGroup(gameGroup).toSelectOption());
        const defaultGameGroupId = this.gameGroupsSelectOptions.find(group => group.data)?.id;
        if (defaultGameGroupId) {
          this.defaultGameGroup.patchValue(defaultGameGroupId);
        }
      }
    );
  }

  private buildGamesSelectOptions() {
    this.gameService.getAllGames(this.entity.path, false, true).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(data => {
      this.gamesSelectOptions = data.map(game => new SelectOptionModel(game.code, game.title));
      this.initialGamesSelectOptions = this.gamesSelectOptions;
    });
  }

  private initForm() {
    this.form = this.fb.group({
      gameGroup: [''],
      game: [''],
    });

    this.gameGroupControl.valueChanges
      .pipe(
        filter(gameGroup => !!gameGroup),
        switchMap(gameGroup => this.gameGroupService.getGameGroupGames(this.entity.path, gameGroup)),
        takeUntil(this.destroyed$)
      ).subscribe(gameCodes => this.filterGamesSelectOptions(gameCodes));

    this.getLimits();
  }

  private filterGamesSelectOptions( gameCodes: any[] ) {
    this.gamesSelectOptions = [];

    if (!gameCodes) {
      this.gameControl.setValue(null, { emitEvent: false });
      this.gamesSelectOptions = this.initialGamesSelectOptions;

      return;
    }

    if (gameCodes.length) {
      let gameCodesArray = gameCodes.map(item => item.code);
      this.gamesSelectOptions = this.initialGamesSelectOptions.filter(
        game => gameCodesArray.includes(game.id));
    }
  }

  private getLimits() {
    this.form.valueChanges.pipe(
      tap(() => {
        this.loading = true;
        this.limits = [];
      }),
      filter(val => !!val.gameGroup && !!val.game),
      map(data => {
        return {
          gameGroup: this.gameGroupsSelectOptions.find(group => group.id === data.gameGroup).text,
          game: data.game
        };
      }),
      switchMap(params => this.gameGroupService.getGameLimits(this.entity.path, params.gameGroup, params.game)),
      map(data => data ? Object.entries(data).map(( [currency, limit] ) => {
        return ({ ...limit as object, currency });
      }) : []),
    ).subscribe(data => {
      this.loading = !this.loading;
      this.limits = data;
    });
  }
}
