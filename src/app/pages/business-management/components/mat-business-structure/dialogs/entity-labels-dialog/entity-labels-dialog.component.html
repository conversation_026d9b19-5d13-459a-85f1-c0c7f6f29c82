<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.LABELS.title' | translate }}
</h2>

<div class="row">
  <div class="add-button">
    <button mat-mini-fab
            color="primary"
            style="box-shadow: none"
            [disabled]="isLabelsInReadMode || itemsArray?.controls.length === labelGroupsSelectOptions.length"
            (click)="addLabelsGroup()">
      <mat-icon>add</mat-icon>
    </button>
  </div>
  <div class="add-label">
    <div class="add_label">{{ 'ENTITY_SETUP.LABELS.addLabelsGroup' | translate }}</div>
  </div>
</div>

<mat-dialog-content>
  <form [formGroup]="form">
    <div *ngIf="itemsArray?.length">
      <div formArrayName="items" *ngFor="let row of itemsArray.controls; let i = index;" class="row">
        <div class="row__item">
          <mat-form-field *ngIf="!itemsArray.controls[i].get('labelGroup').value; else labelsGroupTitle"
                          class="select"
                          appearance="outline">
            <lib-swui-select
              [formControl]="itemsArray.controls[i].get('labelGroup')"
              [placeholder]="'ENTITY_SETUP.LABELS.labelsGroup' | translate"
              [data]="labelGroupsSelectOptions"
              [showSearch]="true"
              [disableEmptyOption]="true">
            </lib-swui-select>
          </mat-form-field>

          <ng-template #labelsGroupTitle>
            <div class="labels-group-title">
              {{ getLabelsGroupTitle(itemsArray.controls[i].get('labelGroup').value) }}
            </div>
          </ng-template>

          <mat-form-field class="chips" appearance="outline">
            <mat-label>{{ 'ENTITY_SETUP.LABELS.labels' | translate }}</mat-label>
            <lib-swui-chips-autocomplete
              [formControl]="itemsArray.controls[i].get('labels')"
              [items]="labelsSelectOptions[i]"
              [addFn]="addLabel(i)"
              [mapFn]="mapFn">
            </lib-swui-chips-autocomplete>
          </mat-form-field>
        </div>

        <div class="row__item">
          <div class="delete_button">
            <button mat-icon-button
                    [disabled]="isLabelsInReadMode"
                    (click)="removeLabelsGroup(i)">
              <mat-icon fontSet="material-icons-outline">delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-button
    color="primary"
    class="mat-button-md"
    mat-dialog-close>
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button
    mat-flat-button
    color="primary"
    class="mat-button-md"
    cdkFocusInitial
    [disabled]="isLabelsInReadMode"
    (click)="save()">
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
