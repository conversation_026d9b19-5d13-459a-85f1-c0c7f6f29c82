import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_ENDPOINT } from '../../../../app.constants';
import { BiReportDomain } from '../../../../common/typings/bi-report-domain';

@Injectable()
export class BiReportsSwitchService {
  private readonly baseUrl = `${API_ENDPOINT}/bi/reports/domains`;

  constructor( private httpClient: HttpClient ) {
  }

  getReports(): Observable<BiReportDomain[]> {
    return this.httpClient.get<BiReportDomain[]>(this.baseUrl);
  }

  selectReports(id: string): Observable<any> {
    return this.httpClient.patch(`${this.baseUrl}/${id}/select`, {});
  }

}
