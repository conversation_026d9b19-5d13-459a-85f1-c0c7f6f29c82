.bi-report-table {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

.bi-report-row {
  height: 44px;
}

.bi-report-cell {
  text-align: left;
  padding: 8px;

  &_first {
    padding-left: 16px;
  }
}

th {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

tr:nth-child(even) {
  background-color: #eef1f5;
}

.bi-report-card {
  width: 100%;
  padding: 0 !important;
  min-height: 100px;
}

.bi-report-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  width: 100%;
}

.spinner-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
