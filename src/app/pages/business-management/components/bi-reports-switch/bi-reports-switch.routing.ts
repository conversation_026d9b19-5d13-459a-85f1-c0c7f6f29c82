import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_NAMES } from '../../../../app.constants';
import { BIReportsSwitchComponent } from './bi-reports-switch.component';

const routes: Routes = [
  {
    path: '',
    component: BIReportsSwitchComponent,
    data: {
      permissions: [PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_DOMAINS, PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_DOMAINS_VIEW],
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BiReportsSwitchRouting {
}
