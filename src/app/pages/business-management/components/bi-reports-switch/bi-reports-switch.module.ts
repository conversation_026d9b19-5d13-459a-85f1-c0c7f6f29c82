import { CdkTableModule } from '@angular/cdk/table';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { BIReportsSwitchComponent } from './bi-reports-switch.component';
import { BiReportsSwitchRouting } from './bi-reports-switch.routing';
import { BiReportsSwitchService } from './bi-reports-switch.service';

@NgModule({
  imports: [
    CommonModule,
    BiReportsSwitchRouting,
    SwuiPagePanelModule,
    MatTableModule,
    CdkTableModule,
    MatCardModule,
    MatRadioModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  declarations: [
    BIReportsSwitchComponent
  ],
  providers: [
    BiReportsSwitchService
  ]
})
export class BiReportsSwitchModule {
}
