<lib-swui-page-panel [title]="'MENU_SECTIONS.bi-reports-switch'"></lib-swui-page-panel>

<div class="p-32 sw-grid-layout">
  <mat-card class="bi-report-card">
    <ng-container *ngIf="loaded; else spinner">
      <mat-radio-group color="primary" aria-label="Select an option" [formControl]="selectedControl">
        <table class="bi-report-table">
          <tr class="bi-report-row">
            <th class="bi-report-cell bi-report-cell_first"></th>
            <th class="bi-report-cell">{{'BI-REPORTS-SWITCH.pid' | translate}}</th>
            <th class="bi-report-cell">{{'BI-REPORTS-SWITCH.baseUrl' | translate}}</th>
            <th class="bi-report-cell">{{'BI-REPORTS-SWITCH.trustServerUrl' | translate}}</th>
          </tr>
          <tr class="bi-report-row" *ngFor="let report of reports">
            <td class="bi-report-cell bi-report-cell_first">
              <mat-radio-button [value]="report.pid"></mat-radio-button>
            </td>
            <td class="bi-report-cell">{{report.pid}}</td>
            <td class="bi-report-cell">{{report.baseUrl}}</td>
            <td class="bi-report-cell">{{report.trustServerUrl}}</td>
          </tr>
        </table>
      </mat-radio-group>
      <div class="bi-report-actions">
        <button *ngIf="loaded && canSelect" mat-flat-button color="primary" [disabled]="!selectedControl.value || selectedControl.pristine"
                (click)="onSave()">{{'ALL.save' | translate}}</button>
      </div>
    </ng-container>

    <ng-template #spinner>
      <div class="spinner-container">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
    </ng-template>
  </mat-card>
</div>
