<div #wrapper class="page-wrapper">
  <lib-swui-page-panel [title]="'MENU_SECTIONS.bulkActions'" [actions]="panelActions"></lib-swui-page-panel>

  <mat-card class="mat-card card mat-elevation-z0">
    <div class="top-panel">
      <div class="search-block">
        <mat-form-field appearance="outline" class="no-field-padding">
          <input
            matInput
            [formControl]="filter"
            autocomplete="off"
            placeholder="Search by name, server or domain"
          >
        </mat-form-field>
        <mat-radio-group
          color="primary"
          [value]="tab"
          (change)="onSelect($event)">
          <mat-radio-button
            class="radio-button"
            *ngFor="let tab of tabs; let i = index;"
            [value]="tab"
          >{{'BULK_ACTIONS.' + tab + 'Domains' | translate}}</mat-radio-button>
        </mat-radio-group>
      </div>

      <div>
        <button class="mat-button-md" mat-button color="primary" (click)="downloadCsv()" [disabled]="isDownloadDisabled">{{ 'BULK_ACTIONS.btnDownloadCsv' | translate}}</button>
        <button class="mat-button-md" mat-flat-button color="primary" (click)="showByDomain()" [disabled]="!items">{{ 'BULK_ACTIONS.btnShowAll' | translate}}</button>
      </div>
    </div>

    <ng-container *ngIf="itemsLength; else noItemsFound">
      <div class="entity-table-wrapper">
        <cdk-virtual-scroll-viewport tvsItemSize="48" style="height: calc(90vh - 200px);">
          <table mat-table #table [dataSource]="dataSource" matSort class="entity-table">
            <ng-container matColumnDef="select">
              <th mat-header-cell *matHeaderCellDef style="min-width: 50px">
              </th>
              <td style="width: 70px" mat-cell *matCellDef="let row">
                <mat-checkbox (click)="$event.stopPropagation()"
                              (change)="$event ? selections[tab].toggle(row) : null"
                              [disabled]="!!disabledEnvs[tab] && disabledEnvs[tab] !== row[tab + 'Domain'].environment"
                              [checked]="selections[tab].isSelected(row)">
                </mat-checkbox>
              </td>
            </ng-container>

            <ng-container matColumnDef="fullPath">
              <th mat-header-cell *matHeaderCellDef>Path</th>
              <td [ngStyle]="{width: maxItemsLength+'px'}" mat-cell *matCellDef="let element">
                <ng-container [ngSwitch]="element.type">
            <span class="badge bg-violetdark mr-10 ml-5" *ngSwitchCase="'entity'"
                  title="{{'ALL.reseller' | translate}}">R</span>
                  <span class="badge bg-fushsia mr-10 ml-5" *ngSwitchCase="'brand'"
                        title="{{'ALL.operator' | translate}}">O</span>
                  <span class="badge bg-aquamarin mr-10 ml-5" *ngSwitchCase="'merchant'"
                        title="{{'ALL.merchant' | translate}}">O</span>
                  <span class="badge bg-lime mr-10 ml-5" *ngSwitchCase="'liveStudio'"
                        title="{{'ALL.liveStudio' | translate}}">S</span>
                </ng-container>
                <entity-linker [links]="element.fullPath" (lickClick)="onLinkClick($event)"></entity-linker>
              </td>
            </ng-container>

            <ng-container matColumnDef="text">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
              <td mat-cell *matCellDef="let element">
                {{element.text}}
              </td>
            </ng-container>

            <ng-container matColumnDef="dynamicDomain.environment">
              <th mat-header-cell *matHeaderCellDef>Server</th>
              <td mat-cell *matCellDef="let element" class="environment"> {{element.dynamicDomain.environment}} </td>
            </ng-container>

            <ng-container matColumnDef="dynamicDomain.domain">
              <th mat-header-cell *matHeaderCellDef>Domain</th>
              <td mat-cell *matCellDef="let element">
                <div class="col-with-actions">
                  <div *ngIf="editedItems.dynamic[element.key] as newDomain"
                       class="new-domain"
                       [class.new-domain__error]="newDomain.hasError">
                    {{newDomain.newDomainName}}
                  </div>
                  <div>{{element.dynamicDomain.domain}}</div>
                  <div class="domain-inherited" *ngIf="element.dynamicDomain.inherited">(inherited)</div>
                  <div class="actions">
                    <button mat-icon-button
                            matTooltip="undo"
                            *ngIf="editedItems.dynamic[element.key]?.canUndo"
                            (click)="undo('dynamic', element.key, true)">
                      <mat-icon>undo</mat-icon>
                    </button>
                    <button mat-icon-button
                            matTooltip="inherit"
                            *ngIf="!element.dynamicDomain.inherited && (!isSuperadmin || element.path)"
                            (click)="updateItemWithChildren(element.key, 'inherited', true)">
                      <mat-icon>call_merge</mat-icon>
                    </button>
                    <button mat-icon-button
                            matTooltip="edit"
                            (click)="onItemEditClick(element)">
                      <mat-icon>edit</mat-icon>
                    </button>
                  </div>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="staticDomain.domain">
              <th mat-header-cell *matHeaderCellDef>Domain</th>
              <td mat-cell *matCellDef="let element">
                <div class="col-with-actions">
                  <div *ngIf="editedItems.static[element.key] as newDomain"
                       class="new-domain"
                       [class.new-domain__error]="newDomain.hasError">
                    {{newDomain.newDomainName}}
                  </div>
                  <div>{{element.staticDomain.domain}}</div>
                  <div class="domain-inherited" *ngIf="element.staticDomain.inherited">(inherited)</div>
                  <div class="actions">
                    <button mat-icon-button
                            matTooltip="undo"
                            *ngIf="editedItems.static[element.key]?.canUndo"
                            (click)="undo('static', element.key, true)">
                      <mat-icon>undo</mat-icon>
                    </button>
                    <button mat-icon-button
                            matTooltip="inherit"
                            *ngIf="!element.staticDomain.inherited"
                            (click)="updateItemWithChildren(element.key, 'inherited', true)">
                      <mat-icon>call_merge</mat-icon>
                    </button>
                    <button mat-icon-button
                            matTooltip="edit"
                            (click)="onItemEditClick(element)">
                      <mat-icon>edit</mat-icon>
                    </button>
                  </div>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row class="entity-table__row" [class.odd]="row.index % 2 === 0"
                [attr.id]="row.text"
                *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </cdk-virtual-scroll-viewport>
      </div>
    </ng-container>

    <ng-template #noItemsFound>
      <ng-container *ngIf="!loading">
        <div *ngIf="filter.value" class="no-items-message">{{ 'ALL.noItemsFound' | translate }}</div>
        <div *ngIf="!filter.value" class="no-items-message">{{ 'BULK_ACTIONS.notify' | translate }}</div>
      </ng-container>
    </ng-template>

    <div class="spinner-wrapper" *ngIf="loading">
      <mat-spinner diameter="60"></mat-spinner>
    </div>
  </mat-card>

</div>
