.page-wrapper {
  display: block;
  height: calc(100vh - 54px);
  width: 100%;
  overflow: auto;
}

.actions-wrapper {
  height: 100%
}

.spinner-wrapper {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  top: 150px;
}

.top-panel {
  display: flex;
  justify-content: space-between;
  padding: 12px;
}

.search-block {
  display: flex;
  align-items: center;

  & > * {
    margin-right: 12px;
  }
}

.radio-button {
  margin-right: 12px;
}

.card {
  margin: 32px;
  padding: 0 !important;
}

.table-tree {
  width: 100%;
  max-width: 100%;
}

.table-tree__text {
  position: relative;
}

.table-tree__editable {
  display: none;
  position: absolute;
  left: 100%;
  top: 0;
  color: #333;
  padding-left: 5px;
  padding-right: 5px;
}

.entity-table-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.actions {
  display: flex;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(0, -50%);
  opacity: 0;
  color: #0e60ca;
  background-color: #eee;
  height: 48px;
  padding: inherit;
}

.new-domain {
  color: #43A047;

  &__error {
    color: #D84315;
  }
}

.domain-inherited {
  color: #43A047;
  opacity: .8;
}

.entity-table {
  table-layout: auto;
  width: 100%;

  .entity-table__row {
    position: relative;
    height: 48px;

    &.odd {
      background-color: #eef1f5;
    }

    &:hover {
      .actions {
        opacity: 1;
      }
    }

    &.found {
      background-color: rgba(yellow, 0.2);
    }

    .col-with-actions {
      position: relative;
    }

    td {
      font-size: 14px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .environment {
      min-width: 50px;
      padding: 0 15px;
    }
  }

  .mat-header-cell {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .table-cell {
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.table-tree {
  thead > tr {
    transition: background-color ease-in-out 0.15s;

    > th {
      border-bottom: 1px solid #bbb;
      padding: 12px 20px;
      line-height: 1.5384616;
      vertical-align: top;
    }

    &:first-child {
      padding-left: 28px;
    }
  }

  tbody > tr {
    transition: background-color ease-in-out 0.15s;

    > td {
      position: relative;
      padding: 12px 20px;
      line-height: 1.5384616;
      vertical-align: top;
      border-top: 1px solid #ddd;

      &:first-child {
        border: none;
        padding: 0;
        vertical-align: top;

        .td-wrapper:hover .table-tree__editable {
          display: block;
        }
      }
    }

    &:first-child > td:first-child .td-wrapper {
      border-top: 1px solid transparent;
      border-left: 1px solid transparent;
    }

    &.tr-highlight > td {
      &:not(:first-child), &:first-child > .td-wrapper {
        background-color: #f8f8f8;
      }
    }

    &.tr-muted > td {
      &:not(:first-child) {
        background-color: #fcfcfc;
        color: #999;

        .table-tree__dropdown, .label, > a {
          opacity: 0.5;
        }
      }

      &:first-child > .td-wrapper {
        background-color: #fcfcfc;
        color: #999;

        .badge {
          opacity: 0.5;
        }
      }
    }
  }
}

.bulk-actions-notify {
  padding: 32px 32px 0;
}

.no-items-message {
  padding: 32px;
  text-align: center;
}
