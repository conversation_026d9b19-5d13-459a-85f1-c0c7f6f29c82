import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_NAMES } from '../../../../app.constants';
import { DynamicDomainsResolver } from '../../../../common/services/resolvers/dynamic-domains.resolver';
import { DynamicEntitydomainResolver } from '../../../../common/services/resolvers/dynamic-entitydomain.resolver';
import { StaticDomainsResolver } from '../../../../common/services/resolvers/static-domains.resolver';
import { StaticEntityDomainResolver } from '../../../../common/services/resolvers/static-entity-domain-resolver.service';
import { EntityBulkActionsComponent } from './entity-bulk-actions.component';


const routes: Routes = [
  {
    path: '',
    component: EntityBulkActionsComponent,
    data: {
      permissions: [PERMISSIONS_NAMES.KEYENTITY_ENTITYDOMAIN_BULKOPERATION],
    },
    resolve: {
      dynamicDomains: DynamicDomainsResolver,
      staticDomains: StaticDomainsResolver,
      dynamicEntityDomain: DynamicEntitydomainResolver,
      staticEntityDomain: StaticEntityDomainResolver,
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EntityBulkActionsRoutingModule {
}
