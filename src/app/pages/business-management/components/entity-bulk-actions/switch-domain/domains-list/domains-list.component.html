<h5>{{title}}</h5>
<mat-form-field class="search-field">
  <mat-label>Search</mat-label>
  <input tabindex="-1" matInput trimValue [formControl]="searchControl" autocomplete="off"/>
</mat-form-field>
<div class="domains-table">
  <ng-container *ngIf="componentName !== 'staticDomain'; else staticDomainTable">
    <table mat-table [dataSource]="foundDomainsList">
      <ng-container matColumnDef="environment">
        <th mat-header-cell *matHeaderCellDef> Server</th>
        <td mat-cell *matCellDef="let domain"> {{domain.environment}} </td>
      </ng-container>
      <ng-container matColumnDef="domain">
        <th mat-header-cell *matHeaderCellDef> Domain</th>
        <td mat-cell *matCellDef="let domain"> {{domain.domain}} </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="['environment', 'domain']; sticky: true"></tr>
      <tr mat-row
          class="domain-row"
          *matRowDef="let row; let i = index; columns: ['environment', 'domain'];"
          (click)="selectDomain(i)"
          [ngClass]="{'row__selected': selectedDomain?.id === row.id}"></tr>
    </table>
  </ng-container>
  <ng-template #staticDomainTable>
    <table mat-table [dataSource]="foundDomainsList">
      <ng-container matColumnDef="domain">
        <th mat-header-cell *matHeaderCellDef> Domain</th>
        <td mat-cell *matCellDef="let domain"> {{domain.domain}} </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="['domain']; sticky: true"></tr>
      <tr mat-row
          class="domain-row"
          *matRowDef="let row; let i = index; columns: ['domain'];"
          (click)="selectDomain(i)"
          [ngClass]="{'row__selected': selectedDomain?.id === row.id}"></tr>
    </table>
  </ng-template>
</div>
