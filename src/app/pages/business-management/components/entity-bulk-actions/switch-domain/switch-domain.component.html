<h2 mat-dialog-title>Switch {{dialogData.type}} domain</h2>
<div class="domains">
  <domains-list class="domains-switcher"
                [title]="'Switch from'"
                [componentName]="dialogData.componentName"
                [domainsList]="domainsList"
                (onSelect)="selectFromDomain($event)">
  </domains-list>
  <domains-list class="domains-switcher"
                [title]="'Switch to'"
                [componentName]="dialogData.componentName"
                [domainsList]="domainsList"
                [environment]="selectedFromDomain?.environment"
                (onSelect)="selectToDomain($event)">
  </domains-list>
</div>

<div mat-dialog-actions [align]="'end'">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{'USERS.ROLES.MODAL.btnClose' | translate}}
  </button>
  <button mat-flat-button
          class="mat-button-md"
          [disabled]="!selectedFromDomain || !selectedToDomain"
          (click)="submit()" color="primary">
    {{'USERS.ROLES.MODAL.btnSave' | translate}}
  </button>
</div>
