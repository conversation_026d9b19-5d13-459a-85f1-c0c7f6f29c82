import { Injectable } from '@angular/core';
import { BehaviorSubject, from, Subject } from 'rxjs';
import { groupBy, map, mergeMap, reduce, share, switchAll, tap, toArray } from 'rxjs/operators';

@Injectable()
export class EntityBulkActionsService {

  public changes = new Subject<{
    key: string;
    controlName: string;
    newValue: string
  }>();

  public dynamicDomains$ = new BehaviorSubject([]);
  public dynamicDomainsMap$: any;
  public dynamicDomainsOptionGroups$: any;

  public staticDomains$ = new BehaviorSubject([]);
  public staticDomainsMap$: any;
  public staticDomainsOptionGroups$: any;

  constructor(
  ) {
    console.log('EntityBulkActionsService');
    // this.staticDomains$ = route.data
    //   .do(console.log.bind(this))
    //   .pluck('staticDomains')
    //   .do(console.log.bind(this))
    //   .filter(domains => domains !== undefined)
    //   .share();
    // this.dynamicDomains$ = route.data.pluck('dynamicDomains')
    //   .filter(domains => domains !== undefined).share();

    this.staticDomainsMap$ = this.staticDomains$
      .asObservable()
      .pipe(
        reduce(( res, i ) => {
          res[i['id']] = i;
          return res;
        }, {}),
        share()
      )
    ;

    this.dynamicDomainsMap$ = this.dynamicDomains$
       .asObservable()
      .pipe(
        // tap(data => console.log('dynamicDomainsMap$', data)),
        reduce(( res, i ) => {
          res[i['id']] = i;
          return res;
        }, {}),
        share(),
        tap(console.log.bind(this))
      );

    this.dynamicDomainsOptionGroups$ = this.dynamicDomains$
      .pipe(
        map(
          list => list.sort(( a, b ) => a.domain < b.domain ? -1 : 1)
        ),
        map(domains => from(domains)),
        switchAll(),
        // tap(domain => console.log(domain)),
        groupBy(d => d.environment),
        mergeMap(group => group.pipe(toArray()))
      );

    this.staticDomainsOptionGroups$ = this.staticDomains$
      .pipe(
        map(
          list => list.sort(( a, b ) => a.domain < b.domain ? -1 : 1)
        ),
        map(domains => from(domains)),
        switchAll(),
        // tap(domain => console.log(domain)),
        groupBy(d => d.environment),
        mergeMap(group => group.pipe(toArray()))
      );
  }


}
