import { Domain } from '../../../../common/models/domain.model';
import { EntityShortInterface } from '../../../../common/typings';

export interface BulkActionItemChange {
  type: string;
  key: string;
  controlName?: string;
  newValue?: string;
  visibility?: boolean;
}

export class BulkEntitySettingsItem {
  id: string;
  path: string;
  text: string;
  childrenIds: Array<string>;
  type: string;
  key: string;
  parentKey: string;
  dynamicDomain?: {
    inherited: boolean,
    id: string,
    environment?: string
  };
  staticDomain?: {
    inherited: boolean,
    id: string,
  };
  hasInputChanges: Record<string, boolean> = {};
  index?: number;
  isVisible: boolean;
  fullPath: string[];

  constructor( data: BulkEntitySettingsItem ) {
    Object.assign(this, data);
  }
}


export interface EntitiesStructureToBulkOptionsParams {
  item: EntityShortInterface;
  dynamicDomainId?: string;
  staticDomainId?: string;
  dynamicDomainsMap?: { [p: string]: Domain };
  staticDomainsMap?: { [p: string]: Domain };
  parent?: Partial<BulkEntitySettingsItem>;
  resultObject: Record<string, BulkEntitySettingsItem>;
}

export function entitiesStructureToBulkOptions( value: EntitiesStructureToBulkOptionsParams ) {
  const { item, dynamicDomainId, staticDomainId, dynamicDomainsMap, staticDomainsMap, parent: parentItem, resultObject } = value;
  const { id, type, key } = item;
  const path = item.path === ':' ? '' : item.path;

  const dynamicId = dynamicDomainId || parentItem?.dynamicDomain?.id;
  const dynamic = dynamicId && dynamicDomainsMap && dynamicDomainsMap[dynamicId];
  const dynamicDomain = dynamic && {
    inherited: !dynamicDomainId,
    id: dynamicId,
    domain: dynamic.domain,
    environment: dynamic.environment,
  };

  const staticId = staticDomainId || parentItem?.staticDomain?.id;
  const fixed = staticId && staticDomainsMap && staticDomainsMap[staticId];
  const staticDomain = fixed && {
    inherited: !staticDomainId,
    id: staticId,
    domain: fixed.domain,
  };

  const fullPath = [...(parentItem?.fullPath || [])];

  if (parentItem?.text) {
    fullPath.push(parentItem.text);
  }

  const result = new BulkEntitySettingsItem({
    id,
    path,
    text: item.name,
    childrenIds: (item.child || []).map(child => child.key),
    type,
    key,
    parentKey: parentItem?.key,
    dynamicDomain,
    staticDomain,
    hasInputChanges: {},
    isVisible: false,
    fullPath
  });
  resultObject[result.key] = result;

  if (item.child) {
    item.child.forEach(child => entitiesStructureToBulkOptions({
      item: child,
      dynamicDomainId: child.dynamicDomainId,
      staticDomainId: child.staticDomainId,
      dynamicDomainsMap,
      staticDomainsMap,
      parent: result,
      resultObject
    }));
  }

  return resultObject;
}

export interface StaticDomain {
  'id': string;
  'domain': string;
  'createdAt': string;
  'updatedAt': string;
}

export interface DynamicDomain extends StaticDomain {
  'environment': string;
}
