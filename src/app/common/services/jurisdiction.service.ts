import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwHubEntityService, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, of, ReplaySubject, Subject, throwError } from 'rxjs';
import { catchError, filter, share, switchMap } from 'rxjs/operators';
import { BaseService } from '../base';
import { Jurisdiction } from '../typings/jurisdiction';

const COMMON_JURISDICTION_URL = '/jurisdictions';

@Injectable()
export class JurisdictionService implements SwuiGridDataService<Jurisdiction> {
  public urlList: string = COMMON_JURISDICTION_URL;

  public _item: Subject<Jurisdiction>;
  public _items: Subject<Jurisdiction[]>;

  private cachedJurisdictions: Jurisdiction[];
  private baseApiEndpoint: string = BaseService.apiEndpoint;
  private currentJurisdiction$ = new ReplaySubject<string[]>(1);

  constructor(public notifications: SwuiNotificationsService,
              public http: HttpClient,
              private hubEntityService: SwHubEntityService) {
    this.listenEntity();
  }

  getGridData(params: HttpParams): Observable<HttpResponse<Jurisdiction[]>> {
    return this.http.get<Jurisdiction[]>(this.getUrl(), {
      params,
      observe: 'response'
    }).pipe(
      catchError((err) => this.handleErrors(err))
    );
  }

  public getUrl( path?, url: string = this.urlList ): string {
    return `${this.baseApiEndpoint}${path ? '/entities/' + path : ''}${url}`;
  }

  public patchJurisdiction(jurisdiction: Jurisdiction, path?: string){
    const url = `${this.urlList}/${jurisdiction.code}`;
    return this.http.patch(this.getUrl(path, url), jurisdiction)
      .pipe(
        catchError((err) => this.handleErrors(err))
      );
  }

  public createJurisdiction(jurisdiction: Jurisdiction){
    const url = `${this.urlList}`;
    return this.http.post(this.getUrl('', url), jurisdiction)
      .pipe(
        catchError((err) => this.handleErrors(err))
      );
  }

  public getEntityJurisdictions( path?: string ): Observable<Jurisdiction[]> {
    return this._getJurisdictions<Jurisdiction>(path).pipe(catchError(() => of([])));
  }

  public getJurisdictions( ): Observable<Jurisdiction[]> {
    if (this.cachedJurisdictions) {
      this._items.next(this.cachedJurisdictions);
      return;
    }

    let source = this._getJurisdictions<Jurisdiction>();
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public addEntityJurisdiction( path: string, code: string ): Observable<any> {
    let url = `${this.getUrl(path)}/${code}`;
    let body = {};

    return this.http.put(url, body).pipe(
      catchError(this.handleErrors.bind(this))
    );
  }

  public deleteEntityJurisdiction( path: string, code: string, force: string = 'false' ): Observable<any> {
    let url = `${this.getUrl(path)}/${code}`;

    return this.http.delete(url, {
      params: {
        force: force
      }
    }).pipe(
      catchError(this.handleErrors.bind(this))
    );
  }

  private _getJurisdictions<K>( path? ): Observable<K[]> {
    console.log('JurisdictionService service: get Jurisdictions');

    if (path === ':') path = undefined;

    const url = this.getUrl(path);
    const source = this.http.get<K[]>(url);

    source.pipe(
      share(),
      catchError(err => this.handleErrors.call(this, err))
    );

    return source;
  }

  private handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }

  get entityJurisdiction(): Observable<string[]> {
    return this.currentJurisdiction$.asObservable();
  }

  private listenEntity() {
    this.hubEntityService.entitySelected$
      .pipe(
        filter(data => !!data),
        switchMap(({path}) => this.getEntityJurisdictions(path))
      )
      .subscribe(jurisdictions => {
        if (!jurisdictions?.length) {
          this.currentJurisdiction$.next([]);
          return;
        }

        this.currentJurisdiction$.next(jurisdictions.map(({ code }) => code));
      });
  }
}
