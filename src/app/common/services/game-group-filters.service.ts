import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, GridRequestData, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { API_ENDPOINT } from '../../app.constants';
import { GameGroupFilter } from '../models/game-group.model';

@Injectable()
export class GameGroupFiltersService implements GridDataService<GameGroupFilter> {

  public urlGameGroups = '/gamegroups';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ): Observable<HttpResponse<GameGroupFilter[]>> {
    let path = typeof requestData !== 'undefined' && requestData.hasOwnProperty('path') ?
      requestData['path'] : '';

    return this.http
      .get<GameGroupFilter[]>(this.getUrl(path), {
        params,
        observe: 'response'
      }).pipe(
        catchError(err => this.handleErrors(err)),
        share()
      );
  }

  getList( path ): Observable<GameGroupFilter[]> {
    let url = this.getUrl(path);

    return this.http.get<GameGroupFilter[]>(url)
      .pipe(
        share(),
      );
  }

  createGameGroupFilter( path, gameGroup, body ) {
    let url = `${this.getUrl(path, this.urlGameGroups)}/${gameGroup}/filters`;

    return this.http
      .post(url, body)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  updateGameGroupFilter( path, gameGroup, filterId, body ) {
    let url = `${this.getUrl(path, this.urlGameGroups)}/${gameGroup}/filters/${filterId}`;

    return this.http
      .put(url, body)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  deleteGameGroupFilter( path, gameGroup, filterId ) {
    let url = `${this.getUrl(path, this.urlGameGroups)}/${gameGroup}/filters/${filterId}`;

    return this.http
      .delete(url)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  getUrl( path?: string, urlList: string = '/gamegroups/filters' ): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}${urlList}`;
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
