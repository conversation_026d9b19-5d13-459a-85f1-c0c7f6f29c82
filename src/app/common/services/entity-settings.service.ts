import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import { catchError, map, mergeMap, share, tap } from 'rxjs/operators';

import { BaseService } from '../base';
import {
  createDefaultTwoFAEmailTemplate,
  createDefaultTwoFASettings,
  createDefaultTwoFASmsTemplate,
  EntitySettingsModel,
  TwoFASettings,
} from '../models/entity-settings.model';


@Injectable()
export class EntitySettingsService<T extends EntitySettingsModel> extends BaseService<EntitySettingsModel> {

  public urlSettingsGet: string = '/settings';
  public urlSettingsSave: string = '/settings';
  public urlSettingsRemove: string = '/settings';
  public urlRestrictedCountries: string = '/restricted-countries-solution';

  public _item: Subject<T>;

  private _baseApiEndpoint: string = BaseService.apiEndpoint;
  private settingsMap: Record<string, ReplaySubject<T>> = {};

  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
    super(notifications, http);
  }

  public getSettings( path: string = '', onlyOwnSettings: boolean = false, canUseCache?: boolean ): Observable<T> {
    if (path === ':') {
      path = '';
    }

    if (this.settingsMap[path]) {
      if (canUseCache) {
        return this.settingsMap[path];
      }
    } else {
      this.settingsMap[path] = new ReplaySubject(1);
    }

    const url = this.getUrl(path, this.urlSettingsGet);
    let params = new HttpParams();
    if (onlyOwnSettings) {
      params = params.set('onlyOwnSettings', 'true');
    }
    return this.http.get<T>(url, { params }).pipe(
      map(settings => {
        if (settings && settings.twoFactorAuthSettings) {

          const authOptionsNotArray = typeof settings.twoFactorAuthSettings.authOptions === 'object'
            && Array.isArray(settings.twoFactorAuthSettings.authOptions) === false;

          if (authOptionsNotArray) {
            settings.twoFactorAuthSettings.authOptions = [];
          }
        }
        return settings;
      }),
      tap(settings => {
        this.settingsMap[path].next(settings);
      })
    );
  }

  public patchSettings( settings: Object, path: string = '' ): Observable<T> {
    if (path === ':') {
      path = '';
    }

    delete this.settingsMap[path];

    const url = this.getUrl(path, this.urlSettingsSave);
    return this.http.patch<T>(url, settings).pipe(
      catchError(err => this.handleErrors(err))
    );
  }

  public patchRestrictedCountries( useCountriesFromJurisdiction: boolean | null, path: string = '' ): Observable<T> {
    if (path === ':') {
      path = '';
    }

    delete this.settingsMap[path];

    const url = this.getUrl(path, this.urlRestrictedCountries);
    return this.http.patch<T>(url, {useCountriesFromJurisdiction}).pipe(
      catchError(err => this.handleErrors(err))
    );
  }

  public resetSettingsToDefault( body: Object, path: string = ':' ): Observable<T> {
    const url = path !== ':'
      ? `${this._baseApiEndpoint}/entities/${path}${this.urlSettingsRemove}`
      : `${this._baseApiEndpoint}${this.urlSettingsRemove}`;

    const method = 'DELETE';
    const source = this.http.request<T>(method, url, { body }).pipe(
      share()
    );

    source.subscribe(
      () => {},
      this.handleErrors.bind(this)
    );

    return source;
  }

  public disableTwoFAByPath( path: string = ':' ): Observable<T> {
    return this._toggleTwoFAByPath(false, path);
  }

  public enableTwoFAByPath( path: string = ':' ): Observable<T> {
    return this._toggleTwoFAByPath(true, path);
  }

  private _toggleTwoFAByPath( isAuthEnabled: boolean, path: string = ':' ): Observable<T> {
    return this.getSettings(path, true).pipe(
      mergeMap(( settings ) => {
        if (!settings.hasOwnProperty('twoFactorAuthSettings')) {
          Object.assign(settings, { twoFactorAuthSettings: createDefaultTwoFASettings() });
        } else {
          if (!settings.twoFactorAuthSettings.hasOwnProperty('authOptions')) {
            Object.assign(settings.twoFactorAuthSettings, { authOptions: [] });
          }
          if (!settings.twoFactorAuthSettings.hasOwnProperty('isAuthEnabled')) {
            Object.assign(settings.twoFactorAuthSettings, { isAuthEnabled: false });
          }
          if (!settings.twoFactorAuthSettings.hasOwnProperty('smsTemplates')) {
            Object.assign(settings.twoFactorAuthSettings,
              { smsTemplates: { default: createDefaultTwoFASmsTemplate() } });
          }
          if (!settings.twoFactorAuthSettings.hasOwnProperty('mailTemplates')) {
            Object.assign(settings.twoFactorAuthSettings,
              { mailTemplates: { default: createDefaultTwoFAEmailTemplate() } });
          }
        }

        let twoFactorAuthSettings: TwoFASettings = Object.assign({}, settings.twoFactorAuthSettings, { isAuthEnabled });
        let newSettings = Object.assign({}, settings, { twoFactorAuthSettings });
        if (!newSettings.twoFactorAuthSettings.isAuthEnabled) {
          newSettings.twoFactorAuthSettings.authOptions = [];
        }
        if (newSettings.twoFactorAuthSettings.hasOwnProperty('mailTemplate')) {
          delete newSettings.twoFactorAuthSettings['mailTemplate'];
        }

        return this.patchSettings({ twoFactorAuthSettings: newSettings.twoFactorAuthSettings }, path);
      })
    );
  }
}
