import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';

import { BaseService } from '../base';
import { HttpClient } from '@angular/common/http';
import { catchError, map, share } from 'rxjs/operators';

const COMMON_COUNTRY_URL = '/countries';

@Injectable()
export class CountryService<T> extends BaseService<T> {
  public urlList: string = COMMON_COUNTRY_URL;
  public urlGet: string = COMMON_COUNTRY_URL;
  public urlSave: string = COMMON_COUNTRY_URL;
  public urlCreate: string = COMMON_COUNTRY_URL;
  public urlRemove: string = COMMON_COUNTRY_URL;

  constructor( notifications: SwuiNotificationsService, http: HttpClient ) {
    super(notifications, http);
  }

  public getList( filter?, path? ): Observable<T[]> {
    if (path === ':') path = undefined;
    const fn = this.processRecord;

    const options = {
      params: BaseService.getRequestParams(filter)
    };

    const url = this.getUrl(path);
    const source = this.http.get<T[]>(url, options).pipe(
      map(response => (response as T[]).map(fn))
    );
    source.pipe(
      share(),
      catchError(err => this.handleErrors.call(this, err))
    );
    return source;
  }

  public deleteCountry( path: string, countryCode: string ) {
    const url = this.getUrl(path, `${this.urlRemove}/${countryCode}`);
    return this.http.delete(url).pipe(
      catchError(err => this.handleErrors.call(this, err)),
      share()
    );
  }
}
