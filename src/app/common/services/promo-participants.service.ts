import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, Subject } from 'rxjs';
import { map, share } from 'rxjs/operators';

import { ZERO_TIME } from '../../app.constants';
import { PromoPlayerInfo } from '../models/promotion.model';
import { BaseService } from '../base';
import { COMMON_PROMO_URL } from './promo.service';

@Injectable()
export class PromoParticipantsService<T extends PromoPlayerInfo> extends BaseService<T> {

  public promoPlayers$: Subject<PromoPlayerInfo[]> = new Subject();

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient,
  ) {
    super(notifications, http);
  }

  getGridData( params: HttpParams, data: GridRequestData ): Observable<HttpResponse<T[]>> {
    const { path, promotionId } = data;

    const url: string = this.getUrl(path, `${COMMON_PROMO_URL}/${promotionId}/players`);
    return this.http.get<T[]>(url, {
      params,
      observe: 'response'
    });
  }

  public getPromoPlayers( promoId: string, path: string = '', filter: any = {} ): Observable<T[]> {
    const url: string = this.getUrl(path, `${COMMON_PROMO_URL}/${promoId}/players`);
    const fn = this.processRecord.bind(this);
    const params = BaseService.getRequestParams(filter);

    const source$ = this.http.get<T[]>(url, {
      params,
      observe: 'response'
    }).pipe(
      map(response => {
        const data = response.body.map(fn) as T[];
        this.responseHeaders = this.getResponseParams(response);
        this._httpResponse.next(this.responseHeaders);
        return data;
      }),
      map(items => items.map(item => fn(item))),
      share(),
    );

    source$.subscribe(
      items => this.promoPlayers$.next(items),
      err => this.handleErrors.call(this, err)
    );

    return source$;
  }


  public processRecord( record: T ): T {
    ['expireAt'].forEach(property => {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    });
    record._meta = {
      expireAt: record.expireAt && moment(record.expireAt),
    };
    return record;
  }

}
