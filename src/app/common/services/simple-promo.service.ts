import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, Subject, throwError as observableThrowError } from 'rxjs';
import { catchError, map, share } from 'rxjs/operators';
import { SimplePromoFreebet } from '../models/simple-promo.model';
import { BaseService } from '../base';

const COMMON_PROMO_URL = '/promo';

@Injectable()
export class SimplePromoService<T extends SimplePromoFreebet> extends BaseService<T> {
  public urlList: string = `${COMMON_PROMO_URL}/`;
  public urlGet: string = `${COMMON_PROMO_URL}/`;
  public urlSave: string = COMMON_PROMO_URL;
  public urlCreate: string = COMMON_PROMO_URL;
  public urlRemove: string = COMMON_PROMO_URL;

  public urlSimplePromo: string = '/simple-promo/freebet';

  public urlChangesStatus: string = `${COMMON_PROMO_URL}/status`;

  public _item: Subject<T>;
  public _items: Subject<T[]>;


  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
    super(notifications, http);
  }

  public getList( filter?, path?: string ): Observable<T[]> {
    console.log('PromoService: get list');

    let fn = this.processRecord.bind(this);

    this.requestParams = BaseService.getRequestParams(filter);
    const url = this.getUrl(path, this.urlList);

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          const data = response.body.map(fn) as T[];
          this.responseHeaders = this.getResponseParams(response);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public createItem( data?: any ) {
    console.log('SimplePromoService: create element');
    delete data._meta;
    delete data.currency;
    delete data.type;

    Object.assign(data, {
      startDate: BaseService.convertDateToBackendValue(data.startDate),
      endDate: BaseService.convertDateToBackendValue(data.endDate)
    });

    const coinFloatReplacer = ( key, value ) => {
      let result = value;
      if (key === 'coin') {
        result = parseFloat(value);
      }
      return result;
    };

    let body = JSON.stringify(data, coinFloatReplacer);
    let fn = this.processRecord;
    const url = this.getItemURL(false, data, this.urlSimplePromo);

    return this.http.put<T>(url, body).pipe(
      map(response => fn(response)),
      catchError(error => observableThrowError(error))
    );
  }

  public getItem( category: T ): Observable<T> {
    const fn = this.processRecord.bind(this);
    const { id } = category;

    console.log('PromoService service: get element ', id);

    const source = this.http
      .get<T>(`${BaseService.apiEndpoint}${this.urlGet}${id}`, {
        observe: 'response'
      }).pipe(
        map(response => {
          this.responseHeaders = this.getResponseParams(response);
          const data = fn(response.body);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public updateItem( promotion: T ): Observable<T> {
    console.log('PromoService service: update element');

    delete promotion._meta;
    Object.assign(promotion, {
      startDate: BaseService.convertDateToBackendValue(promotion.startDate),
      endDate: BaseService.convertDateToBackendValue(promotion.endDate),
    });

    const body = JSON.stringify(promotion);
    let url = `${BaseService.apiEndpoint}${this.urlSimplePromo}`;
    let fn = this.processRecord;

    const source = this.http
      .patch(url, body).pipe(
        map(fn),
        share()
      );

    source.subscribe(
      () => {
      },
      this.handleErrors.bind(this),
    );

    return source;
  }

  public applyPromotionByFilter( promoId, filter?, path? ): Observable<any> {
    console.log('PromoService service: apply promotion by filter');

    this.requestParams = BaseService.getRequestParams(filter);
    let url = this.getUrl(path);
    url += `freebet/${promoId}/players/group`;

    const source = this.http
      .put(url, {}, {
        params: this.requestParams
      }).pipe(
        share()
      );
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public setNextItem( value: T ) {
    this._item.next(value);
  }

  public processRecord( record: T ): T {
    record._meta = {
      startDate: record.startDate && moment(record.startDate),
      endDate: record.endDate && moment(record.endDate),
      createdAt: record.createdAt && moment(record.createdAt),
    };

    return record;
  }


}
