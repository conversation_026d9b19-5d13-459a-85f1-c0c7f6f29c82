import { Injectable } from '@angular/core';
import { Observable, of, Subject } from 'rxjs';

import { BaseService } from '../base';
import { HttpClient } from '@angular/common/http';
import { catchError, map, share } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { getCurrencyLabel } from '../core/currecy-transform';
import { Currency } from '../typings';

// const COMMON_CURRENCY_URL = '/currency';
const COMMON_CURRENCY_URL = '/currencies';

@Injectable()
export class CurrencyService<T extends Currency> extends BaseService<T> {
  public urlList: string = COMMON_CURRENCY_URL;
  public urlGet: string = COMMON_CURRENCY_URL;
  public urlSave: string = COMMON_CURRENCY_URL;
  public urlCreate: string = COMMON_CURRENCY_URL;
  public urlRemove: string = COMMON_CURRENCY_URL;

  public _item: Subject<T>;
  public _items: Subject<T[]>;

  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
    super(notifications, http);
  }

  public getList( filter?, path? ): Observable<T[]> {
    console.log('CurrencyService service: get list');

    if (path === ':') path = undefined;
    let fn = this.processRecord;
    let url = this.getUrl(path);

    const source = this.http
      .get<T[]>(url, {
        params: BaseService.getRequestParams(filter)
      }).pipe(
        catchError(() => of<Currency[]>([])),
        map(response => response.map((curr) => ({...curr, label: getCurrencyLabel(curr.code)}))),
        map(response => response.map(fn))
      );

    source.pipe(
      share(),
      catchError(err => this.handleErrors.call(this, err))
    );
    return source;
  }

  public addCurrency( path: string, currencyCode: string ): Observable<T[]> {
    const currencyUrl = `${this.urlCreate}/${currencyCode}`;

    const source = this.http
      .put<T[]>(this.getUrl(path, currencyUrl), null).pipe(
        share()
      );

    source.subscribe(
      () => {
      },
      this.handleErrors.bind(this)
    );

    return source;
  }

  public deleteCurrency( path: string, currencyCode: string ) {
    const currencyUrl = `${this.urlRemove}/${currencyCode}`;
    return this.http
      .delete(this.getUrl(path, currencyUrl)).pipe(
        share(),
        catchError(err => this.handleErrors.call(this, err))
      );
  }
}
