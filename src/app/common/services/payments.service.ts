import { ActivatedRoute } from '@angular/router';
import { GridDataService } from '@skywind-group/lib-swui';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { share, tap } from 'rxjs/operators';

import { BaseService } from '../base';
import { DepositInfo, WithdrawalInfo } from '../typings';

export abstract class PaymentsService<T extends DepositInfo | WithdrawalInfo> implements GridDataService<T> {
  public urlList: string = '/payments/';

  path: string;
  id: string;
  type: string;

  protected constructor( readonly route: ActivatedRoute,
                         readonly http: HttpClient,
                         readonly typeIn: string,
  ) {
    const { snapshot: { params: { path: path, id: id } } } = this.route;
    this.path = path;
    this.id = id;
    this.type = typeIn;
  }

  public getGridData( params: HttpParams ): Observable<HttpResponse<T[]>> {
    params = params.set('playerCode', this.id).set('orderType__in', this.type);

    return this.http
      .get<T[]>(this.getUrl(this.path), {
        params,
        observe: 'response'
      }).pipe(
        tap(( response ) => {
          response.body.forEach(( item ) => this.processRecord(item));
        }),
        share()
      );
  }

  public getUrl( path?, urlList: string = this.urlList ): string {
    return `${BaseService.apiEndpoint}${path && path !== ':' ? '/entities/' + path : ''}${urlList}`;
  }

  protected abstract processRecord( record: T ): T;
}
