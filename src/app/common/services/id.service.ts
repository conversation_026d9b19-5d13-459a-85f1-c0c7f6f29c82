import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs/Observable';
import { BaseService } from '../base';
import { catchError, map } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

@Injectable()
export class IdService {
  constructor(private http: HttpClient,
              private notifications: SwuiNotificationsService) {
  }

  encode(ids: string[]): Observable<string> {
    return this.http.post<string[]>(`${BaseService.apiEndpoint}/ids/encode`, ids).pipe(
      catchError(err => {
        this.handleErrors(err);
        return throwError(err);
      }),
      map(res => JSON.stringify(res))
    );
  }

  decode(ids: string[]): Observable<string> {
    return this.http.post<string[]>(`${BaseService.apiEndpoint}/ids/decode`, ids)
      .pipe(
        catchError(err => {
          this.handleErrors(err);
          return throwError(err);
        }),
        map(res => JSON.stringify(res))
      );
  }

  private handleErrors(err): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
