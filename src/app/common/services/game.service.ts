import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { SwuiGridDataService } from '@skywind-group/lib-swui/swui-schema-grid/services/grid-data.service';
import { Observable, of, ReplaySubject, Subject, throwError } from 'rxjs';
import { catchError, first, map, share, shareReplay, switchMap, tap, toArray } from 'rxjs/operators';

import { BaseService } from '../base';
import { GameImpl } from '../models/game.model';
import { ChangeEntityGameData, Game, Label, PlayerGameURLInfo } from '../typings';
import { CsvSchema, CsvService } from './csv.service';
import { transformCurrencyValue } from '../core/currecy-transform';

const csvSchema: CsvSchema[] = [
  {
    name: 'code',
    title: 'ENTITY_SETUP.GAMES.CSV.gameCode'
  },
  {
    name: 'title',
    title: 'ENTITY_SETUP.GAMES.CSV.gameName',
    transform( value: any ): string {
      return value.split(',').join(' ');
    }
  },
  {
    name: 'type',
    title: 'ENTITY_SETUP.GAMES.type',
  },
  {
    name: 'status',
    title: 'ENTITY_SETUP.GAMES.CSV.status'
  },
  {
    name: 'jpGame',
    title: 'ENTITY_SETUP.GAMES.CSV.jpGame',
    transform( _: any, game: Game ): string {
      return !!game.settings?.jackpotId ? 'Yes' : 'No';
    }
  },
];

const csvSchemaJpGames: CsvSchema[] = [
  {
    name: 'code',
    title: 'ENTITY_SETUP.GAMES.CSV.gameCode'
  },
  {
    name: 'title',
    title: 'ENTITY_SETUP.GAMES.CSV.gameName',
    transform( value: any ): string {
      return value.split(',').join(' ');
    }
  },
  {
    name: 'status',
    title: 'ENTITY_SETUP.GAMES.CSV.status'
  },
  {
    name: 'connectedPool',
    title: 'ENTITY_SETUP.GAMES.CSV.connectedPool',
    transform( _: any, game: Game ): string {
      if (game?.settings?.jackpotId) {
        return Object.keys(game?.settings?.jackpotId ?? {}).map(key => `${key} {${game?.settings?.jackpotId[key]}}`).join('; ');
      }
      if (game?.features?.jackpotTypes) {
        return (game.features?.jackpotTypes ?? []).map(type => `${type.toLowerCase()} {-}`).join('; ');
      }
      return '';
    }
  },
];

const csvDetailedSchemaJpGames: CsvSchema[] = [
  {
    name: 'gameCode',
    title: 'ENTITY_SETUP.GAMES.CSV.gameCode'
  },
  {
    name: 'jpType',
    title: 'ENTITY_SETUP.GAMES.CSV.jpType'
  },
  {
    name: 'jpId',
    title: 'ENTITY_SETUP.GAMES.CSV.jpId'
  },
  {
    name: 'configuredOn',
    title: 'ENTITY_SETUP.GAMES.CSV.configuredOn'
  },
  {
    name: 'isInherited',
    title: 'ENTITY_SETUP.GAMES.CSV.isInherited'
  },
  {
    name: 'businessEntityConfigurationLevel',
    title: 'ENTITY_SETUP.GAMES.CSV.businessEntityConfigurationLevel'
  },
  {
    name: 'billingConfigurationLevel',
    title: 'ENTITY_SETUP.GAMES.CSV.billingConfigurationLevel'
  }
];

const GAME_SETTINGS_STORAGE_NAME = 'gameSettings';

export const ERROR_GAME_DELETE_NEED_FORCE_FLAG: number = 302;

export const GAME_STATUSES = {
  NORMAL: 'normal',
  SUSPENDED: 'suspended',
  KILL_SESSION: 'kill_session',
  TEST: 'test',
  HIDDEN: 'hidden'
};

function getUrl( path: string, urlList: string ): string {
  return `${BaseService.apiEndpoint}${path && path !== ':' ? '/entities/' + path : ''}${urlList}`;
}

@Injectable()
export class GameService implements SwuiGridDataService<Game> {
  public _item: Subject<Game> = new Subject<Game>();
  public _items: Subject<Game[]> = new Subject<Game[]>();
  public _allGames: ReplaySubject<Game[]> = new ReplaySubject(1);
  public forceRemoveConfirm: Subject<string> = new Subject();
  public forceRemoveConfirmed: Subject<boolean> = new Subject();

  public gameSettings = Object.assign({},
    JSON.parse(localStorage.getItem(GAME_SETTINGS_STORAGE_NAME))
  );

  private listReplay$: Observable<Game[]>;

  private cachedItems: { [key: string]: Game[] } = {};

  private readonly suspended: string = '/suspended';

  constructor( protected readonly notifications: SwuiNotificationsService,
               protected readonly http: HttpClient,
               protected readonly csvService: CsvService
  ) {
  }

  unSuspendedGame( path: string, gameCode: string, isLive: boolean ) {
    const urlList = isLive ? '/live-games' : '/games';
    const params = new HttpParams().set('gameCode', `${gameCode}`);
    return this.http.delete(getUrl(path, `${urlList}/${gameCode}${this.suspended}`), { params })
      .pipe(
        catchError(err => {
          this.handleErrors(err);
          return throwError(err);
        }),
      );
  }

  getGridData( param: HttpParams, data: GridRequestData ): Observable<HttpResponse<Game[]>> {
    let fn = this.processRecord.bind(this);
    const {
      path, isSuperAdmin, changeStateDisabled, changeStateEnabled,
      jackpots, jpnDetailsAllowed, changeStateLiveDisabled, changeStateLiveEnabled, changeState, changeStateLive
    } = data;

    param = jackpots ? param.append('jackpotTypes', true).append('shortInfo', true) : param;
    return this.http
      .get<Game[]>(getUrl(path, '/games'), {
        params: param,
        observe: 'response'
      }).pipe(
        tap(( response ) => {
          response.body.forEach(( item ) => fn(item));
          this._allGames.next(response.body);
        }),
        map(( response: HttpResponse<Game[]> ) => {
          response.body.map(game => {
            game._meta = {
              isSuperAdmin: isSuperAdmin,
              changeStateDisabled: changeStateDisabled,
              changeStateEnabled: changeStateEnabled,
              jpnDetailsAllowed: jpnDetailsAllowed,
              changeStateLiveEnabled,
              changeStateLiveDisabled,
              changeState,
              changeStateLive
            };
            return game;
          });
          return new HttpResponse({
            body: response.body,
            headers: response.headers,
          });
        }),
        catchError(err => {
          this.handleErrors(err);
          return throwError(err);
        }),
      );
  }

  suspendedGame( path: string, gameCode: string, allowToFinishCurrentSession = false, isLive = false ) {
    const urlList = isLive ? '/live-games' : '/games';

    return this.http.put(getUrl(path, `${urlList}/${gameCode}${this.suspended}`),
      { gameCode: gameCode }, { params: { allowToFinishCurrentSession } })
      .pipe(
        catchError(err => {
          this.handleErrors(err);
          return throwError(err);
        }),
      );
  }

  setStatus( game, status: 'normal' | 'suspended' | 'test', path: string = '' ): Observable<Game> {
    const fn: ( record ) => Game = path && path !== ':' ? ( record ) => record as Game : this.processRecord;
    return this.http.patch(getUrl(path, `/games/${game.code}`), { status }).pipe(
      map(response => fn(response)),
      catchError(err => {
        this.handleErrors(err);
        return throwError(err);
      }),
    );
  }

  public getList( filter?, path?: string ): Observable<Game[]> {
    console.log('Game service: get list');

    let fn = this.processRecord.bind(this);

    const requestParams = BaseService.getRequestParams(filter);

    let url;
    if (path && path !== ':') {
      url = getUrl(path, '/games');
    } else {
      url = getUrl(undefined, '/games');
    }

    const source = this.http
      .get<Game[]>(url, {
        params: requestParams,
        observe: 'response'
      }).pipe(map(response => {
          let data = response.body.map(item => fn(item));
          data = data.map(fn);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  getFunUrl( data: any ): Observable<string> {
    return this.http.get<{ url: string }>(getUrl(data.path, `/fun/games/${data.gameCode}`), {
      params: data
    })
      .pipe(
        map(( { url } ) => url),
        catchError(this.handleErrors.bind(this))
      );
  }

  public getListReplay( filter?, path?: string ): Observable<Game[]> {
    console.log('Game service: get list replay');

    if (this.listReplay$) {
      return this.listReplay$;
    }

    let fn = this.processRecord.bind(this);

    const requestParams = BaseService.getRequestParams(filter);

    let url;
    if (path && path !== ':') {
      url = getUrl(path, '/games');
    } else {
      url = getUrl(undefined, '/games');
    }

    this.listReplay$ = this.http
      .get<Game[]>(url, {
        params: requestParams,
        observe: 'response'
      }).pipe(map(response => {
          let data = response.body;
          data = data.map(fn);
          return data;
        }),
        shareReplay()
      );

    this.listReplay$.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return this.listReplay$;
  }

  public getAllGames( path: string = ':', force: boolean = false, shortInfo?: boolean, jackpotTypes?: boolean ): Observable<Game[]> {
    if (!force && this.cachedItems.hasOwnProperty(path)) {
      let games = this.cachedItems[path] as Game[];
      this._allGames.next(games);
      return of(games);
    }

    let fn = this.processRecord.bind(this);

    let url;
    if (path && path !== ':') {
      url = getUrl(path, '/games');
    } else {
      url = getUrl(undefined, '/games');
    }

    let param: HttpParams = new HttpParams();
    if (jackpotTypes) {
      param = param.append('jackpotTypes', true);
    }
    param = param.append('limit', 10000).append('offset', 0).append('shortInfo', shortInfo);

    const source = this.http
      .get<Game[]>(url, {
        params: param
      }).pipe(
        map(response => response.map(fn) as Game[]),
        share()
      );

    source.subscribe(
      ( data: Game[] ) => {
        this.cachedItems[path] = data;
        this._allGames.next(data);
      },
      ( err ) => this.handleErrors.call(this, err)
    );

    return source;
  }

  public createItem( game: Game, _: string = ':' ): Observable<Game> {
    console.log('Games service: create element');
    const body = JSON.stringify(game);
    const url = `${BaseService.apiEndpoint}/games`;

    return this.http
      .post<Game>(url, body).pipe(
        share()
      );
  }

  public getItem( code: string ): Observable<Game> {
    const fn = this.processRecord.bind(this);

    console.log('Game service: get element ', code);

    const source = this.http
      .get<Game>(`${BaseService.apiEndpoint}/games/${code}`).pipe(
        map(response => fn(response)),
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public getGameLabels(): Observable<Label[]> {
    let params = { type: 'game' };

    return this.http
      .get<Label[]>(`${BaseService.apiEndpoint}/labels`, { params }).pipe(
        share()
      );
  }

  public getGameUrl( code ): Observable<PlayerGameURLInfo> {
    return this.http
      .get<PlayerGameURLInfo>(`${BaseService.apiEndpoint}/fun/games/${code}`).pipe(
        share()
      );
  }

  public processRecord( record ): Game {
    record._meta = {};
    return new GameImpl(record).toInfo() as Game;
  }

  public setEntityGames( codes: string[], path: string, remove: boolean = false ) {
    let method = remove ? 'DELETE' : 'POST';
    let body = { codes };
    let url = `${BaseService.apiEndpoint}/entities/${path}/games`;

    return this.http.request(method, url, { body }).pipe(
      catchError(( err ) => {
        return this.handleErrors(err);
      })
    );
  }

  public setEntityLiveGames( codes: string[], path: string, remove: boolean = false, force: boolean = false ) {
    let method = remove ? 'DELETE' : 'POST';
    let body = { codes };
    let url = `${BaseService.apiEndpoint}/entities/${path}/live-games`;

    if (force) {
      const params = new URLSearchParams();
      params.set('force', force.toString());
      url += `?${params}`;
      this.forceRemoveConfirmed.next(force);
    }

    return this.http.request(method, url, { body }).pipe(
      catchError(( err ) => {
        return this.handleErrors(err);
      })
    );
  }

  public removeEntityGame( gameCode: string, path: string, force?: boolean ) {
    let url = `${BaseService.apiEndpoint}/entities/${path}/games/${gameCode}`;
    const params = new HttpParams({fromObject: {force}});

    return this.http.delete(url, {params}).pipe(
      catchError(( err ) => {
        let { error } = err;
        let forceRequired = 'code' in error && error.code === ERROR_GAME_DELETE_NEED_FORCE_FLAG;
        if (forceRequired) {
          this.forceRemoveConfirmRequired(gameCode);
        }
        return this.handleErrors(err);
      })
    );
  }

  public removeEntityLiveGame( gameCode: string, path: string, force?: boolean ) {
    const url = `${BaseService.apiEndpoint}/entities/${path}/live-games/${gameCode}`;
    const params = new HttpParams({fromObject: {force}});

    return this.http.delete(url, {params}).pipe(
      catchError(( err ) => {
        let { error } = err;
        let forceRequired = 'code' in error && error.code === ERROR_GAME_DELETE_NEED_FORCE_FLAG;
        if (forceRequired) {
          this.forceRemoveConfirmRequired(gameCode);
        }
        return this.handleErrors(err);
      })
    );
  }

  public getGameByPath( gameCode: string, path: string ): Observable<Game> {
    const urlPath = path === ':' ? '' : path;
    const url = `${getUrl(urlPath, '/games')}/${gameCode}`;

    const game$ = this.http.get<Game>(url).pipe(
      share()
    );

    game$.subscribe(
      data => console.log('getGameByPath', gameCode, path, data),
      err => this.handleErrors.call(this, err)
    );

    return game$;
  }

  public updateRoyalties( gameCode: string, path: string, isLive: boolean, royalties: number ): Observable<Game> {
    console.log('Games service: updateRoyalties');
    return this.patchGameDetails(gameCode, path, isLive, { royalties });
  }

  public patchGameDetails( gameCode: string, path: string, isLive: boolean, body: ChangeEntityGameData ): Observable<Game> {
    return this.saveGameDetails('PATCH', gameCode, path, isLive, body);
  }

  public postGameDetails( gameCode: string, path: string, isLive: boolean, body: ChangeEntityGameData ): Observable<Game> {
    return this.saveGameDetails('POST', gameCode, path, isLive, body);
  }

  public forceRemoveConfirmRequired( gameCode: string ) {
    this.forceRemoveConfirm.next(gameCode);
  }

  public getListWithShortData( shortInfo?: boolean ): Observable<{ id: string; text: string }[]> {
    return this.getList({ pages: { limit: Infinity, shortInfo: shortInfo } }).pipe(
      first(),
      switchMap(r => r), // @TODO: to refactor
      map(( game: Game ) => {
        return {
          text: game.title,
          id: game.code,
          providerCode: game.providerCode
        };
      }),
      toArray()
    );
  }

  public getListReplayWithShortData( shortInfo?: boolean ): Observable<{ id: string; text: string }[]> {
    return this.getListReplay({ pages: { limit: Infinity, shortInfo: shortInfo } }).pipe(
      first(),
      switchMap(r => r), // @TODO: to refactor
      map(( game: Game ) => {
        return {
          text: game.title,
          id: game.code,
        };
      }),
      toArray()
    );
  }

  public cascadeAddGames( path: string, codes: string[] ) {
    const url = `${BaseService.apiEndpoint}/entities/group/${path}/games`;
    return this.http.post(url, { codes }).pipe(
      catchError(err => this.handleErrors.call(this, err)),
      share()
    );
  }

  public getEntityGame( gameCode: string, path?: string ): Observable<Game> {
    return this.http
      .get<Game>(getUrl(path, `/games/${gameCode}`)).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        map((item: Game) => {
          const money = ['balanceAfter', 'balanceBefore', 'bet', 'credit', 'debit', 'totalJpWin', 'win']
            .reduce((res, field) => {
              res[field] = transformCurrencyValue(item[field], item.currency);

              return res;
            }, {});

          return {
            ...item,
            ...money
          } as Game;
        }),
        share()
      );
  }

  public getGame( gameCode: string ): Observable<Game> {
    return this.http
      .get<Game>(`${BaseService.apiEndpoint}/game/${gameCode}`).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        map(item => <Game>item),
        share()
      );
  }

  public createGame( game ) {
    return this.http
      .post(`${BaseService.apiEndpoint}/game`, game).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        share()
      );
  }

  public patchGame( gameCode, body ) {
    return this.http
      .patch(`${BaseService.apiEndpoint}/game/${gameCode}`, body).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        share()
      );
  }

  public patchGames( gameCode, body ) {
    return this.http
      .patch(`${BaseService.apiEndpoint}/games/${gameCode}`, body).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        share()
      );
  }

  public downloadCsv( path: string, fileName: string ) {
    return this.getAllGames(path, true, true).pipe(
      tap(( games ) => {
        this.csvService.toCsv(csvSchema, games, fileName);
      })
    );
  }

  public downloadCsvJpGames( path: string, fileName: string ) {
    return this.getAllGames(path, true, true, true).pipe(
      tap(( games ) => {
        this.csvService.toCsv(csvSchemaJpGames, games, fileName);
      })
    );
  }

  public downloadCsvJpDefinitions( forcePath: string, fileName: string ): Observable<any> {
    const urlFn = (path) => getUrl(path, '/jackpots-configuration');
    return this.csvService.download(urlFn, csvDetailedSchemaJpGames, fileName, undefined, forcePath);
  }

  private saveGameDetails( method: string, gameCode: string, path: string, isLive: boolean, body: ChangeEntityGameData ): Observable<Game> {
    const urlList = isLive ? '/live-games' : '/games';
    const url = `${getUrl(path, urlList)}/${gameCode}`;
    return this.http
      .request<Game>(method, url, { body })
      .pipe(
        catchError(err => this.handleErrors.call(this, err)),
        map(item => <Game>item),
        share(),
      );
  }

  private handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
