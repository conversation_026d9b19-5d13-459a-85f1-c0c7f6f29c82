import { Injectable } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Resolve } from '@angular/router';
import { DynamicFormOptionData } from '@skywind-group/lib-swui/swui-dynamic-form/dynamic-form.model';

export interface WidgetOptions {
  [prop: string]: any;
}

export interface LobbyWidgets {
  [tag: string]: {
    title?: string;
    path: string;
    name: string;
    options?: WidgetOptions;
  };
}

export interface LobbyWidget {
  tag: string;
  title?: string;
  src: string;
  path: string;
  options?: WidgetOptions;
  targets?: string[];
  properties?: DynamicFormOptionData;
  disabled?: boolean;
}

export function removeObsoleteWidgetOptions( values: WidgetOptions | undefined, props: string[] ): WidgetOptions {
  return Object.entries(values || {})
    .filter(( [name] ) => props.includes(name))
    .reduce(( result, [name, value] ) => ({
      ...result,
      [name]: value
    }), {});
}

const URL = '/widgets';

@Injectable()
export class LobbyWidgetsService implements Resolve<LobbyWidget[]> {
  private readonly widgets: Observable<LobbyWidget[]>;

  constructor( http: HttpClient ) {
    this.widgets = http.get<LobbyWidgets>(`${URL}/index.json`).pipe(
      map(data => data ? Object.entries(data).reduce<LobbyWidget[]>(( result, [tag, { path, name, ...w }] ) => [
        ...result,
        { tag, src: `${path}/${name}`, path, ...w }
      ], []) : []),
      map(widgets => widgets.filter(( { disabled } ) => !disabled))
    );
  }

  resolve() {
    return this.widgets.pipe(
      catchError(err => {
        console.error(err);
        return [];
      })
    );
  }
}
