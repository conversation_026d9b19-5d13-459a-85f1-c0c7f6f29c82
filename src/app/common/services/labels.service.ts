import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, Subject, throwError } from 'rxjs';
import { catchError, map, share } from 'rxjs/operators';

import { BaseService } from '../base';
import { Label } from '../typings/label';

const LABELS_URL = '/labels';


export const LABEL_GROUP: { [key: string]: LabelGroupType } = {
  'class': 'class',
  'platform': 'platform',
  'feature': 'feature',
  'promotion': 'promotion',
  'entity': 'entity',
  'game': 'game',
};
export type LabelGroupType = 'class' | 'platform' | 'feature' | 'promotion' | 'entity' | 'game';

@Injectable()
export class LabelsService implements SwuiGridDataService<Label> {
  labelUrl = '/label';
  public urlList: string = LABELS_URL;
  public urlGet: string = LABELS_URL;
  public urlCreate: string = LABELS_URL;
  public urlGetLabelGroups: string = '/label-groups';

  public _item: Subject<Label> = new Subject<Label>();
  public _items: Subject<Label[]> = new Subject<Label[]>();

  private baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
  }

  getGridData(params: HttpParams): Observable<HttpResponse<Label[]>> {
    return this.http.get<Label[]>(this.getUrl(), {
      params,
      observe: 'response'
    }).pipe(
      map(( response: HttpResponse<Label[]> ) => {
        const headers = response.headers.set('X-Paging-Total', response.body.length.toString());

        return new HttpResponse({
          body: response.body,
          headers,
        });
      }),
      catchError((err) => this.handleErrors(err))
    );
  }

  public getPromoLabels(): Observable<Label[]> {
    return this._getListByGroups([], LABEL_GROUP.promotion);
  }

  public getGameLabels(): Observable<Label[]> {
    return this._getListByGroups([], LABEL_GROUP.game);
  }

  public getEntityLabels(): Observable<Label[]> {
    return this._getListByGroups([], LABEL_GROUP.entity);
  }

  getLabelGroups( type?: string, group?: string ) {
    let url = `${BaseService.apiEndpoint}${this.urlGetLabelGroups}`;
    let params = new HttpParams();
    if (type) {
      params = params.set('type', type);
      if (group) {
        params = params.set('group', group);
      }
    }

    return this.http
      .get<any[]>(url, { params })
      .pipe(
        catchError(err => this.handleErrors.call(this, err)),
        share(),
      );
  }

  public addLabel( body ) {
    let url = `${BaseService.apiEndpoint}/${this.urlList}`;

    return this.http
      .post(url, body)
      .pipe(
        catchError(err => this.handleErrors.call(this, err)),
      );
  }

  public addLabelGroup( body ) {
    let url = `${BaseService.apiEndpoint}/${this.urlGetLabelGroups}`;

    return this.http
      .post(url, body)
      .pipe(
        catchError(err => this.handleErrors.call(this, err)),
      );
  }

  public deleteLabel(labelId: string) {
    return this.http
      .delete(`${BaseService.apiEndpoint}/${this.labelUrl}`, {
        body: {
          id: labelId
        }
      })
      .pipe(
        catchError(err => this.handleErrors.call(this, err)),
      );
  }

  public processRecord( record ): Label {
    record._meta = {};
    // ...
    return record;
  }

  private _getListByGroups( groups: LabelGroupType[], type?: string ): Observable<Label[]> {
    let fn = this.processRecord;
    let url = `${this.baseApiEndpoint}${this.urlList}`;
    let params = {};

    if (groups.length === 1) {
      params['group'] = groups[0];
    } else if (groups.length > 1) {
      params['labelsGroup__in'] = groups.join(',');
    }

    if (type) {
      params = { type: type };
    }

    const source = this.http
      .get<Label[]>(url, { params }).pipe(
        map(response => response.map(fn)),
        share()
      );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  private handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }

  private getUrl( path?, url: string = this.urlList ): string {
    return `${this.baseApiEndpoint}${path ? '/entities/' + path : ''}${url}`;
  }
}
