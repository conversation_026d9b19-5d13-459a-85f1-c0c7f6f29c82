import { Injectable } from '@angular/core';
import { from, Observable } from 'rxjs';
import { delay, toArray } from 'rxjs/operators';

import { ChartDataField } from '../components/swHighcharts/highcharts.component';

@Injectable()
export class ChartsService {

  public getExampleData(): Observable<ChartDataField[]> {
    let data: ChartDataField[] = [
      {
        data: [49.9, 71.5, 106.4, 129.2, 144.0, 176.0, 135.6, 148.5, 216.4, 194.1, 95.6, 54.4]
      }, {
        data: [83.6, 78.8, 98.5, 93.4, 106.0, 84.5, 105.0, 104.3, 91.2, 83.5, 106.6, 92.3]
      }, {
        data: [48.9, 38.8, 39.3, 41.4, 47.0, 48.3, 59.0, 59.6, 52.4, 65.2, 59.3, 51.2]
      }, {
        data: [42.4, 33.2, 34.5, 39.7, 52.6, 75.5, 57.4, 60.4, 47.6, 39.1, 46.8, 51.1]
      }
    ];

    return from(data).pipe(toArray(), delay(400));
  }


  public getPaymentData(): Observable<ChartDataField[]> {
    let data: ChartDataField[] = [
      {
        data: [176000, 1190000, 2190000, 2220000, 2800000, 2800000, 2850000]
      }, {
        data: [167800, 1150000, 2140000, 2150000, 2720000, 2720000, 2730000]
      }
    ];

    return from(data).pipe(toArray(), delay(200));
  }

  public getAvgPaymentData(): Observable<ChartDataField[]> {
    let data: ChartDataField[] = [
      {
        data: [0.30, 0.27, 0.18, 0.18, 0.23, 0.32, 0.50]
      }, {
        data: [0.22, 0.21, 0.13, 0.14, 0.18, 0.25, 0.39]
      }
    ];

    return from(data).pipe(toArray(), delay(200));
  }

  public getGcrData(): Observable<ChartDataField[]> {
    let data: ChartDataField[] = [
      {
        data: [8260, 39870, 53370, 73600, 86680, 83150, 126800]
      }, {
        data: [0.9531, 0.9664, 0.9757, 0.9669, 0.9691, 0.9704, 0.9556]
      }
    ];

    return from(data).pipe(toArray());
  }
}
