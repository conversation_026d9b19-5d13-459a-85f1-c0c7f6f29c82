import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { BaseService } from '../base';
import { Label, LabelGroupInfo } from '../typings/label';

@Injectable()
export class EntityLabelsService {

  public urlLabels = 'labels';
  public urlEntityLabels = 'entity-labels';
  public urlLabelGroups = 'label-groups';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
  }

  getLabelGroups( type?: string ) {
    let url = `${BaseService.apiEndpoint}/${this.urlLabelGroups}`;
    let params = new HttpParams();
    if (type) {
      params = params.set('type', type);
    }

    return this.http
      .get<LabelGroupInfo[]>(url, { params })
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  getEntityLabels( path ): Observable<Label[]> {
    let url = `${this.getUrl(path)}/${this.urlEntityLabels}`;

    return this.http
      .get<Label[]>(url)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  updateEntityLabels( path, body ) {
    let url = `${this.getUrl(path)}/${this.urlEntityLabels}`;

    return this.http
      .put(url, body)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  addLabel( body ) {
    let url = `${BaseService.apiEndpoint}/${this.urlLabels}`;

    return this.http
      .post(url, body)
      .pipe(
        catchError(err => this.handleErrors(err)),
      );
  }

  getUrl( path ): string {
    if (path && path !== ':') {
      return path ? `${BaseService.apiEndpoint}/entities/${path}/` : `${BaseService.apiEndpoint}`;
    } else {
      return `${BaseService.apiEndpoint}`;
    }
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
