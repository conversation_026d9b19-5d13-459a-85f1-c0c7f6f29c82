import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SelectOptionItem, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, of, ReplaySubject, Subject, throwError } from 'rxjs';
import { catchError, first, map, share, tap } from 'rxjs/operators';
import { API_ENDPOINT, PERMISSIONS_NAMES } from '../../app.constants';

import { BaseService } from '../base';
import { CreateMerchantEntityData, Entity as EntityModel, UpdateMerchantEntityData } from '../models/entity.model';
import { SelectOptionModel } from '../models/select-option.model';
import { Balance, Entity as EntityTyping, EntityShortInterface, User } from '../typings';
import { EntityWhitelistDetailed } from '../models/whitelist-ip.model';
import { transformBalances } from '../core/currecy-transform';

export type EntityInfoTypes = 'notifications' | 'whitelist';
export type IpWhitelistType = 'bo' | 'user';

@Injectable()
export class EntityService<Entity> extends BaseService<Entity> {

  public urlList: string = '/entities';
  public urlGet: string = '/entities';
  public urlGetStructure: string = '/structure';
  public urlGetShortStructure: string = '/short-structure';
  public urlGetShortStructureSearch: string = 'short-structure/search';
  public urlGetBalances: string = '/balances';
  public urlGetUsers: string = '/users';
  public urlSave: string = '/entities';
  public urlCreate: string = '/entities';
  public urlRemove: string = '/entities';
  public urlDebits: string = '/debits';
  public urlCredits: string = '/credits';
  public urlGetBrief: string = '/brief';

  public urlBrandCreate: string = '/brandentities';

  public urlMerchantGet: string = '/merchantentities';
  public urlMerchantSave: string = '/merchantentities';
  public urlMerchantCreate: string = '/merchantentities';
  public urlMerchantRemove: string = '/merchantentities';

  public urlInfoGet: string = 'info/';
  public urlInfoSave: string = 'info/';
  public urlInfoCreate: string = 'info/';
  public urlInfoRemove: string = 'info/';

  public urlSuspendedPlayersGet: string = '/suspendedplayers';
  public urlSuspendedPlayersSave: string = '/suspendedplayers';
  public urlSuspendedPlayersCreate: string = '/suspendedplayers';
  public urlSuspendedPlayersRemove: string = '/suspendedplayers';

  public _item: Subject<Entity>;
  public _items: Subject<Entity[]>;
  public _structure: ReplaySubject<Entity> = new ReplaySubject(1);
  public _shortStructure: ReplaySubject<EntityShortInterface> = new ReplaySubject(1);

  private urlWhitelist: string = '/settings/ip-whitelist';

  private cachedStructure: Entity;
  private cachedShortStructure: EntityShortInterface;
  private cachedEnabledShortStructure: EntityShortInterface;
  private cachedBrief$: ReplaySubject<Entity>;
  private _balances: Subject<Balance[]> = new Subject();
  private _users: Subject<User[]> = new Subject();
  private _baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( notifications: SwuiNotificationsService,
               http: HttpClient,
               private auth: SwHubAuthService) {
    super(notifications, http);
  }

  get structure() {
    return this._structure.asObservable();
  }

  get shortStructure() {
    return this._shortStructure.asObservable();
  }

  get balances(): Observable<Balance[]> {
    return this._balances.asObservable();
  }

  get users(): Observable<User[]> {
    return this._users.asObservable();
  }

  public processRecord( record ): Entity {
    record._meta = {};

    if (record.balances) {
      record.balances = transformBalances(record.balances);
    }

    return record as Entity;
  }

  public getStructure( parent: string = '', force: boolean = false ): Observable<Entity> {
    if (!force && this.cachedStructure) {
      this._structure.next(this.cachedStructure);
      return of(this.cachedStructure);
    }

    let url = API_ENDPOINT;
    if (parent) {
      url += '/entities/' + parent;
    }
    url += this.urlGetStructure;

    const source = this.http
      .get<Entity>(url, {
        params: {
          includeLiveStudio: 'true'
        }
      }).pipe(
        share()
      );

    source.subscribe(
      data => {
        this.cachedStructure = data;
        this._structure.next(data);
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public searchEntityInShortStructure( filter: any ) {
    let url = `${API_ENDPOINT}/${this.urlGetShortStructureSearch}`;

    return this.http.get<EntityShortInterface>(url, { params: filter });
  }

  public getShortStructure( additionalFields: string = '', force: boolean = false, enabled?: boolean ): Observable<EntityShortInterface> {
    if (!this.auth.allowedTo([PERMISSIONS_NAMES.ENTITY_VIEW])) {
      return of([] as any);
    }

    let url = API_ENDPOINT;
    url += this.urlGetShortStructure;

    if (this.cachedShortStructure !== undefined && !force) {
      return enabled ? of(this.cachedEnabledShortStructure) : of(this.cachedShortStructure);
    }

    const source = this.http
      .get<EntityShortInterface>(url, {
        params: {
          includeLiveStudio: 'true',
          additionalFields: additionalFields
        }
      }).pipe(
        catchError(this.handleErrors.bind(this)),
        catchError(() => of([])),
        share()
      );
    source
      .subscribe(str => {
        this.cachedShortStructure = str;
        this.cachedEnabledShortStructure = this.hideDisabledItems({...str});
      });
    return source;
  }

  public getCsvStructure(): Observable<EntityShortInterface> {
    if (!this.auth.allowedTo([PERMISSIONS_NAMES.ENTITY_VIEW])) {
      return of([] as any);
    }

    let url = API_ENDPOINT;
    url += this.urlGetShortStructure;

    return this.http
      .get<EntityShortInterface>(url, {
        params: {
          includeLiveStudio: 'true',
          includeJurisdiction: 'true'
        }
      }).pipe(
        catchError(this.handleErrors.bind(this)),
        catchError(() => of([])),
      );
  }

  public moveEntity( entityKey, newParentKey ) {
    let url = `${API_ENDPOINT}/${this.urlGetStructure}/move-entity`;

    return this.http
      .post(url, { entityKey, newParentKey }).pipe(
        tap(() => this.notifications.success('Successfully moved!'))
      );
  }

  public getBalances( path: string = '', provideAsIs: boolean = false ): Observable<Balance> {
    if (path === ':') {
      path = '';
    }

    const url = this.getUrl(path, this.urlGetBalances);
    const source = this.http.get<any>(url)
      .pipe(
        map(balancesData => transformBalances(balancesData)),
        share()
      );

    source.pipe(
      map(balancesData => {
        if (provideAsIs) {
          return balancesData;
        }

        const balances = [];
        const currencies = Object.keys(balancesData);
        for (let currency of currencies) {
          const value = balancesData[currency].main;
          balances.push({ currency, value });
        }
        return balances;
      })
    ).subscribe(
      (data: Balance[]) => this._balances.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public getUsers( parent: string ) {
    let url = API_ENDPOINT;
    if (parent && parent !== ':') url += '/entities/' + parent;
    url += this.urlGetUsers;

    this.http.get<User[]>(url)
      .subscribe(
        data => this._users.next(data),
        err => this.handleErrors.call(this, err)
      );
  }

  public setStatus( entity, status: string ): Observable<Entity> {
    const method = status === 'normal' ? 'DELETE' : 'PUT';
    const url = `${this._baseApiEndpoint}/entities/${entity.path}/suspended`;

    return this.http.request(method, url).pipe(
      map(data => this.processRecord(data))
    );
  }

  public setMaintenance( entity, status: string ): Observable<Entity> {
    const method = status === 'normal' ? 'DELETE' : 'PUT';
    const url = `${this._baseApiEndpoint}/entities/${entity.path}/maintenance`;

    return this.http.request(method, url).pipe(
      map(data => this.processRecord(data))
    );
  }

  public setTest( entity, status: string ): Observable<Entity> {
    const method = status === 'normal' ? 'DELETE' : 'PUT';
    const url = `${this._baseApiEndpoint}/entities/${entity.path}/test`;

    return this.http.request(method, url).pipe(
      map(data => this.processRecord(data))
    );
  }

  public patchEntityStatus( entity, status: string ): Observable<Entity> {
    let body = { status: status };
    let url = `${this._baseApiEndpoint}/entities/${entity.path}/status`;

    return this.http
      .patch<Entity>(url, body).pipe(
        map(data => this.processRecord(data))
      );
  }

  public patchEntityStatusFull( entity, status: string ): Observable<Entity> {
    let body = { status: status };
    let url = `${this._baseApiEndpoint}/entities/${entity.path}`;

    return this.http
      .patch<Entity>(url, body).pipe(
        map(data => this.processRecord(data))
      );
  }

  public updateEntityItem( entity: EntityTyping ): Observable<Entity> {
    console.log('Entities service: update element');
    let body;

    // @TODO: to remove after refactoring (temporary solution)
    if (entity instanceof EntityModel) {
      body = entity.toJSON();
    } else {
      body = JSON.stringify(entity);
    }

    let url = `${this._baseApiEndpoint}/entities/${entity.path}`;

    return this.http
      .patch<Entity>(url, body)
      .pipe(
        catchError(this.handleErrors.bind(this))
      );
  }

  public createEntityItem( entity: EntityTyping, parent: string = ':' ): Observable<Entity> {
    console.log('Entities service: create element');
    let body;

    // @TODO: to remove after refactoring (temporary solution)
    if (entity instanceof EntityModel) {
      body = entity.toJSON();
    } else {
      body = JSON.stringify(entity);
    }

    let url = `${API_ENDPOINT}${this.urlCreate}/`;
    if (parent !== ':') url += `${parent}/`;

    return this.http
      .post<Entity>(url, body).pipe(
        share()
      );
  }

  public getMerchantEntityItem( path: string ): Observable<Entity> {
    const fn = this.processRecord.bind(this);

    console.log('Entity service: get element ', path);
    let url = `${BaseService.apiEndpoint}${this.urlMerchantGet}`;
    if (path && path !== ':') {
      url += `/${path}`;
    }

    const source = this.http
      .get<Entity>(url).pipe(
        map(response => fn(response)),
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public updateMerchantEntityItem( merchant: UpdateMerchantEntityData, path: string = ':' ): Observable<EntityModel> {
    console.log('Entities service: update merchant');
    const fn = this.processRecord.bind(this);
    let url = `${API_ENDPOINT}${this.urlMerchantSave}/`;
    if (path !== ':') url += `${path}/`;
    return this.http.patch(url, merchant).pipe(
      map(response => fn(response)),
    );
  }

  public createMerchantEntityItem( merchant: CreateMerchantEntityData, parent: string = ':' ): Observable<EntityModel> {
    console.log('Entities service: create merchant');
    const fn = this.processRecord.bind(this);
    let url = `${API_ENDPOINT}${this.urlMerchantCreate}/`;
    if (parent !== ':') url += `${parent}/`;
    return this.http.post(url, merchant).pipe(
      map(response => fn(response))
    );
  }

  public getItem( path: string ): Observable<Entity> {
    const fn = this.processRecord.bind(this);

    console.log('Entity service: get element ', path);

    const source = this.http
      .get<Entity>(this.getUrl(path, '')).pipe(
        map(response => fn(response)),
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public getBrief(): Observable<any> {
    const needRequest = !this.cachedBrief$;

    if (!this.cachedBrief$) {
      this.cachedBrief$ = new ReplaySubject<Entity>(1);
    }
    if (!needRequest) {
      return this.cachedBrief$.pipe(first());
    }

    return this.http
      .get(`${API_ENDPOINT}${this.urlGetBrief}`).pipe(
        tap(this.cachedBrief$),
        catchError(err => {
          this.cachedBrief$ = null;
          return throwError(err);
        })
      );
  }

  public getItemWithMerchantData( path: string = ':' ): Observable<EntityModel> {
    let url = `${API_ENDPOINT}${this.urlMerchantGet}/`;
    if (path !== ':') url += `${path}/`;
    const source = this.http.get<EntityModel>(url).pipe(
      share()
    );
    source.subscribe(() => {
    }, err => {
      this.handleErrors.call(this, err);
    });
    return source;
  }

  /**
   * Adds amount to balance
   */
  public creditEntity( path: string, currencyCode: string, amount: number ) {
    const fn = this.processRecord.bind(this);
    let url = `${this._baseApiEndpoint}/entities/${path}${this.urlCredits}/`
      + `${currencyCode}/${amount}`;

    return this.http
      .post<Entity>(url, {}).pipe(
        map(response => fn(response)),
        catchError(this.handleErrors.bind(this))
      );
  }

  /**
   * Subtracts amount from balance
   */
  public debitEntity( path: string, currencyCode: string, amount: number ) {
    const fn = this.processRecord.bind(this);
    let url = `${this._baseApiEndpoint}/entities/${path}${this.urlDebits}/`
      + `${currencyCode}/${amount}`;

    return this.http
      .post<Entity>(url, {}).pipe(
        map(response => fn(response)),
        catchError(this.handleErrors.bind(this))
      );
  }

  getWhitelistedIPs( path: string, type: IpWhitelistType ): Observable<EntityWhitelistDetailed> {
    const url = `${this._baseApiEndpoint}/entities/${path}/${this.urlWhitelist}/${type}`;
    return this.http.get<EntityWhitelistDetailed | string[]>(url).pipe(
      map(response => Array.isArray(response) ? { own: response } : response)
    );
  }

  createWhitelistedIPs( ipList: string[], path: string, type: IpWhitelistType, issueCode?: string ): Observable<string[]> {
    const url = `${this._baseApiEndpoint}/entities/${path}/${this.urlWhitelist}/${type}`;
    const params = issueCode ? { issueCode } : {};

    return this.http.post<string[]>(url, JSON.stringify(ipList), { params });
  }

  updateWhitelistedIPs( ipList: string[], path: string, type: IpWhitelistType, issueCode = '' ): Observable<string[]> {
    const url = `${this._baseApiEndpoint}/entities/${path}/${this.urlWhitelist}/${type}`;
    const params = issueCode ? { issueCode } : {};

    return this.http.patch<string[]>(url, JSON.stringify(ipList), { params });
  }

  removeWhitelistedIPs( ipList: string[], path: string, type: IpWhitelistType, issueCode = '' ): Observable<string[]> {
    const url = `${this._baseApiEndpoint}/entities/${path}/${this.urlWhitelist}/${type}`;
    return this.http.delete<string[]>(url, { body: JSON.stringify(ipList), params: { issueCode } });
  }

  public getSuspendedPlayers( path: string = ':' ) {
    const url = this.getUrl(path, this.urlSuspendedPlayersGet);

    return this.http.get<any[]>(url, {
      params: BaseService.getRequestParams({}, { limit: 10000, offset: 0 })
    });
  }

  public suspendPlayer( playerCode: string, path: string = ':' ) {
    return this._suspendPlayer(playerCode, path);
  }

  public restorePlayer( playerCode: string, path: string = ':' ) {
    return this._suspendPlayer(playerCode, path, true);
  }

  public getInfo( type: EntityInfoTypes, path: string ) {

    let fn = this.processRecord.bind(this);
    const infoUrl = `/${this.urlInfoSave}${type}`;
    const source = this.http
      .get<any>(this.getUrl(path, infoUrl), {
        observe: 'response'
      }).pipe(
        map(response => {
          this.responseHeaders = this.getResponseParams(response);
          const data = fn(response.body);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._item.next(data), // info to entity???
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public setInfo( type: EntityInfoTypes, path: string, data?: any ) {
    const infoUrl = `/${this.urlInfoSave}${type}`;

    const source = this.http.put(`${this.getUrl(path, infoUrl)}`, data).pipe(
      share()
    );

    source.subscribe(
      () => null,
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  private _suspendPlayer( playerCode: string, path: string = ':', restore: boolean = false ) {
    let url = this._baseApiEndpoint;
    if (path && path !== ':') {
      url += '/entities/' + path;
    }
    url += `${restore ? this.urlSuspendedPlayersRemove : this.urlSuspendedPlayersCreate}/${playerCode}/suspended`;

    let source = restore ? this.http.delete(url) : this.http.put(url, {});
    source = source.pipe(share());

    source.subscribe(
      () => null,
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  private hideDisabledItems(str: EntityShortInterface): EntityShortInterface {
    if (str.child) {
      str.child = str.child
        .filter(({status}) => !['suspended', 'blocked_by_admin'].includes(status)).map(item => this.hideDisabledItems({...item}));
    }

    return str;
  }
}

export const pushDefaultToStart = ( value: string[], defaultValue: string ) => {
  const valueArray = [...value];
  let index = valueArray.indexOf(defaultValue);
  if (index < 1) return valueArray; // is absent or already on a first place
  valueArray.splice(index, 1);
  return [defaultValue, ...valueArray];
};


export const codeArrayToObjectReducer = ( obj, item ) => ({ ...obj, [item.code]: item });

export const entitiesStructureToSelectOptions =
  ( item: any, count: number = 0, resultArray: SelectOptionModel[] = [], disableEntityEntries: boolean = true ): SelectOptionItem[] => {
    const { type, key } = item;
    const disabled = disableEntityEntries && item.type === EntityModel.TYPE_ENTITY;
    const path = item.path === ':' ? '' : item.path;

    resultArray.push(
      new SelectOptionModel(
        path,
        `${'-'.repeat(count)} ${item.name} - ${item.title || 'All'}`,
        disabled,
        { type, key }
      )
    );

    if (item.child) item.child.reverse()
      .forEach(i => entitiesStructureToSelectOptions(i, count + 1, resultArray, disableEntityEntries));

    return resultArray;
  };

export const flattenStructure = ( structure, result = [] ): EntityModel[] => {
  const entity = new EntityModel(structure);

  if ('child' in structure && structure.child.length) {
    structure.child.forEach(item => flattenStructure(item, result));
  }

  result.push(entity);
  return result;
};

export const buildStructureHash = ( structure ): { [path: string]: EntityModel } => {
  const hash = {};
  const flattened = flattenStructure(structure);
  for (let i = 0; i < flattened.length; i++) {
    let item = flattened[i];
    hash[item['path']] = new EntityModel(item);
  }
  return hash;
};
