import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  GridDataService,
  GridRequestData,
  SettingsService,
  SwHubAuthService,
  SwHubEntityService,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, of } from 'rxjs';
import { catchError, map, mergeMap, share, tap } from 'rxjs/operators';

import { API_ENDPOINT, FORMAT_DATETIME, ZERO_TIME } from '../../app.constants';
import { BaseService } from '../base';
import { BaseApiObject, User, User as UserTyping, UserBlocking, UserProfile } from '../typings';
import { Permission, PermissionGroup } from '../typings/permission';
import { CsvSchema, CsvService, transformDate } from './csv.service';
import { EntityType } from '../models/entity.model';


const DEFAULT_USER_SETTINGS = {
  timezoneName: moment.tz.guess(),
  availableCurrencies: ['EUR', 'USD', 'GBP', 'MYR', 'CNY', 'KRW', 'IDR', 'JPY', 'VND', 'THB']
};
const USER_SETTINGS_STORAGE_NAME = 'userSettings';

const DATE_PARAMS: string[] = ['lastLogin', 'createdAt', 'updatedAt'];

const buildCsvSchema = (
  type: EntityType, path: string, selectedEntity: string, timezoneName: string, format: string, role: CsvSchema
): CsvSchema[] => [
  ...(type === 'entity' ? [{
    name: 'entity',
    title: 'USERS.CSV.entityPath',
    transform: ( data: string, row ) => {
      if (!selectedEntity) {
        return row.entityPath;
      }

      if (path && path !== ':') {
        return`${path}${data}`;
      }

      return data;
    }
  }] : []),

  {
    name: 'entity',
    title: 'USERS.CSV.entityCode',
    transform: (data: string) => {
      if (!selectedEntity) {
        return data;
      }

      let result: string = data;

      if (!result && path && path !== ':') {
        result = path;
      }

      if (result) {
        const entities = result.split(':');
        return entities[entities.length - 2];
      }

      return selectedEntity;
    }
  },
  {
    name: 'firstName',
    title: 'USERS.CSV.name',
    transform: ( data, rows ) => {
      return `${data} ${rows.firstName ?? ''}`;
    }
  },
  {
    name: 'username',
    title: 'USERS.CSV.username',
  },
  role,
  {
    name: 'email',
    title: 'USERS.CSV.email',
  },
  {
    name: 'createdAt',
    title: 'USERS.CSV.createdAt',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'updatedAt',
    title: 'USERS.CSV.updatedAt',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'lastLogin',
    title: 'USERS.CSV.lastLogin',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'userType',
    title: 'USERS.CSV.userType',
  },
  {
    name: 'status',
    title: 'USERS.CSV.status',
  }
];

export function optionalFieldsReplacer( key, value ) {
  let result = value;
  if (key === 'phone') {
    if (value === '') {
      result = null;
    }
  }
  return result;
}

@Injectable()
export class UserService<T extends UserTyping & BaseApiObject> extends BaseService<T> implements GridDataService<User> {
  public urlList: string = '/users';
  public urlGet: string = '/users';
  public urlSave: string = '/users';
  public urlCreate: string = '/users/';
  public urlRemove: string = '/users/';

  public urlResetTwoFactorSettings: string = '/login/secondstep/reset';
  public urlForceResetPassword: string = '/password/force-reset';
  public urlForceResetEmail: string = '/email/force-set';
  public userSettings = Object.assign({},
    DEFAULT_USER_SETTINGS,
    JSON.parse(localStorage.getItem(USER_SETTINGS_STORAGE_NAME))
  );

  private baseApiEndpoint: string = BaseService.apiEndpoint;
  private currentUserProfileCache: UserProfile;

  constructor( notifications: SwuiNotificationsService,
               http: HttpClient,
               private auth: SwHubAuthService,
               private csvService: CsvService,
               private setting: SettingsService,
               private entityService: SwHubEntityService
  ) {
    super(notifications, http);
  }


  public getItemURL( id, data, url ) {
    if (id) {
      return `${BaseService.apiEndpoint}${url}${data.username}`;
    } else {
      return `${BaseService.apiEndpoint}${url}`;
    }
  }

  public getList( filter?, params?: any ): Observable<T[]> {
    console.log('Users service: get list');

    let fn = this.processRecord.bind(this);

    this.requestParams = BaseService.getRequestParams(filter);

    let path;

    if (filter && filter.values) {
      path = filter.values.path;
    }

    if (params && 'path' in params && params['path'] !== ':') {
      path = params.path;

      // overriding fn
      fn = ( record: T ): T => {
        let processedRecord = this.processRecord(record);

        if ('entity' in record && record['entity'].indexOf(path) === -1) {
          record['entity'] = `${path}${record['entity']}`;
        }
        return processedRecord;
      };
    }

    let url = this.getUrl(path);

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          let data = response.body;
          this.responseHeaders = this.getResponseParams(response, { pages: {} }, data);
          data = data.map(fn);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public getGridData( params: HttpParams, data?: GridRequestData ): Observable<HttpResponse<User[]>> {
    let path;

    if (typeof data !== 'undefined' && data.hasOwnProperty('path')) {
      path = data['path'];
    } else if (params.has('path')) {
      path = params.get('path');
    }

    const fn = record => {
      if (path && path !== ':') {
        record.entityPath = `${path}${record.entity}`;
      } else {
        record.entityPath = record.entity;
      }

      if (!record.entity && path && path !== ':') {
        record.entity = path;
      }

      if (record.entity) {
        const entities = record.entity.split(':');
        record.entity = entities[entities.length - 2];
      } else {
        record.entity = this.entityService.entities[0].name;
      }

      return this.processRecord(record);
    };

    return this.http.get<User[]>(this.getUrl(path), {
      params,
      observe: 'response'
    }).pipe(
      tap(response => {
        response.body.forEach(item => fn(item));
      }),
    );
  }

  public downloadCsv( type: EntityType, path: string ): Observable<any> {
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const schema = buildCsvSchema(type, path, this.entityService.entities[0].name, timezoneName, datetimeFormat, {
      name: 'roles',
      title: 'USERS.CSV.roles',
      transform: data => {
        if (!data) {
          return '';
        }
        const roles: any[] = data instanceof Array ? data : JSON.parse(data.replace(/\B'|'\B/g, '"'));
        return `"${roles.map(( { title } ) => title).join(', ').replace(/"/g, '\'')}"`;
      },
    });
    const fileName = `Export Users list ${moment().format('YYYY-MM-DD HH:MM')}`;
    return this.csvService.download(this.getUrl.bind(this), schema, fileName);
  }

  public exportPage( type: EntityType, path: string, data: Record<string, any>[], columns: string[], page: number ) {
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const schema = buildCsvSchema(type, path, undefined, timezoneName, datetimeFormat, {
      name: 'roleTitle',
      title: 'USERS.CSV.roles'
    });
    const fileName = `Export Users list ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    this.csvService.exportToCsv(schema, data, fileName, columns);
  }

  public processRecord( record: T ): T {
    DATE_PARAMS.forEach(property => {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    });
    record._meta = {
      createdAt: record.createdAt && moment(record.createdAt),
      updatedAt: record.updatedAt && moment(record.updatedAt),
      lastLogin: record.lastLogin && moment(record.lastLogin),
    };

    record.firstName = record.firstName || '';
    record.lastName = record.lastName || '';
    record.roles = record.roles || [];

    if (record.roles && typeof record.roles !== 'string') {
      record.roleTitle = record.roles.map(element => element.title).join(', ');
      record.hoverTitle = record.roles.length ? record.roles.map(element => element.title).join(', ') : '';
    } else if (record.roles && typeof record.roles === 'string') {
      record.roleTitle = record.roles;
      record.roleTitle = this.processRolesCSV(record.roles);
    } else if (!record.roles) {
      record.roleTitle = '';
    }

    record.fullName = `${record.firstName} ${record.lastName}`;
    return record;
  }

  public getUserProfile( user: T ): Observable<UserBlocking> {
    console.log('User service: get profile: ', user.username);
    const { username, entityPath } = user;

    let url = this.getBaseUrl(entityPath);
    url += `${this.urlList}/${username}/profile`;

    const source = this.http.get<UserBlocking>(url).pipe(
      share()
    );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );

    return source;
  }

  public getCurrentUserProfile( forceLoad: boolean = false ): Observable<T> {
    let currentUserProfile$;
    if (this.currentUserProfileCache && !forceLoad) {
      currentUserProfile$ = of(this.currentUserProfileCache);
    } else {
      currentUserProfile$ = this.http.get<User>(`${API_ENDPOINT}/users/${this.auth.username}/profile`)
        .pipe(
          tap(( profile ) => this.currentUserProfileCache = profile)
        );
    }

    return currentUserProfile$.pipe(share());
  }

  public createItem( user: T, path = '' ): Observable<T> {
    console.log('Users service: create element');
    const body = JSON.stringify(user, optionalFieldsReplacer);

    if (path === ':') {
      path = '';
    }

    const url = this.getUrl(path, this.urlCreate);

    const source = this.http
      .post<T>(url, body).pipe(
        share()
      );

    source.subscribe(
      () => {
      },
      this.handleErrors.bind(this)
    );

    return source;
  }

  public updateItem( user: T, oldUsername: string = '' ): Observable<T> {
    console.log('Users service: update element');
    const body = JSON.stringify(user, optionalFieldsReplacer);
    let { username, entityPath } = user;

    if (oldUsername !== '') {
      username = oldUsername;
    }

    let url = this.getBaseUrl(entityPath);
    url += `/users/${username}`;

    const source = this.http
      .patch<T>(url, body).pipe(
        share()
      );

    source.subscribe(
      () => {
      },
      this.handleErrors.bind(this)
    );

    return source;
  }

  public resetTwoFactorSettingsForUser( user: T, types: string[] ) {
    const { username, entityPath } = user;

    let url = this.getBaseUrl(entityPath);
    url += `${this.urlResetTwoFactorSettings}/${username}`;

    const createSource = ( authType ) => {
      const body = { authType };
      const source = this.http.post(url, body).pipe(
        share()
      );
      source.subscribe(
        () => {
        },
        this.handleErrors.bind(this)
      );
      return source;
    };

    return types.reduce(( merged$, type ) => {
      return merged$.pipe(mergeMap(() => createSource(type)));
    }, createSource(types.shift()));
  }

  public deleteUserAccount( user: T ) {
    const { username, entityPath } = user;

    let url = this.getBaseUrl(entityPath);
    url += `${this.urlRemove}${username}`;

    const body = {};

    return this.http.delete(url, body).pipe(
      catchError(err => this.handleErrors.call(this, err)),
      share(),
    );
  }

  public unblockUserAccount( user: T, type: 'change-password-lock' | 'login-lock' ) {
    const { username, entityPath } = user;

    let url = this.getBaseUrl(entityPath);
    url += `${this.urlList}/${username}/${type}`;

    const body = {};
    const source = this.http.delete(url, body).pipe(
      share()
    );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );

    return source;
  }

  public forceResetUserPassword( user: T ) {
    const { username, entityPath, password } = user;

    let url = this.getBaseUrl(entityPath);
    url += `/users/${username}${this.urlForceResetPassword}`;

    const body = {
      newPassword: password
    };
    const source = this.http.post(url, body).pipe(
      share()
    );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );

    return source;
  }

  public forceSetUserEmail( user: T ) {
    const { username, entityPath, email } = user;

    let url = this.getBaseUrl(entityPath);
    url += `/users/${username}${this.urlForceResetEmail}`;

    const body = { email };
    const source = this.http.post(url, body).pipe(
      share()
    );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );

    return source;
  }

  public setUserType( user: T ) {
    const { username, entityPath, userType } = user;

    let url = this.getBaseUrl(entityPath);
    url += `/users/${username}/type`;

    const body = { userType };
    const source = this.http.patch(url, body).pipe(
      share()
    );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );

    return source;
  }

  public getPermissions(): Observable<PermissionGroup[]> {
    return this.http
      .get(`${this.baseApiEndpoint}/permissions`).pipe(
        map(this.processPermissions),
        share()
      );
  }

  private processPermissions( permissions: Permission[] ): PermissionGroup[] {
    let groupsObj = permissions.reduce(( obj, permission ) => {
      let group = permission.group;

      if (!obj[group]) {
        obj[group] = [];
      }

      obj[group].push(permission);

      return obj;
    }, {});

    return Object.keys(groupsObj).map(key => {
      return {
        groupName: key,
        permissions: groupsObj[key],
      };
    }) as PermissionGroup[];
  }


  private getBaseUrl( entity ): string {
    let url = this.baseApiEndpoint;
    if (entity && entity !== ':') {
      url += `/entities/${entity}`;
    }
    return url;
  }

  private processRolesCSV( roles: string ): string {
    const rolesProcessed = this.stringToObject(roles);
    return rolesProcessed.map(el => el.title).join(', ');
  }
}
