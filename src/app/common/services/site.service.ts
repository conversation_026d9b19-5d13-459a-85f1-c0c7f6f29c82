import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, Subject } from 'rxjs';
import { catchError, map, share, take } from 'rxjs/operators';
import { BaseService } from '../base/base-service';
import { Site } from '../models/site.model';
import { BaseApiObject } from '../typings/base';

export const SITE_AVAILABLE_URL = '/sites/available';
export const SITE_AVAILABLE_BULK_OPERATION = '/sites/bulk-operation';

export type CheckWebsiteWhitelistedLevelType = 'none' | 'warning' | 'error';

export const whitelistLevels: {
  NONE: CheckWebsiteWhitelistedLevelType,
  WARNING: CheckWebsiteWhitelistedLevelType,
  ERROR: CheckWebsiteWhitelistedLevelType,
} = {
  NONE: 'none',
  WARNING: 'warning',
  ERROR: 'error'
};

export interface CheckWebsiteWhitelistedResult {
  level: CheckWebsiteWhitelistedLevelType;
}

@Injectable()
export class SiteService<T extends Site & BaseApiObject> extends BaseService<T> {
  public urlAvailableGet: string = `${SITE_AVAILABLE_URL}/`;
  public urlAvailableSave: string = SITE_AVAILABLE_URL;
  public urlAvailableCreate: string = SITE_AVAILABLE_URL;
  public urlAvailableRemove: string = SITE_AVAILABLE_URL;
  public urlAvailableBulkOperation: string = SITE_AVAILABLE_BULK_OPERATION;

  public checkWebsiteWhitelistedURL: string = '/check-website-whitelisted';

  public _items: Subject<T[]>;

  constructor( notifications: SwuiNotificationsService, http: HttpClient ) {
    super(notifications, http);

  }

  public changeSiteStatus( path: string, body: any ) {
    let url = this.getUrl(path, this.urlAvailableBulkOperation);

    let source = this.http
      .post<T>(url, body).pipe(
        share()
      );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );
    return source;
  }

  public getAvailableSites( path: string ): Observable<T[]> {
    let url = `${BaseService.apiEndpoint}/entities/${path}${this.urlAvailableGet}`;

    let source = this.http
      .get<T[]>(url, { observe: 'response' }).pipe(
        map(response => {
          const data = response.body;
          this.responseHeaders = this.getResponseParams(response);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public addAvailableSite( site: Site, path: string ): Observable<T> {
    let url = `${BaseService.apiEndpoint}/entities/${path}${this.urlAvailableCreate}`;
    let body = JSON.stringify(site);

    let source = this.http
      .post<T>(url, body).pipe(
        share()
      );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err))
    );
    return source;
  }

  public modifyAvailableSite( site: Site, path: string ): Observable<T> {
    let url = `${BaseService.apiEndpoint}/entities/${path}${this.urlAvailableSave}/${site.id}`;
    let body = JSON.stringify(site);

    let source = this.http
      .patch<T>(url, body).pipe(
        share()
      );

    source
      .pipe(
        take(1),
        catchError(err => this.handleErrors.call(this, err))
      ).subscribe();
    return source;
  }

  public removeAvailableSite( id: string, path: string ) {
    let url = `${BaseService.apiEndpoint}/entities/${path}${this.urlAvailableRemove}/${id}`;

    let source = this.http
      .delete(url).pipe(
        share()
      );

    source
      .pipe(
        take(1),
        catchError(err => this.handleErrors.call(this, err))
      ).subscribe();
    return source;
  }

  public getCheckWebSiteWhitelisted( path: string ): Observable<CheckWebsiteWhitelistedResult> {
    const url = this.getUrl(path, this.checkWebsiteWhitelistedURL);
    return this.http.get<CheckWebsiteWhitelistedResult>(url);
  }

  public setCheckWebSiteWhitelisted( path: string, level: string ) {
    const url = this.getUrl(path, this.checkWebsiteWhitelistedURL);
    return this.http.patch<CheckWebsiteWhitelistedResult>(url, { level });
  }

  public resetCheckWebSiteWhitelisted( path: string ) {
    const url = this.getUrl(path, this.checkWebsiteWhitelistedURL);
    return this.http.delete<CheckWebsiteWhitelistedResult>(url);
  }
}
