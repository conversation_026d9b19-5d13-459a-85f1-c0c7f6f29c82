import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { GAME_CATEGORY_TYPES, GameCategory } from '../../pages/games-categories-management/game-category.model';
import { BaseService } from '../base';
import { handleErrors } from '../lib/handle-http-error';
import { GameInfo } from '../typings';

export const API = environment.ENV_API_SERVER_ENDPOINT;

function setGamestoreType( filter? ) {
  if (!filter) {
    filter = {};
  }
  if (!('values' in filter)) {
    filter['values'] = {};
  }
  filter.values['type'] = GAME_CATEGORY_TYPES.gamestore;
  return filter;
}

function getUrl( path?, urlList: string = '/gamecategories/' ): string {
  return `${BaseService.apiEndpoint}${path && path !== ':' ? '/entities/' + path : ''}${urlList}`;
}

@Injectable()
export class GameCategoryService {
  item$: BehaviorSubject<GameCategory> = new BehaviorSubject<GameCategory>(null);
  items$: BehaviorSubject<GameCategory[]> = new BehaviorSubject<GameCategory[]>([]);

  constructor( private readonly http: HttpClient,
               private readonly notificationsService: SwuiNotificationsService,
               private readonly translate: TranslateService
  ) {
  }

  setNextItem( value: GameCategory ) {
    this.item$.next(value);
  }

  getList( type?: string, path?: any ): Observable<GameCategory[]> {
    let param: any = {};

    if (type) {
      param.type = type;
    }

    return this.http.get<GameCategory[]>(getUrl(path), {
      params: param,
    }).pipe(
      tap((data) => {
        data.reduce((acc, cur) => {
          cur.path = path;
          acc.push(cur);
          return acc;
        }, []);
        this.items$.next(data);
      }),
      handleErrors(this.notificationsService, this.translate)
    );
  }

  getItem( id: string, path?: string ): Observable<GameCategory> {
    console.log('GameCategory service: get element ', id);
    const fn = this.processRecord.bind(this);
    const url = `${getUrl(path)}${id}`;

    return this.http.get<GameCategory>(url, {
      observe: 'response'
    }).pipe(
      map(response => fn(response.body)),
      tap(data => {
        data.path = path;
        this.item$.next(data);
      }),
    );
  }

  createItem( gameCategory: GameCategory ): Observable<GameCategory> {
    console.log('GameCategory service: create element');
    const { path } = gameCategory;
    const url = getUrl(path);
    return this.http.post<GameCategory>(url, gameCategory).pipe(
      map(( data: GameCategory ) => {
        data.path = path;
        return data;
      }),
      handleErrors(this.notificationsService, this.translate),
    );
  }

  updateItem( gameCategory: GameCategory ): Observable<GameCategory> {
    console.log('GameCategory service: update element');
    const { path } = gameCategory;
    const url = `${getUrl(path)}${gameCategory.id}`;
    return this.http.patch<GameCategory>(url, gameCategory).pipe(
      handleErrors(this.notificationsService, this.translate),
    );
  }

  deleteItem( gameCategory: GameCategory ) {
    console.log('GameCategory service: delete element', gameCategory.id);
    const { path } = gameCategory;
    const url = `${getUrl(path)}/${gameCategory.id}`;
    return this.http.delete(url).pipe(
      handleErrors(this.notificationsService, this.translate)
    );
  }

  getGamestoreListWithGames( filter: any = {}, force: boolean = false ): Observable<GameCategory[]> {
    filter = setGamestoreType(filter);
    const { type } = filter.values;

    filter = {
      includeGames: true,
      force: force,
      type: type
    };

    return this.http.get<GameCategory[]>(getUrl(), {
      params: filter
    }).pipe(
      tap(data => {
        this.items$.next(data);
      }),
      handleErrors(this.notificationsService, this.translate),
    );
  }

  setCategoryOrder( categoryId, newPosition, path?: string ): Observable<GameCategory> {
    const url = `${getUrl(path)}${categoryId}/move`;

    return this.http.put<GameCategory>(url, { newPosition }).pipe(
      handleErrors(this.notificationsService, this.translate),
    );
  }

  getCategoryGames( categoryId: string, path: string ): Observable<GameInfo[]> {
    const url = `${getUrl(path)}${categoryId}/games`;
    return this.http.get<GameInfo[]>(url, { params: { limit: `${Infinity}` } }).pipe(
      handleErrors(this.notificationsService, this.translate),
    );
  }

  processRecord( record: GameCategory, path?: string ): GameCategory {
    record.path = path;
    return new GameCategory(record) as GameCategory;
  }

  handleErrors( httpErrorResponse ) {
    console.error(httpErrorResponse);
    const errorBody = httpErrorResponse.error;
    if (errorBody) {
      this.notificationsService.error(httpErrorResponse?.error?.message);
    } else {
      this.notificationsService.error(httpErrorResponse.statusText, `Status: ${httpErrorResponse.status}`);
    }

    throwError(httpErrorResponse);
  }
}
