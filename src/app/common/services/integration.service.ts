import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { BaseService } from '../base';
import { Integrations, IntegrationsReportTests, NewIntegrations } from '../typings/integration';

function getUrl( urlList: string, path?: string ): string {
  return `${BaseService.apiEndpoint}${path && path !== ':' ? '/merchants/' + path : ''}${urlList}`;
}

@Injectable()
export class IntegrationService {
  constructor( private readonly notifications: SwuiNotificationsService, private readonly http: HttpClient ) {
  }

  test( body: string, path?: string ) {
    const url = getUrl('/test/run', path);
    return this.http.post<Integrations>(url, body)
      .pipe(catchError(err => this.handleErrors.call(this, err)));
  }

  getTestsReports( id: number, path?: string ): Observable<Integrations> {
    const url = getUrl(`/test/${id}`, path);
    return this.http.get<Integrations | NewIntegrations>(url)
      .pipe(
        map(data => {
          if (data.version === 2 && data.report) {
            return this.convertToOldTestsVersion(data as NewIntegrations);
          }

          return data as Integrations;
        }),
        catchError(this.handleErrors)
      );
  }

  convertToOldTestsVersion(data: NewIntegrations): Integrations {
    const tests: IntegrationsReportTests[] = data.report.tests.map(test => {
      return {
        ...test,
        results: test.results.map(result => {
          return {
            ...result,
            logs: result.logs.map(log => {
              return {
                request: {
                  url: log.url,
                  data: log.data,
                  baseUrl: log.url
                },
                requestTime: log.requestTime,
                responseBody: {
                  ticket: log.data.ticket,
                  ...log.response
                },
                responseCode: log.responseStatus,
                time: log.time,
              };
            })
          };
        })
      };
    });

    return {
      ...data,
      report: {
        ...data.report,
        tests
      },
      version: null
    };
  }

  getTestsReportsPage( id: number, path?: string ): Observable<Blob> {
    const url = getUrl(`/test/${id}`, path);
    return this.http.get(url, { params: { format: 'html' }, responseType: 'blob' }).pipe(catchError(this.handleErrors));
  }

  getTestsHistory( code: string, path?: string ): Observable<Integrations[]> {
    const url = getUrl(`/test/history/${code}`, path);
    return this.http.get<Integrations[]>(url).pipe(catchError(this.handleErrors));
  }

  private handleErrors( err ): Observable<never> {
    console.error(err);
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }
    return throwError(err);
  }
}
