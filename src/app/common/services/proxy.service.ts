import { Injectable } from '@angular/core';
import { BaseService } from '../base/base-service';
import { Subject, Observable } from 'rxjs';
import { ZERO_TIME } from '../../app.constants';
import * as moment from 'moment';
import { Proxy } from '../models/proxy.model';
import { API_ENDPOINT } from '../../app.constants';
import { HttpClient } from '@angular/common/http';
import { catchError, map, share } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

const DATE_PARAMS: string[] = ['createdAt', 'updatedAt'];

/**
 * @deprecated use src/app/pages/proxy-management/proxy-management.service.ts
 */
@Injectable()
export class ProxyService<T extends Proxy> extends BaseService<T> {

  public urlList: string = '/proxy/';
  public urlGet: string = '/proxy/';
  public urlSave: string = '/proxy/';
  public urlCreate: string = '/proxy/';
  public urlRemove: string = '/proxy';
  public urlEntityProxy: string = '/merchants';

  public _item: Subject<T>;
  public _items: Subject<T[]>;

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
    super(notifications, http);
  }

  public getList(): Observable<T[]> {
    let fn = this.processRecord.bind(this);
    let url = `${this.getUrl()}`;

    const source = this.http
      .get<T[]>(url, {
        observe: 'response'
      }).pipe(
        map(response => {
          let data = response.body;
          this.responseHeaders = this.getResponseParams(response, { pages: {} }, data);
          data = data.map(fn);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public saveProxy( id: string, data: Object ) {
    if ('_meta' in data) delete data['_meta'];

    let body = JSON.stringify(data);
    let fn = this.processRecord;
    const url = this.getItemURL(id, data, `${this.urlSave}`);

    return this.http.patch<T>(url, body).pipe(
      map(response => fn(response))
    );
  }

  public createProxy( data: Object ) {
    if ('_meta' in data) delete data['_meta'];

    let body = JSON.stringify(data);
    let fn = this.processRecord;
    const url = this.getItemURL(false, data, `${this.urlCreate}`);

    return this.http.post<T>(url, body).pipe(
      map(response => fn(response))
    );
  }

  public deleteProxy( id: string ) {
    const url = this.getItemURL(id, {}, `${this.urlRemove}`);
    return this.http.delete(url);
  }

  public processRecord( record: T ): T {
    if (!record) return record;

    DATE_PARAMS.forEach(property => {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    });

    record._meta = {
      createdAt: record.createdAt && moment(record.createdAt),
      updatedAt: record.updatedAt && moment(record.updatedAt),
    };

    return record;
  }

  public setEntityProxy( proxyId: string, path: string = ':' ) {
    let url = API_ENDPOINT;

    url += `${this.urlEntityProxy}`;
    if (path !== ':') {
      url += `/${path}`;
    }

    let data = {
      'proxyId': proxyId
    };

    let body = JSON.stringify(data);

    const source = this.http
      .patch(url, body).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        share()
      );

    return source;
  }

  public getEntityProxy( path: string = ':' ) {
    return this.invokeEntityProxyRequest('GET', path);
  }


  private invokeEntityProxyRequest( method: string, path: string = ':') {
    let url = API_ENDPOINT;

    url += `${this.urlEntityProxy}`;
    if (path !== ':') {
      url += `/${path}`;
    }

    const source = this.http.request<T>(method, url).pipe(
      map(response => this.processRecord(response)),
      share()
    );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

}
