import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class JackpotService {
  private jpnUrl = '/auth-gateway/jackpot-api/api/v2/jpn/';

  constructor( private http: HttpClient ) {
  }

  initJackpot( jackpotType: string ): Observable<any> {
    return this.http.post(`${this.jpnUrl}initType/${jackpotType}`, null);
  }

  getJackpotInfo( id: string ): Observable<any> {
    return this.http.get(`${this.jpnUrl}jackpots/${id}`);
  }

  createJackpot( jpData: Record<string, string> ): Observable<any> {
    return this.http.post(`${this.jpnUrl}jackpots`, jpData);
  }
}
