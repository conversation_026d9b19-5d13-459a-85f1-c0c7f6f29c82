import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { PERMISSIONS_LIST } from '../../app.constants';

@Injectable()
export class BiService {
  private redirectUrlParams = {
    dashboard: 'dashboards/58935fab9635aa5cf40000d7?embed=true',
    players: 'dashboards/5866366edf628bb4070006b7?embed=true',
    kpi: 'dashboards/589446fc9635aa5cf4000135?embed=true',
  };

  private promoReportUrlParams = {
    promotionParticipants: 'views/PromotionParticipants{{lang}}/PromotionParticipants',
    redeemableExpiredCoins: 'views/PromotionParticipants{{lang}}/RedeemableExpiredCoins',
  };

  private promoReportLanguages = {
    en: 'EN',
    zh: 'ZH',
  };

  private promoReportFallbackLanguage = 'en';

  private readonly REDIRECT_URL = 'https://bi.gc.skywind-tech.com/app/main#/';

  private readonly SSO_LOGIN_URL = '/v1/sisense/sso/login';

  private promoReportPrefix = 'https://bi.gc.skywind-tech.com/#/site/public/'; // initialized with prod url

  private language: string;

  private reportLinksEnabled = {
    'linkPromotionParticipants': true,
    'linkRedeemableExpiredCoins': true,
  };

  private reportPermissionList = {
    'linkPromotionParticipants': PERMISSIONS_LIST.TABLEAU_VIEW,
    'linkRedeemableExpiredCoins': PERMISSIONS_LIST.TABLEAU_VIEW,
  };

  constructor(
    protected http: HttpClient,
    protected translate: TranslateService,
    protected authService: SwHubAuthService,
  ) {
    // this.fetchBIReportLink();
    this.subscribeToLanguageChange();
  }

  getIframeUrl( type: string ) {
    let ssoLoginData = JSON.stringify({ redirectTo: this.REDIRECT_URL + this.redirectUrlParams[type] });

    return this.http
      .post(this.SSO_LOGIN_URL, ssoLoginData)
      .toPromise();
  }

  getPromotionParticipantsUrl(): string {
    const reportLink = this.promoReportUrlParams.promotionParticipants.replace('{{lang}}', this.getLangUrlParam());
    return `${this.promoReportPrefix}${reportLink}`;
  }

  getRedeemableExpiredCoinsUrl(): string {
    const reportLink = this.promoReportUrlParams.redeemableExpiredCoins.replace('{{lang}}', this.getLangUrlParam());
    return `${this.promoReportPrefix}${reportLink}`;
  }

  isReportLinkAllowed(reportName: string): boolean {
    let enabled = false;
    let allowed = false;

    if (reportName in this.reportLinksEnabled) {
      enabled = this.reportLinksEnabled[reportName];
    }

    if (reportName in this.reportPermissionList) {
      allowed = this.authService.allowedTo(this.reportPermissionList[reportName]);
    }

    return allowed && enabled;
  }

  private subscribeToLanguageChange() {
    this.translate.onLangChange.subscribe(( event: LangChangeEvent ) => {
      this.changeLanguage(event.lang);
    });
  }

  private changeLanguage( lang: string ) {
    this.language = lang;
  }

  private getLangUrlParam(): string {
    let result = this.promoReportLanguages[this.promoReportFallbackLanguage];

    if (this.language in this.promoReportLanguages) {
      result = this.promoReportLanguages[this.language];
    }

    return result;
  }

}

