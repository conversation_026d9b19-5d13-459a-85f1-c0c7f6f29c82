import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, of, throwError } from 'rxjs';
import { map, share, tap } from 'rxjs/operators';
import { API_ENDPOINT } from '../../app.constants';

import { Role } from '../../pages/users/components/roles/role.model';


@Injectable()
export class RoleService implements GridDataService<Role> {
  private cachedRoles: Role[];

  constructor( private readonly notifications: SwuiNotificationsService,
               private readonly http: HttpClient,
  ) {
  }


  public getGridData() {
    return this.http
      .get<Role[]>(this.getUrl(), {
        observe: 'response'
      }).pipe(
        map((response) => {
          this.cachedRoles = response.body || [];
          let header = response.headers;
          header = header.set('x-paging-total', this.cachedRoles.length.toString());
          return new HttpResponse({
            body: response.body,
            headers: header,
          });
        })
      );
  }

  public getRoles( forceLoad: boolean = true ): Observable<Role[]> {
    let roles$;
    if (this.cachedRoles && !forceLoad) {
      roles$ = of(this.cachedRoles);
    } else {
      roles$ = this.http
        .get<Role[]>(this.getUrl()).pipe(
          tap(( roles ) => this.cachedRoles = roles)
        );
    }
    return roles$.pipe(share());
  }

  public getRole( role ): Observable<Role> {
    const { id } = role;
    return this.http
      .get<Role>(`${this.getUrl()}/${id}`).pipe(
        map(r => new Role(r)),
        share()
      );
  }

  public editRole( role: Role ): Observable<any | void> {
    const source = this.http.patch(`${this.getUrl()}/${role.id}`, role)
      .pipe(share());
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public addRole( role: Role ): Observable<any | void> {
    const source = this.http.post(this.getUrl(), role)
      .pipe(share());
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public deleteRole( path: string, isForceDelete: boolean ): Observable<any | void> {
    const url = `${this.getUrl()}/${path}`;
    const params = new HttpParams().set('force', `${isForceDelete}`);
    const source = this.http.delete(url, { params })
      .pipe(share());
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  private handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }

  private getUrl( path?: string ): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/roles`;
  }
}
