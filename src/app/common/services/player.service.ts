import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { GridRequestData, SettingsService, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { catchError, map, share, switchMap } from 'rxjs/operators';

import { BaseService } from '../base';
import { CustomerData, TransferData } from '../models/customer-balance.model';
import { Player, PlayerCredentials } from '../typings';
import { CsvSchema, CsvService, transformDate } from './csv.service';
import { FORMAT_DATETIME } from '../../app.constants';
import { getCurrencyLabel, transformFormatCurrencyValue } from '../core/currecy-transform';

const csvSchema = ( timezoneName: string, format: string, updatedAt: CsvSchema, balance?: CsvSchema ): CsvSchema[] => [
  {
    name: 'brandTitle',
    title: 'CUSTOMERS.CSV.brandTitle',
  },
  {
    name: 'code',
    title: 'CUSTOMERS.CSV.code',
  },
  {
    name: 'firstName',
    title: 'CUSTOMERS.CSV.firstName',
  },
  {
    name: 'lastName',
    title: 'CUSTOMERS.CSV.lastName',
  },
  {
    name: 'currency',
    title: 'CUSTOMERS.CSV.currency',
    transform(data: any): string {
      return getCurrencyLabel(data);
    }
  },
  {
    name: 'status',
    title: 'CUSTOMERS.CSV.status',
  },
  {
    name: 'isTest',
    title: 'CUSTOMERS.CSV.type',
    transform( data: Player['isTest'] | string ): string {
      return data?.toString() === 'true' ? 'Test' : 'Real';
    }
  },
  {
    name: 'createdAt',
    title: 'CUSTOMERS.CSV.createdAt',
    transform: transformDate(timezoneName, format),
  },
  updatedAt,
  {
    name: 'country',
    title: 'CUSTOMERS.CSV.country',
  },
  ...(balance ? [balance] : [])
];

@Injectable()
export class PlayerService extends BaseService<Player> implements SwuiGridDataService<Player> {
  public urlList: string = '/players/';
  public urlGet: string = '/players/';
  public urlSave: string = '/players';
  public urlCreate: string = '/players/';
  public urlRemove: string = '/players/';

  private baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( notifications: SwuiNotificationsService,
               http: HttpClient,
               private readonly translate: TranslateService,
               private readonly csvService: CsvService,
               private readonly setting: SettingsService
  ) {
    super(notifications, http);
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ): Observable<HttpResponse<Player[]>> {
    let path;

    if (typeof requestData !== 'undefined' && requestData.hasOwnProperty('path')) {
      path = requestData['path'];
    } else if (params.has('path')) {
      path = params.get('path');
    }

    const response = this.http.get<Player[]>(this.getUrl(path), {
      params,
      observe: 'response'
    }).pipe(
      map(resp => {
        resp.body.map(( player: Player ) => this.processRecord(player));
        return resp;
      }),
      share(),
    );

    response.subscribe(
      () => {
      },
      ( error ) => {
        this.handleErrors(error);
      },
      () => {
      }
    );

    return response;
  }

  public setStatus( player: Player, status: string, path?: string ): Observable<Player> {
    const method = status === 'normal' ? 'DELETE' : 'PUT';


    const url = `${this.getUrl(path)}${player.code}/suspended`;

    const source = this.http.request<Player>(method, url).pipe(
      map(data => this.processRecord(data)),
      share()
    );
    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public register( player: Player, path? ): Observable<any> {
    const url = `${this.baseApiEndpoint}/${path ? 'entities/${path}/' : ''}players/register`;
    const res = this.http.post<Player>(url, player).pipe(
      map(data => this.processRecord(data)),
      share()
    );

    res.subscribe(
      data => {
        this._item.next(data);
        this.notifications.success(`Player ${data.code} created!`);
      },
      this.handleErrors.bind(this)
    );
    return res;

  }

  public transfer( transferData: TransferData ): Observable<any> {
    const type = transferData.direction === 'in' ? 'deposits' : 'withdrawals';
    const { code, currency } = transferData.player;
    const url = `${this.getUrl(transferData.path)}${code}/${type}/${currency}/${transferData.amount}`;
    return this.http.post(url, {}).pipe(
      catchError(err => this.handleErrors.call(this, err))
    );
  }

  public setStatuses( id: Array<string>, status: string, path?: string ) {
    let urlPath = path ? `/entities/${path}/` : ``;
    let url = `${this.baseApiEndpoint}${urlPath}/players/group/status`;

    const source = this.http.post(url, { status, id }).pipe(
      share()
    );

    source.subscribe(
      () => {
      },
      this.handleErrors.bind(this)
    );

    return source;
  }

  public getItem( params: CustomerData, withLastAction?: boolean ) {
    console.log('Players service: get element', params.id);
    const url = `${this.getUrl(params.path)}${params.id}`;

    let fn = this.processRecord.bind(this);

    const source = this.http
      .get<Player>(url, {
        params: BaseService.getRequestParams({ values: { withLastAction } }),
        observe: 'response'
      }).pipe(
        map(response => {
          this.responseHeaders = this.getResponseParams(response);
          const data = fn(response.body);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors(err)
    );
    return source;
  }

  public saveType( data: Player, isTest: boolean, path?: string ) {
    console.log('Player service: save type', data);

    const url = `${this.getUrl(path)}${data.code}`;
    let body = JSON.stringify({ isTest: isTest });

    const source = this.http.patch<Player>(url, body)
      .pipe(
        map(_data => this.processRecord(_data)),
        switchMap(() => this.getItem({ id: data.code, path: path })),
        share()
      );
    source.subscribe(
      player => this._item.next(player),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public updatePlayer( body: any, playerCode: string, path?: string ): Observable<any> {
    const url = `${this.getUrl(path)}${playerCode}`;

    const source = this.http.patch(url, body).pipe(
      share()
    );

    source.subscribe(() => {
        const message = this.translate.instant('CUSTOMERS.NOTIFICATIONS.edit_success', { code: playerCode });
        this.notifications.success(message, '');
      },
      this.handleErrors.bind(this)
    );

    return source;
  }

  public processRecord( record: Player ): Player {
    record._meta = {
      createdAt: record.createdAt && moment(record.createdAt),
      updatedAt: record.lastLogin && moment(record.updatedAt),
      lastLogin: record.lastLogin && moment(record.lastLogin),
      lastAction: record.lastAction && moment(record.lastAction),
    };

    record.provider = 'Default';
    {
      const { currency = 'USD', balances } = record;
      const balance = balances ? balances[currency].main : 0;
      Object.assign(record, { balance });
    }
    record.login = record.code;
    record.agentDomain = record.agentDomain || '';
    record.operator = 'DEMO';
    record.contentProvider = 'Asia ' + Math.round(Math.random() * 100);
    record.registrationIP = Math.round(Math.random() * 200) + '.' + Math.round(Math.random() * 254) + '.' +
      Math.round(Math.random() * 254) + '.' + (Math.round(Math.random() * 254) || 1);

    return record as Player;
  }

  public changePassword( playerCode: string, playerCredentials: PlayerCredentials, path?: string ) {
    const url = `${this.getUrl(path)}${playerCode}/password`;
    return this.http
      .put(url, playerCredentials).pipe(
        catchError(err => this.handleErrors.call(this, err)),
        share()
      );
  }

  public downloadCsv( limit: number, withBalance = false ): Observable<any> {
    const fileName = `Export Players list ${moment().format('YYYY-MM-DD HH:MM')}`;
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const schema = csvSchema(timezoneName, datetimeFormat,
      {
        name: 'updatedAt',
        title: 'CUSTOMERS.CSV.updatedAt',
        transform( date: string ) {
          return date ? moment(date).tz(timezoneName).format(datetimeFormat) : '';
        },
      },
      withBalance && {
        name: 'balances',
        title: 'CUSTOMERS.CSV.balance',
        transform( value: string, record: Player ): string {
          let balances: Player['balances'];
          try {
            if (value) {
              balances = JSON.parse(value.replace(/'/g, '"'));
            }
          } catch (e) {
            console.warn(e);
          }
          const balance = balances?.[record.currency ?? 'USD']?.main ?? 0;
          return transformFormatCurrencyValue(balance, record.currency ?? 'USD');
        }
      }
    );
    return this.csvService.download(this.getUrl.bind(this), schema, fileName, {
      withBalance,
      limit
    });
  }

  public exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const fileName = `Export Players list ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const schema = csvSchema(timezoneName, datetimeFormat,
      {
        name: 'lastLogin',
        title: 'CUSTOMERS.CSV.updatedAt',
        transform( date: string ) {
          return date ? moment(date).tz(timezoneName).format(datetimeFormat) : '';
        },
      },
      {
        name: 'balance',
        title: 'CUSTOMERS.CSV.balance',
        transform( value: string, rows: Record<string, any> ): string {
          return transformFormatCurrencyValue(Number(value), rows.currency);
        }
      }
    );
    this.csvService.exportToCsv(schema, data, fileName, columns);
  }

  public unblockPlayer( player: Player, path?: string ) {
    const url = `${this.getUrl(path)}${player.code}/login-lock`;
    return this.http.delete<Player>(url).pipe(
      catchError(err => this.handleErrors.call(this, err)),
      share()
    );
  }

  public resetNicknameChangeAttempts( brandId: string, playerCode: string ) {
    const url = `${BaseService.apiEndpoint}/players/info/${brandId}/${playerCode}/change-nickname/attempts`;
    return this.http.delete<Player>(url).pipe(
      catchError(err => this.handleErrors.call(this, err))
    );
  }
}
