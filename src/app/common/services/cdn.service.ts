import { Injectable } from '@angular/core';
import { Observable, zip } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map, share } from 'rxjs/operators';

const URL = '/cdn';

interface SwGames {
  games: {
    [key: string]: {
      images: {
        poster: string;
      };
    }
  };
}

interface StudioGames {
  [key: string]: StudioGame;
}

interface StudioGame {
  logo: {
    en: string;
  };
}

export interface StudioGameInfo {
  gamePath: string;
  descriptionEN?: string;
  shortDescriptionEN?: string;
  marketingKit?: {
    [key: string]: any;
    Screenshots?: string[];
  };
}

@Injectable()
export class CdnService {
  readonly gameImages: Observable<{ [key: string]: string }>;

  constructor( private http: HttpClient ) {
    this.gameImages = zip(
      this.http.get<StudioGames>(`${URL}/gamestudios/games.json`),
      this.http.get<SwGames>(`${URL}/swbo/games/games.json`)
    ).pipe(
      map(( [studioGames, swGames] ) => {
        const images = {};
        Object.entries(studioGames).forEach(( [key, { logo }] ) => {
          if (logo && logo.en) {
            images[key] = `${URL}/gamestudios/${logo.en}`;
          }
        });
        Object.entries(swGames.games).forEach(( [key, { images: { poster } }] ) => {
          images[key] = poster;
        });
        return images;
      }),
      share()
    );
  }

  getImageUrl( code: string ): Observable<string> {
    return this.gameImages.pipe(map(games => games[code]));
  }

  getStudioGameInfo( code: string ): Observable<StudioGameInfo> {
    const gamePath = `${URL}/gamestudios/${code}`;
    return this.http.get(`${gamePath}/info.json`).pipe(
      map(gameInfo => ({ ...gameInfo, gamePath })),
      share()
    );
  }
}
