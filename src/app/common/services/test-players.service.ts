import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { API_ENDPOINT } from '../../app.constants';
import { TestPlayer } from '../typings/test-player';

@Injectable()
export class TestPlayersService implements SwuiGridDataService<TestPlayer> {

  public urlTestPlayers = '/testplayers/';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient,
  ) {
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ): Observable<HttpResponse<TestPlayer[]>> {
    let path;

    if (typeof requestData !== 'undefined' && requestData.hasOwnProperty('path')) {
      path = requestData['path'];
    } else if (params.has('path')) {
      path = params.get('path');
    }

    return this.http
      .get<TestPlayer[]>(this.getUrl(path), {
        params,
        observe: 'response'
      }).pipe(
        catchError(err => this.handleErrors(err)),
        share()
      );
  }

  createTestPlayer( body, path ) {
    return this.http
      .post(this.getUrl(path), body)
      .pipe(
        catchError(err => this.handleErrors(err))
      );
  }

  editTestPlayer( body, path ) {
    return this.http
      .put(this.getUrl(path), body)
      .pipe(
        catchError(err => this.handleErrors(err))
      );
  }

  deleteTestPlayer( path, playerCode ) {
    let url = `${this.getUrl(path)}/${playerCode}`;

    return this.http
      .delete(url)
      .pipe(
        catchError(err => this.handleErrors(err)),
      );
  }

  getUrl( path?: string ): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}${this.urlTestPlayers}`;
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
