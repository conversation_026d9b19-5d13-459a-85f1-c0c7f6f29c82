import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, iif, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map, mapTo, mergeMap, share, takeUntil, tap } from 'rxjs/operators';

import { LobbyExtendedData, LobbyShortData, process, UpdateLobbyData } from '../../pages/lobby/lobby.model';
import { LobbyBuildService } from './lobby-build.service';
import { API_ENDPOINT } from '../../app.constants';

import * as moment from 'moment';
import { Params } from '@angular/router';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { environment } from '../../../environments/environment';


function buildUrl( path?: string ): string {
  return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/lobbies`;
}

function fn<T extends { id: string, key: string }>( data: { id: string, [key: string]: any } ): T {
  return Object.assign({}, data, { key: data.id.toLowerCase() }) as T;
}

@Injectable()
export class LobbyService {
  private readonly _item = new Subject<LobbyExtendedData>();
  private readonly _items = new Subject<LobbyShortData[]>();
  private readonly _loading = new BehaviorSubject<boolean>(true);
  private _formSubmitted = new BehaviorSubject<boolean>(false);
  private destroy$ = new Subject();

  constructor(
    private readonly notifications: SwuiNotificationsService,
    private readonly http: HttpClient,
    private readonly buildService: LobbyBuildService,
  ) {
  }


  set formSubmitted( val: boolean ) {
    this._formSubmitted.next(val);
  }

  get formSubmitted$(): Observable<boolean> {
    return this._formSubmitted as Observable<boolean>;
  }

  get item(): Observable<LobbyExtendedData> {
    return this._item.asObservable();
  }

  get items(): Observable<LobbyShortData[]> {
    return this._items.asObservable();
  }

  get loading(): Observable<boolean> {
    return this._loading;
  }

  cleanList() {
    this.destroy$.next();
    return this._items.next([]);
  }

  getList( path?: string ): Observable<LobbyShortData[]> {
    this._loading.next(true);
    const params = new URLSearchParams();
    params.set('fields', 'id,title,description,status,info,isDefault,createdAt,updatedAt');
    return this.http.get<LobbyShortData[]>(`${buildUrl(path)}?${params}`).pipe(
      map(records => records.map(record => fn<LobbyShortData>(record))),
      map(lobbies => lobbies.sort(( a, b ) => moment(a.createdAt).unix() - moment(b.createdAt).unix())),
      tap<LobbyShortData[]>(lobbies => {
        this._loading.next(false);
        this._items.next(lobbies);
      }),
      catchError(err => this.handleErrors(err)),
      takeUntil(this.destroy$)
    );
  }

  getItem( { path, id }: Params ): Observable<LobbyExtendedData> {
    this._item.next(null);
    return this.http.get<LobbyExtendedData>(`${buildUrl(path)}/${id}`).pipe(
      map(record => fn<LobbyExtendedData>(record)),
      tap<LobbyExtendedData>(item => {
        this._item.next(item);
      }),
      catchError(err => this.handleErrors(err)),
    );
  }

  getBuilds( lobbies: LobbyShortData[], path?: string ): Observable<LobbyShortData[]> {
    return this.buildService.query({ path }).pipe(
      map(builds => lobbies.map(lobby => process(builds, lobby))),
      tap(items => {
        this._items.next(items);
      }),
      catchError(err => this.handleErrors(err)),
      takeUntil(this.destroy$)
    );
  }

  getBuild( lobby: LobbyShortData, path?: string ): Observable<LobbyShortData> {
    return this.buildService.query({ path, id: lobby.id }).pipe(
      map(build => process({ [lobby.key]: build }, lobby)),
      catchError(err => this.handleErrors(err)),
    );
  }

  copy( title: string, { id }: LobbyShortData, path?: string ): Observable<LobbyExtendedData> {
    return this.http.get<LobbyExtendedData>(`${buildUrl(path)}/${id}`).pipe(
      map(record => fn<LobbyExtendedData>(record)),
      map<LobbyExtendedData, UpdateLobbyData>(( { description, info, theme } ) => ({
        title,
        description,
        theme,
        info: {
          ...info,
          requiredBuildAt: new Date().toISOString(),
          theme: {
            ...info.theme,
            version: environment.APP_VERSION
          }
        }
      })),
      mergeMap(record => this.http.post<LobbyExtendedData>(`${buildUrl(path)}`, record)),
      mergeMap(record => this.buildService.build(record, path).pipe(map(() => record))),
      catchError(err => this.handleErrors(err))
    );
  }

  create( data: UpdateLobbyData, path?: string ): Observable<LobbyExtendedData> {
    data.info.requiredBuildAt = new Date().toISOString();
    data.info.theme.version = environment.APP_VERSION;
    return this.http.post<LobbyExtendedData>(buildUrl(path), data).pipe(
      map(record => fn<LobbyExtendedData>(record)),
      mergeMap(record => this.buildService.build(record, path).pipe(
        map(() => record)
      )),
      catchError(err => this.handleErrors(err)),
    );
  }

  update( id: string, data: UpdateLobbyData, rebuild = false, path?: string ): Observable<LobbyExtendedData> {
    if (rebuild) {
      data.info.requiredBuildAt = new Date().toISOString();
      data.info.theme.version = environment.APP_VERSION;
    }
    return this.http.patch<LobbyExtendedData>(`${buildUrl(path)}/${id}`, data).pipe(
      map(record => fn<LobbyExtendedData>(record)),
      mergeMap(record => iif(() => rebuild,
        this.buildService.build(record, path).pipe(mapTo(record)),
        of(record)
      )),
      catchError(err => this.handleErrors(err)),
      share()
    );
  }

  delete( id: string, path?: string ): Observable<void> {
    return this.buildService.delete(id, path).pipe(
      mergeMap(() => this.http.delete<void>(`${buildUrl(path)}/${id}`)),
      catchError(err => this.handleErrors(err)),
    );
  }

  private handleErrors( err ): Observable<never> {
    console.error(err);
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }
    return throwError(err);
  }
}
