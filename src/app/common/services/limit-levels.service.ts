import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { BaseService } from '../base';
import { GameLimitLevel, LimitLevel } from '../typings/limit-level';

@Injectable()
export class LimitLevelsService {

  public urlGameLimitLevels = 'game-limit-levels';
  public urlLimitLevels = 'limit-levels';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
  }

  getGameLimitLevels( path: string ): Observable<GameLimitLevel[]> {
    let url = `${this.getUrl(path)}/${this.urlGameLimitLevels}`;

    return this.http.get<GameLimitLevel[]>(url, {}).pipe(
      share(),
    );
  }

  getLimitLevels( path: string ): Observable<LimitLevel[]> {
    let url = `${this.getUrl(path)}/${this.urlLimitLevels}`;
    const params = new HttpParams().set('all', `true`);

    return this.http.get<LimitLevel[]>(url, { params }).pipe(
      share(),
    );
  }

  createNewLevel( path, body ) {
    let url = `${this.getUrl(path)}/${this.urlLimitLevels}`;

    return this.http.post(url, body).pipe(
      share(),
    );
  }

  createNewGameLimitLevel( path, body ) {
    let url = `${this.getUrl(path)}/${this.urlGameLimitLevels}`;

    return this.http.post(url, body)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  patchLimitLevel( path, levelId, body ) {
    let url = `${this.getUrl(path)}/${this.urlLimitLevels}/${levelId}`;

    return this.http.patch(url, body).pipe(
      share(),
    );
  }

  removeGameLimitLevel( path, id ) {
    let url = `${this.getUrl(path)}/${this.urlGameLimitLevels}/${id}`;

    return this.http.delete(url).pipe(
      catchError(this.handleErrors),
      share(),
    );
  }

  getUrl( path: string ): string {
    if (path && path !== ':') {
      return path ? `${BaseService.apiEndpoint}/entities/${path}/` : `${BaseService.apiEndpoint}`;
    } else {
      return `${BaseService.apiEndpoint}`;
    }
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
