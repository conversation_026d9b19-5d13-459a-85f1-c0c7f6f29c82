import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { EMPTY } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { EntityService } from '../entity.service';


@Injectable()
export class BriefResolver implements Resolve<any> {

  constructor( private entity: EntityService<any> ) {
  }

  resolve() {
    return this.entity.getBrief().pipe(
      catchError(err => {
        console.error(err);
        return EMPTY;
      })
    );
  }
}
