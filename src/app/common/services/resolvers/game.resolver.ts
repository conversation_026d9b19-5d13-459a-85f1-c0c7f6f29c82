import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable, zip } from 'rxjs';
import { map } from 'rxjs/operators';
import { GameService } from '../game.service';
import { Game } from '../../typings';

@Injectable()
export class GameResolver implements Resolve<Game> {
  constructor( private readonly service: GameService ) {
  }

  resolve( { params: { code } }: ActivatedRouteSnapshot ): Observable<Game> {
    return zip(
      this.service.getGame(code),
      this.service.getEntityGame(code)
    ).pipe(
      map(( [game, { code: gameCode, limitFiltersWillBeApplied, settings }] ) => ({
        ...game,
        gameCode,
        limitFiltersWillBeApplied,
        settings
      }))
    );
  }
}
