import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { GameService } from '../game.service';
import { Game } from '../../typings';

@Injectable()
export class GamesPathOptionalResolver implements Resolve<Game[]> {
  constructor( private service: GameService ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    const path = route.params['path'];
    return this.service.getAllGames(path);
  }
}
