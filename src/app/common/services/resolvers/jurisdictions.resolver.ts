import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Jurisdiction } from '../../typings/jurisdiction';
import { JurisdictionService } from '../jurisdiction.service';

@Injectable()
export class JurisdictionsResolver implements Resolve<Jurisdiction[]> {
  constructor( private service: JurisdictionService ) {

  }

  resolve( ) {
    return this.service.getJurisdictions();
  }
}
