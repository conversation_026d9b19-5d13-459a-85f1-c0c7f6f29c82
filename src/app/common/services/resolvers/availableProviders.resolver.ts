import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { GameProvider } from '../../../pages/games-management/game-provider.model';
import { GameProviderService } from '../game-provider.service';


@Injectable()
export class AvailableProvidersResolver implements Resolve<GameProvider[]> {
  constructor(
    private service: GameProviderService<GameProvider>,
  ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    let path = route.params['path'];
    return this.service.getAvailableGameProviders(path === ':' ? '' : path)
      .pipe(
        catchError(() => of([]))
      );
  }
}
