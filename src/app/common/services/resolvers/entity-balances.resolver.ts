import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { EntityService } from '../entity.service';
import { Balance } from '../../typings/balance';


@Injectable()
export class EntityBalancesResolver implements Resolve<Balance> {
  constructor( private service: EntityService<Balance> ) {

  }

  resolve( route: ActivatedRouteSnapshot ) {
    let path = !!route.params['path'] ? route.params['path'] : ':';
    return this.service.getBalances(path);
  }
}
