import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { API_ENDPOINT } from '../../../app.constants';
// import { AuthService } from '../../auth/auth.service';
import { HttpClient } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { EMPTY } from 'rxjs';
import { SwHubAuthService } from '@skywind-group/lib-swui';


@Injectable()
export class CurrentUserResolver implements Resolve<any> {

  constructor( private http: HttpClient,
               private auth: SwHubAuthService
  ) {
  }

  resolve() {
    return this.http.get(`${API_ENDPOINT}/users/${this.auth.username}/profile`).pipe(
      catchError(err => {
        console.error(err);
        return EMPTY;
      })
    );
  }
}
