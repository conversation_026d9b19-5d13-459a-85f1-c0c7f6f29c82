import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { tap } from 'rxjs/operators';
import { BusinessStructureService } from '../../../pages/business-management/components/mat-business-structure/business-structure.service';

import { EntityService } from '../entity.service';
import { Entity } from '../../models/entity.model';


@Injectable()
export class StructureResolver implements Resolve<any> {

  constructor( private service: EntityService<Entity> ) {
  }

  resolve() {
    return this.service.getStructure('', true);
  }
}

@Injectable()
export class ShortStructureResolver implements Resolve<any> {

  constructor( private service: EntityService<Entity>,
               private bsService: BusinessStructureService ) {
  }

  resolve() {
    return this.service.getShortStructure().pipe(
      tap((data) => this.bsService.setStructure(data))
    );
  }
}

@Injectable()
export class ActiveShortStructureResolver implements Resolve<any> {

  constructor( private service: EntityService<Entity> ) {
  }

  resolve() {
    return this.service.getShortStructure('', false, true);
  }
}
