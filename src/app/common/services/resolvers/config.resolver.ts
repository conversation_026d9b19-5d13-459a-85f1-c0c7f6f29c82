import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Resolve } from '@angular/router';
import { ServerConfig } from '../../typings/server-config';
import { catchError } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

@Injectable()
export class ConfigResolver implements Resolve<ServerConfig> {
  constructor( private http: HttpClient ) {
  }

  resolve() {
    return this.http.get<ServerConfig>('/api/config').pipe(
      catchError(err => {
        console.error(err);
        return EMPTY;
      })
    );
  }
}
