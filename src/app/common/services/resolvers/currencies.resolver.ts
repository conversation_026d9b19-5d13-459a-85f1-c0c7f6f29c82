import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Currency } from '../../typings/currency';
import { CurrencyService } from '../currency.service';


@Injectable()
export class CurrenciesResolver implements Resolve<Currency[]> {
  constructor( private service: CurrencyService<Currency> ) {

  }

  resolve( ) {
    return this.service.getList({ pages: { limit: 10000 } });
  }
}
