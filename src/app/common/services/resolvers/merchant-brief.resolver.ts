import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import { Entity } from '../../typings';
import { EntityService } from '../entity.service';

@Injectable()
export class MerchantBriefResolver implements Resolve<any> {

  constructor( private entityService: EntityService<Entity & { merchant: any }> ) {
  }

  resolve(): Observable<Entity & { hasCustomers: boolean }> {
    return this.entityService.getBrief().pipe(
      mergeMap(brief => {
        if ('isMerchant' in brief && brief.isMerchant) {
          return this.entityService.getMerchantEntityItem(':').pipe(
            map(entity => ({
              ...brief,
              hasCustomers: 'merchant' in entity &&
                'isPromoInternal' in entity.merchant.params &&
                entity.merchant.params.isPromoInternal,
              params: entity.merchant.params,
            }))
          );
        }
        return of({ ...brief, hasCustomers: true });
      })
    );
  }
}
