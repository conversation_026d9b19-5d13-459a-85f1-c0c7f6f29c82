import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { of } from 'rxjs';

import { GameGroupService } from '../game-group.service';

@Injectable()
export class GameGroupsResolver implements Resolve<any> {
  constructor( private gameGroupService: GameGroupService ) {
  }

  resolve( route: ActivatedRouteSnapshot ) {
    const { params: { path } } = route;

    return this.gameGroupService.getGameGroupsList(path)
      .pipe(
        catchError(() => {
          return of(null);
        })
      );
  }
}
