import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { PERMISSIONS_NAMES } from '../../../app.constants';
import { EntitySettingsModel } from '../../models/entity-settings.model';
import { EntitySettingsService } from '../entity-settings.service';

@Injectable()
export class EntitySettingsResolver implements Resolve<Object> {
  constructor(
    private service: EntitySettingsService<EntitySettingsModel>,
    private authService: SwHubAuthService,
  ) {

  }

  resolve( route: ActivatedRouteSnapshot ) {
    let resolver$ = of(null);
    if (this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_VIEW])) {
      let path = !!route.params['path'] ? route.params['path'] : '';
      resolver$ = this.service.getSettings(path);
    }
    return resolver$;
  }
}
