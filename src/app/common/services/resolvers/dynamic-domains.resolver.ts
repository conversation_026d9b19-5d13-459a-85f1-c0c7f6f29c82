import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { DomainsManagementService } from '../../../pages/domains-management/domains-management.service';
import { Domain, DOMAIN_TYPES } from '../../models/domain.model';


@Injectable()
export class DynamicDomainsResolver implements Resolve<Domain[]> {

  constructor( private service: DomainsManagementService ) {
  }

  resolve() {
    return this.service.getList(DOMAIN_TYPES.dynamic);
  }
}
