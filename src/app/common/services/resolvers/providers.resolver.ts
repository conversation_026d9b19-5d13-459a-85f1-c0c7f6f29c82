import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { GameProviderService } from '../game-provider.service';
import { GameProvider } from '../../../pages/games-management/game-provider.model';


@Injectable()
export class ProvidersResolver implements Resolve<GameProvider[]> {

  constructor( private readonly service: GameProviderService<GameProvider> ) {
  }

  resolve() {
    return this.service.getAllGameProviders();
  }
}
