import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Resolve } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { GameProvider } from '../../../pages/games-management/game-provider.model';
import { catchError } from 'rxjs/operators';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { PERMISSIONS_LIST } from '../../../app.constants';

@Injectable()
export class AvailableGameProvidersResolver implements Resolve<GameProvider[]> {
  constructor( private readonly http: HttpClient, private readonly authService: SwHubAuthService ) {
  }

  resolve(): Observable<GameProvider[]> {
    if (this.authService.allowedTo(PERMISSIONS_LIST.EXTERNAL_GAME_PROVIDER_AVAILABILITY)) {
      return this.http.get<GameProvider[]>('/v1/gameproviders/available').pipe(
        catchError(err => {
          console.error(err);
          return EMPTY;
        })
      );
    }
    return of([]);
  }
}
