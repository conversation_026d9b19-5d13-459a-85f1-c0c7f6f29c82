import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';

import { SchemaDefinitionsService } from '../schema-definitions.service';
import { SchemaDefinition } from '../../typings/schema-definition';

@Injectable()
export class SchemaDefinitionsResolver implements Resolve<SchemaDefinition[]> {

  constructor( private readonly service: SchemaDefinitionsService<SchemaDefinition> ) {
  }

  resolve() {
    return this.service.getSchemaDefinitions();
  }
}
