import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Language } from '../../typings/language';
import { LanguagesService } from '../languages.service';



@Injectable()
export class LanguagesResolver implements Resolve<Language[]> {
  constructor( private service: LanguagesService<Language> ) {

  }

  resolve( ) {
    return this.service.getList({ pages: { limit: 10000 } });
  }
}
