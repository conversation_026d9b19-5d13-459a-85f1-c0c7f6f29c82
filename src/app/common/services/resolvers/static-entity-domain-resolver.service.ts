import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { EntityDomainService } from '../../../pages/domains-management/entity-domain.service';
import { Domain, DOMAIN_TYPES } from '../../models/domain.model';


@Injectable()
export class StaticEntityDomainResolver implements Resolve<Domain> {
  constructor( private service: EntityDomainService ) {
  }

  resolve(route: ActivatedRouteSnapshot) {
    let path = !!route.params['path'] ? route.params['path'] : '';
    return this.service.getEntityDomain(DOMAIN_TYPES.static, path);
  }
}
