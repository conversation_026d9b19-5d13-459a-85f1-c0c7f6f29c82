import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Country } from '../../typings/country';
import { CountryService } from '../country.service';

@Injectable()
export class CountriesResolver implements Resolve<Country[]> {

  constructor( private readonly service: CountryService<Country> ) {
  }

  resolve() {
    return this.service.getList({ pages: { limit: 10000 } });
  }
}
