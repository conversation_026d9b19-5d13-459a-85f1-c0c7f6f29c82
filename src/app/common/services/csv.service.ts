import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, OnDestroy, Optional } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SettingsService, SwHubEntityService, SwuiNotificationsService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { combineLatest, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map, switchMap, take, tap } from 'rxjs/operators';
import { FileType, simulateBrowserDownload } from '../lib/files';
import { isEmpty } from 'lodash';

export interface CsvSchema {
  name: string;
  title: string;
  defaultValue?: string;

  transform?( data: any, rows: Record<string, any> ): string;
}

export function transformFloat( value: any ) {
  return parseFloat(String(value ?? 0)).toFixed(2);
}

export function transformDate( timezoneName: string, format: string ) {
  return function( value?: string ) {
    return value ? moment(value).tz(timezoneName).format(format) : '';
  };
}

export function overrideString<T = any>( value?: string ): T | undefined {
  if (value) {
    try {
      return JSON.parse(value
        .replace(new RegExp(/(\w)'(\w)/, 'g'), '$1&*&$2')
        .replace(new RegExp('\'', 'g'), '"')
        .replace(new RegExp('&*&', 'g'), '\''));
    } catch (e) {
      console.warn(e);
    }
  }
}

function parseCsv( s: string ) {
  // tslint:disable-next-line:quotemark
  const lineSeparator = "\n";

  s = s.replace(/\r/g, lineSeparator).replace(/\n+/g, lineSeparator);
  // Get rid of any trailing \n
  s = s.charAt(s.length - 1) !== lineSeparator ? s : s.substring(0, s.length - 1);

  let fieldQuoted = false;
  let inQuote = false;
  let field: string | number = '';  // Buffer for building up the current field
  let row = [];
  const rows = [];

  for (let i = 0; i < s.length; i += 1) {
    const cur = s.charAt(i);

    // If we are at EOF or EOR
    if (inQuote === false && (cur === ',' || cur === lineSeparator)) {
      field = fieldQuoted ? field : field.trim();
      // Add the current field to the current row
      row.push(field);
      // If this is EOR append row to output and flush row
      if (cur === lineSeparator) {
        rows.push(row);
        row = [];
      }
      // Flush the field buffer
      field = '';
      fieldQuoted = false;
    } else {
      if (cur === '"') {
        if (!inQuote) {
          // We are not in a quote, start a quote
          inQuote = true;
          fieldQuoted = true;
        } else {
          // Next char is ", this is an escaped "
          if (s.charAt(i + 1) === '"') {
            field += '"';
            // Skip the next char
            i += 1;
          } else {
            // It's not escaping, so end quote
            inQuote = false;
          }
        }
      } else {
        // If it's not a ", add it to the field buffer
        field += cur;
      }
    }
  }

  // Add the last field
  field = fieldQuoted ? field : field.trim();
  row.push(field);
  rows.push(row);

  if (rows.length > 0) {
    const keys = rows[0].filter(f => typeof f === 'string') as string[];
    return Object.freeze(
      rows
        .filter(( r, index ) => r && index > 0)
        .map(( r ) => {
          const object = {} as any;
          keys.forEach(( key, keyIndex ) => {
            object[key] = r[keyIndex];
          });
          return Object.freeze(object);
        }),
    );
  }
  return Object.freeze([]);
}

const LIMIT_RECORDS = 10000;

@Injectable()
export class CsvService implements OnDestroy {
  private readonly destroy$ = new Subject();

  constructor( private readonly settingsService: SettingsService,
               private readonly translate: TranslateService,
               private readonly http: HttpClient,
               private readonly notifications: SwuiNotificationsService,
               private readonly route: ActivatedRoute,
               @Optional() private readonly filterService: SwuiTopFilterDataService,
               @Optional() private readonly entityService: SwHubEntityService
  ) {
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  download( getUrl: ( path?: string ) => string, csvSchema: CsvSchema[], fileName: string, addParams = {},
            forcePath = '' ): Observable<any> {
    const filter$ = this.filterService?.appliedFilter ?? of({});
    const entity$ = !forcePath && this.entityService ? this.entityService.entitySelected$ : of({ path: forcePath } as any);
    const { sortOrder, sortBy } = this.route.snapshot.queryParams;
    const extraParams = {
      limit: `${LIMIT_RECORDS}`,
      ...addParams
    };
    return combineLatest([
      filter$.pipe(
        map(params => Object.entries(params)
          .filter(( [, value] ) => !isEmpty(value))
          .reduce(( res, [key, value] ) => ({
            ...res,
            [key]: moment.isMoment(value) ? value.toISOString() : value
          }), {})),
        map(params => ({
          ...params,
          format: 'csv',
          offset: '0',
          ...(sortOrder ? { sortOrder } : {}),
          ...(sortBy ? { sortBy } : {}),
          ...extraParams
        }))
      ),
      entity$.pipe(
        map(( { path } ) => path ?? ''),
        map(path => path.startsWith(':') ? path.substring(1) : path),
        map(path => getUrl(path))
      )
    ]).pipe(
      switchMap(( [params, url] ) => this.http.get(url, { params, responseType: 'text' })),
      catchError(err => this.handleErrors(err)),
      take(1),
      tap(( value: string ) => {
        const data = parseCsv(value);
        this.toCsv(csvSchema, data, fileName, data.length === Number(extraParams['limit']));
      })
    );
  }

  exportToCsv( schema: CsvSchema[], rows: Record<string, any>[], fileName: string, dataHeaders: string[] ): void {
    const csvSchema = schema.filter(( { name } ) => dataHeaders.includes(name));
    this.toCsv(csvSchema, rows, fileName);
  }

  toCsv( csvSchema: CsvSchema[], rows: readonly Record<string, any>[], fileName: string, recordsLimit = false ): void {
    const defTransform = function( value: any, row?: any, transformFn? ): string {
      const data = transformFn ? `${transformFn(value, row)}` : String(value ?? '');
      return data.includes(',') ? `"${data.replace(/"/g, "'")}"` : data;
    };
    const newRows: string[][] = Array.from(new Array(rows.length)).map(() => []);
    csvSchema.forEach(( { name, defaultValue, transform } ) => {
      for (let i = 0; i < newRows.length; i++) {
        if (defaultValue) {
          newRows[i].push(defaultValue);
        } else {
          const row = rows[i] ?? {};
          const value = row[name];
          newRows[i].push(defTransform(value, row, transform));
        }
      }
    });

    const displayHeaders = csvSchema.map(( { title } ) =>
      this.translate.instant(title, {
        zone: moment().tz(this.settingsService.appSettings.timezoneName).format('Z')
      }));

    if (recordsLimit) {
      newRows.push([`Not fully data was collected to CSV according to exceeded limitation of ${recordsLimit} records,,,,,,,,,`]);
    }

    simulateBrowserDownload([displayHeaders, ...newRows].join('\n'), fileName, FileType.Csv);
  }

  private handleErrors( res: HttpErrorResponse ): Observable<never> {
    if (res.error) {
      this.notifications.error(res.error.message);
    } else {
      this.notifications.error(res.statusText, `Status: ${res.status}`);
    }
    return throwError(res);
  }
}
