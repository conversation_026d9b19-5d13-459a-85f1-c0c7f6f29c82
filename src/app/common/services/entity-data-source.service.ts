import { Injectable } from '@angular/core';
import { SwHubBriefEntity, SwHubEntityDataSource, SwHubShortEntity } from '@skywind-group/lib-swui';
import { Observable, of, ReplaySubject } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { Entity } from '../typings';
import { EntityService } from './entity.service';

@Injectable({
  providedIn: 'root'
})
export class EntityDataSourceService implements SwHubEntityDataSource {
  private readonly wait = new ReplaySubject<boolean>(1);

  constructor( private readonly entityService: EntityService<Entity> ) {
  }

  show() {
    this.wait.next(true);
  }

  hide() {
    this.wait.next(false);
  }

  getEntity(): Observable<SwHubShortEntity | null> {
    return this.wait.pipe(
      switchMap(enable => enable ? this.entityService.getShortStructure('', false, true) : of(null)),
      map(entity => entity as SwHubShortEntity)
    );
  }

  getBrief(): Observable<SwHubBriefEntity | null> {
    return this.wait.pipe(
      switchMap(enable => enable ? this.entityService.getBrief() : of(null)),
      map(entity => entity as SwHubBriefEntity)
    );
  }
}
