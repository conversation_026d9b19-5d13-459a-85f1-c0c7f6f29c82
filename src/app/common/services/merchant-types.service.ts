import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DynamicFormOptionData } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { API_ENDPOINT } from '../../app.constants';

export interface MerchantTypeSchema {
  type: string;
  url?: string;
  schema?: DynamicFormOptionData;
}

function getUrl( uri: string = '', path: string = '' ) {
  return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/merchant-types${uri ? '/' + uri : ''}`;
}

@Injectable()
export class MerchantTypesService {
  public urlGet: string = '/merchant-types';

  constructor( private http: HttpClient ) {
  }

  public fetch( path: string = '' ): Observable<MerchantTypeSchema[]> {
    return this.http.get<MerchantTypeSchema[]>(getUrl('', path));
  }

  public get( id: string, path: string = '' ): Observable<MerchantTypeSchema> {
    return this.http.get<MerchantTypeSchema>(getUrl(id, path));
  }
}
