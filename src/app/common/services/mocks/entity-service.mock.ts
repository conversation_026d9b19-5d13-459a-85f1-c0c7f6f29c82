import { Injectable } from '@angular/core';
import { Entity } from '../../models/entity.model';
import { Observable, of } from 'rxjs';

@Injectable()
export class EntityServiceMock {

  briefEntityMock: Entity;

  constructor() {
  }

  getBrief(): Observable<Entity> {
    if (!this.briefEntityMock) {
      this.createDefaultBriefEntityMock();
    }

    return of(this.briefEntityMock);
  }

  private createDefaultBriefEntityMock() {
    this.briefEntityMock = new Entity({
      type: Entity.TYPE_ENTITY,
      name: 'mock_ENTITY',
      title: 'Mock Entity',
      status: 'normal',
      key: 'Mock-Key-Entity-Please-Ignore',
    });
  }
}
