import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

import { BaseService } from '../base';
import { HttpClient } from '@angular/common/http';
import { map, share } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

const COMMON_LANGUAGES_URL = '/languages';

@Injectable()
export class LanguagesService<T> extends BaseService<T> {
  public urlList: string = COMMON_LANGUAGES_URL;
  public urlGet: string = COMMON_LANGUAGES_URL;
  public urlSave: string = COMMON_LANGUAGES_URL;
  public urlCreate: string = COMMON_LANGUAGES_URL;
  public urlRemove: string = COMMON_LANGUAGES_URL;

  public _item: Subject<T>;
  public _items: Subject<T[]>;

  private baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
    super(notifications, http);
  }

  public getUrl( path? ): string {
    return `${this.baseApiEndpoint}${path ? '/entities/' + path : ''}${this.urlList}`;
  }


  public getList( filter?, path? ): Observable<T[]> {
    console.log('LanguagesService service: get list');

    if (path === ':') path = undefined;
    let fn = this.processRecord;


    let url = this.getUrl(path);

    const source = this.http.get<T[]>(url, {
      params: BaseService.getRequestParams(filter)
    }).pipe(
      map(response => response.map(fn)),
      share()
    );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public processRecord( record ): T {
    record._meta = {};
    // ...
    return record;
  }
}
