import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { BaseService } from '../base';
import { FlatReport } from '../typings/flat-report';

@Injectable()
export class FlatReportsService {

  private urlFlatReports = 'flat-reports';

  constructor( private readonly notifications: SwuiNotificationsService,
               private readonly http: HttpClient
  ) {
  }

  getTheLatestUpdatedFlatReport( path?: string, type = 'ols' ) {
    const url = `${this.getUrl(path)}/${this.urlFlatReports}/${type}`;

    let params = new HttpParams();
    params = params.append('limit', 1).append('sortBy', 'updatedAt').append('sortOrder', 'DESC');

    return this.http
      .get<FlatReport>(url, { params })
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  getUrl( path ): string {
    if (path && path !== ':') {
      return path ? `${BaseService.apiEndpoint}/entities/${path}/` : `${BaseService.apiEndpoint}`;
    } else {
      return `${BaseService.apiEndpoint}`;
    }
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
