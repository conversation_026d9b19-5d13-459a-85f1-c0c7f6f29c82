import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { share } from 'rxjs/operators';
import { API_ENDPOINT } from '../../app.constants';
import { BaseService } from '../base';

@Injectable()
export class SchemaDefinitionsService<T> extends BaseService<T> {
  private readonly url = '/schema-definitions';

  constructor( http: HttpClient, notifications: SwuiNotificationsService ) {
    super(notifications, http);
  }

  public getSchemaDefinitions(): Observable<T[]> {
    const url = `${API_ENDPOINT}${this.url}`;
    const source = this.http.get<T[]>(url).pipe(
      share()
    );
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public getSchemaDefinition( schemaDefinitionId ): Observable<T> {
    const url = `${API_ENDPOINT}${this.url}/${schemaDefinitionId}`;
    const source = this.http.get<T>(url).pipe(
      share()
    );
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }
}
