import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { map, share } from 'rxjs/operators';
import { GameProvider } from '../../pages/games-management/game-provider.model';
import { BaseService } from '../base';
import { BaseApiObject } from '../typings';


@Injectable()
export class GameProviderService<T extends GameProvider & BaseApiObject> extends BaseService<T> {
  private readonly baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( notifications: SwuiNotificationsService, http: HttpClient ) {
    super(notifications, http);
  }

  public getGameProviders( { isTest }: { isTest: boolean } ): Observable<T[]> {
    let params: string = '';
    if (isTest !== undefined) {
      params = `?isTest=${isTest}`;
    }
    return this.http
      .get<T[]>(`${this.baseApiEndpoint}/gameProviders${params}`).pipe(
        map(items => items.map(item => new GameProvider(item)) as T[]),
        share()
      );
  }

  public getAllGameProviders(): Observable<T[]> {
    return this.http
      .get<T[]>(`${this.baseApiEndpoint}/gameProviders`).pipe(
        map(items => items.map(item => new GameProvider(item)) as T[]),
        share()
      );
  }

  public getAvailableGameProviders( path?: string ): Observable<T[]> {
    let url = `${BaseService.apiEndpoint}${path ? '/entities/' + path : ''}/gameproviders/available`;
    return this.http.get<GameProvider[]>(url).pipe(
      map(items => items.map(item => new GameProvider(item)) as T[]),
      share()
    );
  }
}
