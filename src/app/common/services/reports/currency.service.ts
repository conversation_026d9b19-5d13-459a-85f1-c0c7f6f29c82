import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SwuiGridDataService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { API_ENDPOINT } from '../../../app.constants';
import { Entity } from '../../models/entity.model';
import { CurrencyReportItem } from '../../typings';
import { CsvSchema, CsvService } from '../csv.service';
import { getCurrencyLabel, transformFormatCurrencyValue } from '../../core/currecy-transform';

const csvSchema: CsvSchema[] = [
  {
    title: 'REPORT_CURRENCY.GRID.date',
    name: 'date'
  },
  {
    title: 'REPORT_CURRENCY.GRID.currency',
    name: 'currency',
    transform(data: any): string {
      return getCurrencyLabel(data);
    }
  },
  {
    title: 'REPORT_CURRENCY.GRID.playedGames',
    name: 'playedGames'
  },
  {
    title: 'REPORT_CURRENCY.GRID.bets',
    name: 'bets',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    title: 'REPORT_CURRENCY.GRID.betsUsd',
    name: 'betsUsd',
    transform(data: any): string {
      return transformFormatCurrencyValue(data, 'USD');
    }
  },
  {
    title: 'REPORT_CURRENCY.GRID.winnings',
    name: 'winnings',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    title: 'REPORT_CURRENCY.GRID.winningsUsd',
    name: 'winningsUsd',
    transform(data: any): string {
      return transformFormatCurrencyValue(data, 'USD');
    }
  },
  {
    title: 'REPORT_CURRENCY.GRID.GGR',
    name: 'ggr',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    title: 'REPORT_CURRENCY.GRID.ggrUsd',
    name: 'ggrUsd',
    transform(data: any): string {
      return transformFormatCurrencyValue(data, 'USD');
    }
  },
];

function getUrl(path?: string): string {
  return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/report/wallet/currency`;
}

@Injectable()
export class ReportCurrencyService implements SwuiGridDataService<CurrencyReportItem> {
  private _blocked = true;

  constructor(private http: HttpClient,
              private readonly csvService: CsvService,
  ) {
  }

  blockData() {
    this._blocked = true;
  }

  unblockData() {
    this._blocked = false;
  }

  getGridData(params: HttpParams, requestData?: GridRequestData) {
    if (requestData && requestData.type !== Entity.TYPE_ENTITY && !this._blocked) {
      return this.http.get<CurrencyReportItem[]>(`${getUrl(requestData.path)}`, {
        params,
        observe: 'response'
      }).pipe(
        tap(response => this.processTotal(response.body))
      );
    }
    return of(new HttpResponse<CurrencyReportItem[]>({
      body: []
    }));
  }

  public exportPage(data: Record<string, any>[], columns: string[], page: number) {
    const fileName = `Export Currency report ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    this.csvService.exportToCsv(csvSchema, data, fileName, columns);
  }

  private processTotal(data: CurrencyReportItem[]) {
    if (!data.length) {
      return;
    }
    let totalRow = data.pop();
    totalRow.bets = totalRow.betsUsd;
    totalRow.winnings = totalRow.winningsUsd;
    totalRow.ggr = totalRow.ggrUsd;
    totalRow.currency = 'total_usd';

    data.push(totalRow);
    return data;
  }
}
