import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

import { API_ENDPOINT, FORMAT_DATETIME } from '../../../app.constants';
import { handleHttpError } from '../../lib/handle-http-error';
import { Entity } from '../../typings';
import { RgReportItem } from '../../typings/reports/responsible-gaming';
import { CsvSchema, CsvService, transformDate } from '../csv.service';
import { EntityService } from '../entity.service';

const csvSchema = (timezoneName: string, format: string): CsvSchema[] => [
  {
    name: 'playerCode',
    title: 'REPORT_RG.GRID.player_code',
  },
  {
    name: 'suspensionTypes',
    title: 'REPORT_RG.GRID.suspension_type',
  },
  {
    name: 'productType',
    title: 'REPORT_RG.GRID.product_type',
  },
  {
    name: 'createdAt',
    title: 'REPORT_RG.GRID.created_at',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'endTime',
    title: 'REPORT_RG.GRID.end_time',
  },
];

function getUrl( path?: string ): string {
  return `${API_ENDPOINT}${path ? `/entities/${path}` : ''}/responsiblegaming/reports/exclusions`;
}

@Injectable()
export class ReportRgService implements SwuiGridDataService<RgReportItem> {

  constructor( private readonly http: HttpClient,
               private readonly entityService: EntityService<Entity>,
               private readonly notifications: SwuiNotificationsService,
               private readonly csvService: CsvService,
               private readonly setting: SettingsService
  ) {
  }

  getGridData( params: HttpParams, _?: GridRequestData ) {
    return this.getEntityPath(params).pipe(
      switchMap(( { enabled, path } ) => {
        if (!enabled) {
          return of(new HttpResponse<RgReportItem[]>({
            body: []
          }));
        }
        return this.http.get<RgReportItem[]>(getUrl(path), {
          params,
          observe: 'response'
        }).pipe(
          handleHttpError(this.notifications, []),
        );
      }),
    );
  }

  public exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const fileName = `Export Exclusions and Timeouts report ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    this.csvService.exportToCsv(csvSchema(timezoneName, datetimeFormat), data, fileName, columns);
  }

  private getEntityPath( params: HttpParams ): Observable<{ enabled: boolean; path?: string }> {
    if (params.has('path')) {
      return of({
        enabled: true,
        path: params.get('path')
      });
    }
    return this.entityService.getBrief().pipe(
      map(entity => {
        if (entity.type === 'entity') {
          return {
            enabled: false
          };
        }
        return {
          enabled: true,
          path: entity.path
        };
      })
    );
  }
}
