import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, zip } from 'rxjs';
import { catchError, map, mergeMap, share, switchMap } from 'rxjs/operators';

import { BaseService } from '../../base';
import { BaseApiObject, GameHistory, GridFilter, UnfinishedGameHistory } from '../../typings';
import { GameService } from '../game.service';


const DUMMY_PROVIDERS = ['SW', 'PT'];

@Injectable()
export class GameHistoryService<T extends GameHistory & BaseApiObject> extends BaseService<T> {
  public urlList: string = '/history/game/';
  public urlGet: string = '/history/game/';
  public transferOut: string = '/transfer-out';

  constructor( notifications: SwuiNotificationsService,
               public gameService: GameService,
               http: HttpClient,
               public translate: TranslateService,
  ) {
    super(notifications, http);
  }

  public clearList(): void {
    console.log('GameHistoryService: clear list');
    this.requestParams = BaseService.getRequestParams();
    this._httpResponse.next({ limit: 0, offset: 0, total: 0 });
    this._items.next([]);
  }

  public getList( filter?: GridFilter ): Observable<T[]> {
    console.log('GameHistoryService: get list');

    let searchParams = { ...filter };
    searchParams.values = { ...filter.values };
    let { values: { path, historyType = 'general' } } = searchParams;
    delete searchParams.values.historyType;

    let fn = this.processRecord.bind(this);
    this.requestParams = BaseService.getRequestParams(searchParams);

    // Change urlList for getUrl in case unfinished rounds history requested
    const urlGetter = historyType === 'unfinished' ?
      this.getUrl.bind({ urlList: '/history/unfinished/game/' }) :
      this.getUrl.bind(this);

    let url = urlGetter(path);

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        mergeMap(response => {
          let data = response.body;
          this.responseHeaders = this.getResponseParams(response, searchParams, data);
          data = data.map(fn) as T[];
          this._httpResponse.next(this.responseHeaders);
          return this.gameService.getListReplayWithShortData(true).pipe(
            map(games => {
              data.forEach(record => {
                let config = games.find(game => game.id === record.gameCode);
                if (config) {
                  record.gameNameLabel = config.text || '-';
                }
              });
              return data;
            })
          );
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }


  public getExternalGameHistoryList( filter?: GridFilter ): Observable<T[]> {
    console.log('GameHistoryService: get life list');

    let searchParams = { ...filter };
    searchParams.values = { ...filter.values };
    let { values: { path } } = searchParams;
    delete searchParams.values.historyType;

    let fn = this.processRecord.bind(this);
    this.requestParams = BaseService.getRequestParams(searchParams);

    let url = this.getUrl(path, '/history/external');

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          let data = response.body;
          this.responseHeaders = this.getResponseParams(response, searchParams, data);
          data = data.map(fn) as T[];
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public getExternalGameHistoryDetails(
    roundId: number, extTrxId: string, gameProviderCode: string, path: string = '' ): Observable<Object> {
    console.log('GameHistoryService: get life list');

    let filter = {
      values: {
        extTrxId,
        gameProviderCode
      }
    };

    const url = `${this.getUrl(path, '/history/external')}/${roundId}/details`;

    const source = this.http
      .get(url, {
        params: BaseService.getRequestParams(filter)
      }).pipe(
        share()
      );
    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public forceFinish( row: UnfinishedGameHistory, path: string ) {
    console.log('GameHistoryService: force finish round');
    let url = this.getUrl(path);
    url = `${url}${row.roundId}/forceFinish`;
    const { gameContextId, status } = row;
    let paramsObject = {
      gameContextId,
      force: `${['broken', 'brokenIntegration'].indexOf(status) !== -1}`
    };

    let params = new HttpParams({ fromObject: paramsObject });

    const source = this.http
      .post(url, {}, { params })
      .pipe(
        share()
      );

    source.subscribe(
      data => {
        let message: Array<string> = [];
        if (data['result'] === 'force-finished') {
          message = [
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished',
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.noForceFinish',
          ];
        } else {
          message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundClosed');
        }
        message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.note');

        this.translate.get(message).subscribe(mes => {
          let translations = Object.keys(mes).map(key => mes[key]).join('. ');
          this.notifications.success(translations.toString());
        });
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public requireTransferOut( row: UnfinishedGameHistory, path: string ) {
    console.log('GameHistoryService: require transfer out');
    let url = this.getUrl(path);
    url = `${url}${row.roundId}${this.transferOut}`;
    const { gameContextId } = row;
    let paramsObject = {
      gameContextId,
    };

    let params = new HttpParams({ fromObject: paramsObject });

    const source = this.http
      .post(url, {}, { params })
      .pipe(
        share()
      );

    source.subscribe(
      data => {
        console.log('requireTransferOut', data);

        let message: Array<string> = [];
        if (data['result'] === 'finished') {
          message = [
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished',
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.noForceFinish',
          ];
        } else {
          message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundClosed');
        }
        message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.note');

        this.translate.get(message).subscribe(mes => {
          let translations = Object.keys(mes).map(key => mes[key]).join('. ');
          this.notifications.success(translations.toString());
        });
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }


  // export type ForceFinishResult = "force-finished" | "finished";
  // export type RevertResult = "reverted" | "finished";


  public revert( row: UnfinishedGameHistory, path: string ) {
    console.log('GameHistoryService: revert round');

    let url = this.getUrl(path);
    url = `${url}${row.roundId}/revert`;
    const { gameContextId, status } = row;
    let paramsObject = {
      gameContextId,
      force: `${status === 'broken'}`
    };

    let params = new HttpParams({ fromObject: paramsObject });

    const source = this.http
      .post(url, {}, { params }).pipe(
        share()
      );

    source.subscribe(
      data => {
        let message: Array<string>;
        if (data['result'] === 'reverted') {
          message = [
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished',
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.noRevert',
          ];
        } else {
          message = [
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundClosed',
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.betsReverted',
          ];
        }
        message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.note');

        this.translate.get(message).subscribe(mes => {
          let translations = Object.keys(mes).map(key => mes[key]).join('. ');
          this.notifications.success(translations.toString());
        });
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public retryPending( row: UnfinishedGameHistory, path: string ) {
    console.log('GameHistoryService: retry pending round');

    let url = this.getUrl(path);
    url = `${url}${row.roundId}/retry-pending`;
    const { gameContextId } = row;
    let params = new HttpParams({ fromObject: { gameContextId } });

    const source = this.http
      .post(url, {}, { params }).pipe(share());

    source.subscribe(
      data => {
        let message: Array<string> = [];
        if (data['result'] === 'force-finished') {
          message = [
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished',
            'GAMEHISTORY.INTERNAL.NOTIFICATIONS.noRetry',
          ];
        } else {
          message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.retrySuccess');
        }
        message.push('GAMEHISTORY.INTERNAL.NOTIFICATIONS.note');

        this.translate.get(message).subscribe(mes => {
          let translations = Object.keys(mes).map(key => mes[key]).join('. ');
          this.notifications.success(translations.toString());
        });
      },
      err => this.handleErrors.call(this, err)
    );
    return source;
  }


  public processRecord( record: any ): T {
    record.firstTs = record.firstTs !== null ? moment(record.firstTs) : '';
    record.ts = record.ts ? moment(record.ts) : '';
    record.insertedAt = record.insertedAt !== null ? moment(record.insertedAt) : '';
    record.recoveryType = record.recoveryType !== null ? record.recoveryType : null;
    record._meta = {
      firstTs: record.firstTs,
      ts: record.ts,
      insertedAt: record.insertedAt,
      available: record.firstTs.isAfter('2018-01-10'),
    };

    const bet = parseFloat(record.bet) || 0;
    const win = parseFloat(record.win) || 0;
    const revenue = parseFloat(record.revenue) || 0;

    record.outcome = bet > win ? 'LOSE' :
      (bet === win ? (record.recoveryType === 'revert' ? 'VOID' : 'TIE') : 'WIN');
    record.provider = DUMMY_PROVIDERS[Math.floor(Math.random() * DUMMY_PROVIDERS.length)];
    record.agentDomain = record.agentDomain || '';
    record.brandId = record.brandId || 'DEMO';
    record.operatorId = 'Asia ' + Math.round(Math.random() * 100);
    if (bet !== 0) {
      record.ggrPerc = (revenue / bet * 100).toFixed(2);
    }
    return record as T;
  }

  public getSpinListWithDetails( path: string, roundId: string ): Observable<T[]> {
    let url = `${this.getUrl(path)}${roundId}`;

    const source = this.http
      .get<T[]>(url, {
        params: BaseService.getRequestParams({}, { withDetails: true, limit: 10000 }),
      }).pipe(
        share()
      );

    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  getGameHistorySmResult( path: string, roundId: string ): Observable<string> {
    const url = `${this.getUrl(path)}${roundId}`;
    return zip(
      this.http.get<{ ts: string }[]>(url, {
        params: { withDetails: false, sortBy: 'ts', sortOrder: 'ASC', limit: 1 },
      }),
      this.http.get<{ ts: string }[]>(url, {
        params: { withDetails: false, sortBy: 'ts', sortOrder: 'DESC', limit: 1 },
      }),
    ).pipe(
      switchMap(([[{ ts: gte }], [{ ts: lte }]]) => this.http.get(`${url}/sm-result`, {
        params: { ts__gte: gte, ts__lte: lte },
        responseType: 'text'
      })),
      catchError(err => this.handleErrors(err))
    );
  }
}
