import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { map, share } from 'rxjs/operators';

import { GridOrderDirectionEnum } from '../../../app.constants';
import { BaseService } from '../../base';
import { BaseApiObject, GameHistory } from '../../typings';
import { CsvSchema, CsvService } from '../csv.service';
import { getCurrencyLabel, transformFormatCurrencyValue } from '../../core/currecy-transform';

@Injectable()
export class GameHistorySpinService<T extends any & BaseApiObject> extends BaseService<T> {
  public urlList: string = '/history/game/';
  public urlGet: string = '/history/game/';

  public spinList: any;
  public prevInfo: any;
  public nextInfo: any;
  public path: string;
  public paging: {
    total?: number, offset?: number, limit?: number,
    ordering?: { string: GridOrderDirectionEnum }, values?: any
  } = {};

  private prevPageData: any;
  private nextPageData: any[];

  constructor( notifications: SwuiNotificationsService,
               http: HttpClient,
               private csvService: CsvService
  ) {
    super(notifications, http);
  }

  public getList( roundId: number, filter?: any ): Observable<T[]> {
    const { values: { path } } = filter;
    let fn = this.processRecord.bind(this);
    this.requestParams = BaseService.getRequestParams(filter, { limit: 10000 });
    let url = this.getUrl(path) + roundId;

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          const data = response.body.map(fn) as T[];
          if (response.headers.has('x-paging-total')) {
            this.paging.total = parseInt(response.headers.get('x-paging-total'), 10);
          }
          if (response.headers.has('x-paging-offset')) {
            this.paging.offset = parseInt(response.headers.get('x-paging-offset'), 10);
          }
          if (response.headers.has('x-paging-limit')) {
            this.paging.limit = parseInt(response.headers.get('x-paging-limit'), 10);
          }

          this.responseHeaders = this.getResponseParams(response, filter, data);
          this._httpResponse.next(this.responseHeaders);
          this.spinList = data;

          this.prevPageData = undefined;
          this.nextPageData = undefined;

          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public getListPure( roundId: number, filter?: any ): Observable<T[]> {
    let fn = this.processRecord.bind(this);
    let { values: { path } } = filter;
    let url = this.getUrl(path) + roundId;

    return this.http
      .get<T[]>(url, {
        params: BaseService.getRequestParams(filter)
      }).pipe(
        map(response => response.map(fn) as T[]),
        share()
      );
  }

  public downloadCSV( roundInfo: GameHistory, forcePath: string, hasFreeBet = false ): Observable<any> {
    const schemas: CsvSchema[] = [
      { name: 'spinNumber', title: 'GAMEHISTORY.GAME.spin' },
      { name: 'type', title: 'GAMEHISTORY.GAME.type' },
      { name: 'currency', title: 'GAMEHISTORY.GAME.currency', transform(data: string): string {
        return getCurrencyLabel(data);
        }
      },
      { name: 'bet', title: 'GAMEHISTORY.GAME.bet', transform(data: any, rows: Record<string, any>): string {
        return transformFormatCurrencyValue(data, rows.currency);
        }
      },
      { name: 'win', title: 'GAMEHISTORY.GAME.win', transform(data: any, rows: Record<string, any>): string {
          return transformFormatCurrencyValue(data, rows.currency);
        }
      },
      ...(hasFreeBet ? [{ name: 'freeBetCoin', title: 'PROMO.EDIT.QUALIFYING_GAMES.freebetCoinValue' }] : []),
      { name: 'ts', title: 'GAMEHISTORY.GAME.date' },
      { name: 'gameNameLabel', title: 'GAMEHISTORY.GRID.gameNameLabel', defaultValue: roundInfo.gameNameLabel },
      { name: 'gameCode', title: 'GAMEHISTORY.GRID.gameCode' },
      { name: 'roundId', title: 'GAMEHISTORY.GRID.roundId', defaultValue: String(roundInfo.roundId) },
      { name: 'playerCode', title: 'GAMEHISTORY.GRID.playerCode', defaultValue: roundInfo.playerCode },
    ];
    return this.csvService.download(
      path => this.getUrl(path) + roundInfo.roundId,
      schemas,
      `${(roundInfo.gameNameLabel)} spins list of round ${(roundInfo.roundId)}`,
      undefined,
      forcePath
    );
  }

  public getItem( { path, roundId, spin, spinNumber }: any ): Observable<T> {
    let fn = this.processRecord.bind(this);

    let url = this.getUrl(path);

    const res = this.http
      .get<T>(`${url}${roundId}/details/${spinNumber}`, { observe: 'response' }).pipe(
        map(response => {
          this.responseHeaders = this.getResponseParams(response);
          let data = Object.assign({}, spin, response.body);
          data = fn(data) as T;
          return data;
        }),
        share()
      );

    res.subscribe(
      data => {
        if (this.paging.total && data.spinNumber < this.paging.total - 1) {
          this.prepareNextData(roundId, data, path);
        } else {
          this.nextInfo = null;
        }

        if (data.spinNumber > 0) {
          this.preparePrevData(spinNumber, roundId, data, path);

        } else {
          this.prevInfo = null;
        }

        this._item.next(data);
      },
      err => this.handleErrors.call(this, err)
    );

    return res;
  }

  public getNext(): Observable<T> {
    this._item.next();

    return this.getItem(this.nextInfo);
  }

  public getPrev(): Observable<T> {
    this._item.next();

    return this.getItem(this.prevInfo);
  }

  public processRecord( record: T ): T {
    // @ts-ignore
    record._meta = {};
    // @ts-ignore
    if (record.details && record.details.state) {
      // @ts-ignore
      record.isFree = record.details.state.initialFreeSpinWin < record.details.state.initialFreeSpinsCount;
      // @ts-ignore
      record.freeSpinNumber = record.details.state.totalFreeSpinsCount - record.details.state.freeSpinsCount;
    }

    return record;
  }

  private preparePrevData( spinNumber: number, roundId: number, data: any, path: string ) {
    const prevIndex = spinNumber - 1;
    const filter = { values: { path: path, spinNumber: prevIndex } };

    this.getListPure(roundId, filter).subscribe(prevPageData => {
      this.prevPageData = prevPageData;

      let prevData = null;
      if (this.prevPageData && Array.isArray(this.prevPageData)) {
        prevData = this.prevPageData[0];
      }

      this.setPrevInfo(prevData, roundId, data, path, prevIndex);

      this._item.next(data);
    });
  }

  private prepareNextData( roundId: number, data: any, path: string ) {
    const nextIndex = data.spinNumber + 1;
    const filter = { values: { path: path, spinNumber: nextIndex } };

    this.getListPure(roundId, filter).subscribe(nextPageData => {
      this.nextPageData = nextPageData;

      let nextData = null;
      if (this.nextPageData && Array.isArray(this.nextPageData)) {
        nextData = this.nextPageData[0];
      }

      this.setNextInfo(nextData, roundId, data, path, nextIndex);

      this._item.next(data);
    });
  }

  private setPrevInfo( prevData: any, roundId: number, data: any, path: string, prevIndex: number ) {
    data.hasPrev = true;
    data.prevType = prevData && prevData.type;
    this.prevInfo = { path, roundId, spin: prevData, spinNumber: prevIndex };
  }

  private setNextInfo( nextData: any, roundId: number, data: any, path: string, nextIndex: number ) {
    data.hasNext = true;
    data.nextType = nextData && nextData.type;
    this.nextInfo = { path, roundId, spin: nextData, spinNumber: nextIndex };
  }
}
