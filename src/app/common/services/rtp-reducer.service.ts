import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { API_ENDPOINT } from '../../app.constants';
import { RtpDeduction, RtpGameConfiguration, RtpReducer } from '../typings/rtp-reducer';


function getUrl( path?: string, urlList: string = '/rtp-report' ): string {
  return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}${urlList}`;
}

@Injectable()
export class RtpReducerService implements SwuiGridDataService<RtpReducer> {
  private rtpDeduction = '/games/rtp-deduction';

  constructor( public http: HttpClient,
               public notifications: SwuiNotificationsService
  ) {
  }

  getGridData( params: HttpParams, data: GridRequestData ): Observable<HttpResponse<RtpReducer[]>> {
    const { path } = data;

    return this.http.get<RtpReducer[]>(getUrl(path), {
      params,
      observe: 'response'
    }).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error.error.message);
        return throwError(error);
      })
    );
  }

  changeGamesRtp( path: string, gamesRtp: RtpDeduction[] ) {
    return this.http.put<RtpGameConfiguration[]>(getUrl(path, this.rtpDeduction), gamesRtp)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error.error.message);
          return throwError(error);
        })
      );
  }
}
