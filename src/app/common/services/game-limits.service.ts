import { HttpClient, HttpEvent, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, GridRequestData } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { map, share } from 'rxjs/operators';
import { BaseService } from '../base';
import { GameLimits } from '../typings/game-limits';

@Injectable()
export class GameLimitsService implements GridDataService<GameLimits> {
  public urlGameGroups = 'gamegroups/';

  constructor( private readonly http: HttpClient ) {
  }

  public getGridData( params: HttpParams | any, data?: GridRequestData ): Observable<HttpResponse<GameLimits[]>> {
    let path: string;
    if (typeof data !== 'undefined' && data.hasOwnProperty('path')) {
      path = data['path'];
    } else if (params.has('path')) {
      path = params.get('path');
    }

    let url = `${this.getUrl(path)}/${this.urlGameGroups}limits`;

    return this.http.get<GameLimits[]>(url, {
      params,
      observe: 'response',
    }).pipe(
      map(( response: HttpResponse<GameLimits[]> ) => {
        let newEvent: HttpEvent<any>;
        newEvent = response.clone({
          body: this.proc(response.body, params)
        });
        return newEvent;
      }),
      share()
    );
  }

  public getUrl( path?: string ): string {
    if (path && path !== ':') {
      return path ? `${BaseService.apiEndpoint}/entities/${path}/` : `${BaseService.apiEndpoint}`;
    } else {
      return `${BaseService.apiEndpoint}`;
    }
  }

  private proc( response: GameLimits[], params: HttpParams ) {
    const game = params.get('game');
    const gameGroup = params.get('gameGroup');
    if (game && !gameGroup) {
      response = response.reduce(( acc: GameLimits[], cur: GameLimits ) => {
        if (cur.game.code === game) {
          acc.push(cur);
        }
        return acc;
      }, []);
    } else if (gameGroup && !game) {
      response = response.reduce(( acc: GameLimits[], cur: GameLimits ) => {
        if (cur.gamegroup.name === gameGroup) {
          acc.push(cur);
        }
        return acc;
      }, []);
    } else if (game && gameGroup) {
      response = response.reduce(( acc: GameLimits[], cur: GameLimits ) => {
        if (cur.gamegroup.name === gameGroup && cur.game.code === game) {
          acc.push(cur);
        }
        return acc;
      }, []);
    }

    return response;
  }
}
