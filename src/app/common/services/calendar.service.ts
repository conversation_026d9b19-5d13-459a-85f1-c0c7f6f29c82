import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface CalendarDetector {
  symbol?: Symbol;
  isOpen?: boolean;
}

@Injectable()
export class CalendarService {
  private _calendarsArray: Symbol[];
  private _observableCalendars: BehaviorSubject<Symbol[]>;

  constructor() {
    this._calendarsArray = new Array<Symbol>();
    this._observableCalendars = <BehaviorSubject<Symbol[]>>new BehaviorSubject([]);
  }

  get calendars() {
    return this._observableCalendars.asObservable();
  }

  addCalendar(calendar: Symbol) {
    this._calendarsArray = [...this._calendarsArray, ...[calendar]];
    this._observableCalendars.next(this._calendarsArray);
  }

  removeCalendar(calendar: Symbol) {
    this._calendarsArray = this._calendarsArray.filter(val => val !== calendar);
    this._observableCalendars.next(this._calendarsArray);
  }
}
