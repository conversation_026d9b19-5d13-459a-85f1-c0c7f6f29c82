import { Injectable } from '@angular/core';
import { LobbiesBuild, LobbyExtendedData } from '../../pages/lobby/lobby.model';
import { Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { catchError } from 'rxjs/operators';

const URL = '/api/v1/lobby-build';

export interface QueryParams {
  id?: string;
  path?: string;
}

@Injectable()
export class LobbyBuildService {

  constructor( private readonly http: HttpClient ) {
  }

  query( { id, path }: QueryParams ): Observable<LobbiesBuild> {
    return this.http.get<LobbiesBuild>(`${URL}/build${id ? `/${id}` : ''}`, { params: path ? { path } : {} }).pipe(
      catchError(err => {
        console.error(err);
        return of({});
      })
    );
  }

  build( { id }: LobbyExtendedData, path?: string ): Observable<any> {
    return this.http.post(`${URL}/build`, { id }, {
      params: path ? { path } : {},
      responseType: 'text'
    }).pipe(
      catchError(err => {
        console.error(err);
        return of('OK');
      })
    );
  }

  delete( id: string, path?: string ): Observable<any> {
    return this.http.delete(`${URL}/build/${id}`, { params: path ? { path } : {}, responseType: 'text' }).pipe(
      catchError(err => {
        console.error(err);
        return of('OK');
      })
    );
  }
}
