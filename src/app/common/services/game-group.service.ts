import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, GridRequestData, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, of, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { BaseService } from '../base';
import { GameGroup } from '../models/game-group.model';
import { GameLimits } from '../typings/game-limits';

@Injectable()
export class GameGroupService implements GridDataService<GameGroup> {
  public urlGameGroups = 'gamegroups/';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ): Observable<HttpResponse<GameGroup[]>> {
    let path = typeof requestData !== 'undefined' && requestData.hasOwnProperty('path') ?
      requestData['path'] : '';

    let url = `${this.getUrl(path)}/${this.urlGameGroups}`;

    return this.http
      .get<GameGroup[]>(url, {
        params,
        observe: 'response'
      }).pipe(
        catchError(err => this.handleError(err)),
        share()
      );
  }

  public getUrl( path ): string {
    if (path && path !== ':') {
      return path ? `${BaseService.apiEndpoint}/entities/${path}/` : `${BaseService.apiEndpoint}`;
    } else {
      return `${BaseService.apiEndpoint}`;
    }
  }

  public getEntityGameGroups( path: string ): Observable<GameGroup[]> {
    let url = this.getUrl(path) + this.urlGameGroups;

    return this.http.get<GameGroup[]>(url).pipe(
      share()
    );
  }

  public updateGameLimits( body, path, gameGroup, gameCode, isGameGroupContainsGame ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}${gameGroup}/games/${gameCode}`;

    const method = isGameGroupContainsGame ? 'PATCH' : 'PUT';

    return this.http
      .request(method, url, { body })
      .pipe(
        share()
      );
  }

  public getGameLimits( path, gameGroup, gameCode ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}${gameGroup}/games/${gameCode}`;

    return this.http.get(url).pipe(
      catchError(err => this.handleError(err)),
      share()
    );
  }

  public getGameGroupsLimits( path, params ): Observable<HttpResponse<GameLimits[]>> {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}limits`;
    params['isCustomLimitsSupported'] = true;

    return this.http.get<GameLimits[]>(url, {
      observe: 'response',
      params: new HttpParams({ fromObject: params })
    }).pipe(
      share()
    );
  }

  removeGameFromGameGroup( path, gameGroup, gameCode ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}${gameGroup}/games/${gameCode}`;

    return this.http.delete(url).pipe(
      catchError(err => this.handleError(err)),
      share(),
    );
  }

  handleError( error ) {
    if (error.error && error.error.code === 214) {
      return of(null);
    } else {
      if (error.error) {
        this.notifications.error(error.error.message);
      } else {
        this.notifications.error(error.statusText, `Status: ${error.status}`);
      }

      return throwError(error);
    }
  }

  getGameGroupsList( path: string ): Observable<GameGroup[]> {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}`;

    return this.http.get<GameGroup[]>(url, {}).pipe(
      share()
    );
  }

  createGameGroup( body, path ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}`;

    return this.http.post(url, body)
      .pipe(
        catchError(err => this.handleError(err))
      );
  }

  editGameGroup( body, path, gameGroup ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}/${gameGroup}`;

    return this.http.patch(url, body)
      .pipe(
        catchError(err => this.handleError(err))
      );
  }

  removeGameGroup( path, gameGroup, isForceDelete ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}/${gameGroup}`;
    const params = new HttpParams().set('force', `${isForceDelete}`);

    return this.http.delete(url, { params }).pipe(
      catchError(err => this.handleError(err)),
      share(),
    );
  }

  getGameGroupGames( path, gameGroup ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}/${gameGroup}`;

    return this.http.get(url).pipe(
      catchError(err => this.handleError(err)),
      share(),
    );
  }

  setDefaultGameGroup( path, gameGroup ) {
    let url = `${this.getUrl(path)}/${this.urlGameGroups}${gameGroup}/default`;

    return this.http.put(url, null).pipe(
      catchError(err => this.handleError(err)),
      share()
    );
  }
}
