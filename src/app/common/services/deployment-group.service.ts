import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { API_ENDPOINT } from '../../app.constants';
import { DeploymentGroup } from '../typings/deployment-group';

@Injectable()
export class DeploymentGroupService {

  public urlDeploymentGroups = '/deployment-groups/';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
  }

  getDeploymentGroups(): Observable<DeploymentGroup[]> {
    return this.http.get<DeploymentGroup[]>(this.getUrl()).pipe(
      catchError(this.handleErrors),
      share()
    );
  }

  setDeploymentGroup( path: string, deploymentGroupRoute: string ) {
    let url = `${this.getUrl(path)}${deploymentGroupRoute}`;

    return this.http.put(url, {})
      .pipe(
        catchError(err => this.handleErrors(err))
      );
  }

  detachDeploymentGroup( path: string ) {
    let url = `${this.getUrl(path)}`;

    return this.http.delete(url)
      .pipe(
        catchError(err => this.handleErrors(err)),
      );
  }

  getUrl( path?: string ): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}${this.urlDeploymentGroups}`;
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
