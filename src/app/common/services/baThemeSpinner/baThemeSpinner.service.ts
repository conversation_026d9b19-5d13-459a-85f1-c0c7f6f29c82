import { Injectable } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { filter, take, tap } from 'rxjs/operators';

@Injectable()
export class BaThemeSpinner {

  private _selector: string = 'preloader';
  private _element: HTMLElement;

  constructor(private readonly route: ActivatedRoute) {
    this._element = document.getElementById(this._selector);
  }

  public show(): void {
    this.route.queryParams.pipe(
      filter((data: Params) => !Object.keys(data).length),
      tap(() => this._element.style['display'] = 'block'),
      take(1)
    ).subscribe();
  }

  public hide( delay: number = 0 ): void {
    setTimeout(() => {
      this._element.style['display'] = 'none';
    }, delay);
  }
}
