import { Injectable } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Observable, of } from 'rxjs';
import { first, map, tap } from 'rxjs/operators';

import { Entity } from '../models/entity.model';
import { MenuItem, RouterMenuItem } from '../models/menu-item.model';
import { EntityService } from './entity.service';

function getPermissions( { permissions }: { permissions?: string[] }, url: string ): string[] {
  if (permissions) {
    return permissions;
  }
  const key = url.replace(/^\/?\/pages\//, '').replace(/([\/\-])/g, ':');
  return [key, `entity:${key}`, `keyentity:${key}`];
}

@Injectable()
export class MenuSidebarService {

  private brief: Entity;

  constructor( private authService: SwHubAuthService,
               private entityService: EntityService<Entity>
  ) {
  }

  getMenu( menu: RouterMenuItem[] ): Observable<MenuItem[]> {
    return this.getBrief()
      .pipe(
        map(( briefData ) => {
          let items = menu.map(item => new MenuItem(item));
          items = this.filterHidden(items[0].children);
          return this.getMenuItems(items, briefData);
        })
      );
  }

  private getMenuItems( menuItems: MenuItem[], briefData ) {
    let entityType = briefData.type;
    return menuItems.filter(( item: MenuItem ) => {
        let filtered = item.permissionsList && Array.isArray(item.permissionsList)
          ? item.permissionsList.every(permissionsItem => Array.isArray(permissionsItem) && this.authService.areGranted(permissionsItem))
          : this.authService.areGranted(getPermissions(item, item.paths.join('/')));
        if (filtered && item.isBrandOnly) {
          filtered = entityType === 'brand';
        }
        if (item.children && item.children.length) {
          item.children = this.getMenuItems(item.children, entityType);
        }
        if (item.isSuperadmin) {
          filtered = this.authService.isSuperAdmin;
        }
        return filtered;
      }
    );
  }

  private filterHidden( arr: MenuItem[] ): MenuItem[] {
    return arr.filter(( item: MenuItem ) => {
      if (item.children) {
        item.children = this.filterHidden(item.children);
      }
      return item.menu ? !item.menu.hide : item;
    });
  }


  private getBrief(): Observable<any> {
    if (this.brief) {
      return of(this.brief).pipe(
        first()
      );
    } else {
      return this.entityService.getBrief()
        .pipe(
          first(),
          tap(brief => this.brief = brief)
        );
    }
  }
}
