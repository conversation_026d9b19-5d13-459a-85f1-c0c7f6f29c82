import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { BaseService } from '../base';

@Injectable()
export class PromotionLabelsService {

  public urlPromotionLabels = 'promo-labels';

  constructor( private notifications: SwuiNotificationsService,
               private http: HttpClient,
  ) {
  }

  updatePromotionLabels( path, promoId, body ) {
    let url = `${this.getUrl(path)}/promo/${promoId}/${this.urlPromotionLabels}`;

    return this.http
      .put(url, body)
      .pipe(
        catchError(err => this.handleErrors(err)),
        share(),
      );
  }

  getUrl( path ): string {
    if (path && path !== ':') {
      return path ? `${BaseService.apiEndpoint}/entities/${path}/` : `${BaseService.apiEndpoint}`;
    } else {
      return `${BaseService.apiEndpoint}`;
    }
  }

  handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}
