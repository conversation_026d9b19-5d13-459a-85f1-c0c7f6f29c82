import { Injectable } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';
import isEmail from 'validator/lib/isEmail';
import * as isCidr from 'is-cidr';

const getControlFromGroup = ( controlName, group ): FormControl => {
  let control;
  if (controlName.indexOf('.') > -1) {
    let name: string[] = controlName.split('.');
    if (name.length === 2) {
      control = (group.controls[name[0]] as FormGroup).controls[name[1]];
    } else {
      throw new Error(`Unsupported control name ${controlName}`);
    }
  } else {
    control = group.controls[controlName];
  }

  return control;
};

const buildMessageForCompareFirstValueWithSecondValue = ( validatorName: string, val: ValidationErrors ) => {
  if (validatorName !== 'compareFirstValueWithSecondValue') return;

  let firstControl = val.firstControlName;
  let secondControl = val.secondControlName;

  const types = {
    en: {
      'lte': 'lower or equal',
      'lt': 'lower',
      'gt': 'greater',
      'gte': 'greater or equal',
      'eq': 'equal',
      'neq': 'not equal',
    }
  };

  if ('controlDisplayNames' in val) {
    const { controlDisplayNames } = val;
    if (firstControl in controlDisplayNames) {
      firstControl = controlDisplayNames[firstControl];
    }
    if (secondControl in controlDisplayNames) {
      secondControl = controlDisplayNames[secondControl];
    }
  }
  return `${firstControl} should be ${types['en'][val.comparisonType]} than ${secondControl}`;
};

export const isOptionalUrl = ( url: any ): boolean => {
  if (typeof url !== 'string') {
    return false;
  }
  if (url.trim() === '') {
    return true;
  }
  if (/[\s<>]/.test(url)) {
    return false;
  }

  let split = url.split('#', 2);
  url = split.shift();
  split = url.split('?', 2);
  url = split.shift();
  split = url.split('://', 2);
  if (split.length <= 1) {
    return false;
  }
  const protocol = split.shift().toLowerCase();
  if (['http', 'https'].indexOf(protocol) === -1) {
    return false;
  }
  url = split.join('://');
  if (url === '') {
    return false;
  }

  split = url.split('/', 2);
  url = split.shift();
  let host;
  let port;
  let portValue = null;
  const ipv6Match = url.match(/^\[([^\]]+)\](?::([0-9]+))?$/);
  if (ipv6Match) {
    host = '';
    portValue = ipv6Match[2] || null;
  } else {
    split = url.split(':');
    host = split.shift();
    if (split.length) {
      portValue = split.join(':');
    }
  }

  if (portValue !== null && portValue.length > 0) {
    port = parseInt(portValue, 10);
    if (!/^[0-9]+$/.test(portValue) || port <= 0 || port > 65535) {
      return false;
    }
  }

  if (ipv6Match) {
    return true;
  }

  const parts = host.split('.');
  const tld = parts[parts.length - 1];
  // disallow spaces
  if (/\s/.test(tld)) {
    return false;
  }

  return parts.every(( part ) => {
    if (part.length > 63) {
      return false;
    }
    if (!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(part)) {
      return false;
    }
    // disallow full-width chars
    if (/[\uff01-\uff5e]/.test(part)) {
      return false;
    }
    // disallow parts starting or ending with hyphen
    return !/^-|-$/.test(part);
  });
};


export type ValidationComparisonType = 'lte' | 'lt' | 'eq' | 'neq' | 'gt' | 'gte';

export const comparisonTypes: { [name: string]: ValidationComparisonType } = {
  lte: 'lte', lt: 'lt', eq: 'eq', neq: 'neq', gt: 'gt', gte: 'gte'
};

export const comparisonTypeToComparisonFn = {
  [comparisonTypes.lte]: ( a, b ) => parseFloat(a) <= parseFloat(b),
  [comparisonTypes.lt]: ( a, b ) => parseFloat(a) < parseFloat(b),
  [comparisonTypes.eq]: ( a, b ) => parseFloat(a) === parseFloat(b),
  [comparisonTypes.neq]: ( a, b ) => parseFloat(a) !== parseFloat(b),
  [comparisonTypes.gt]: ( a, b ) => parseFloat(a) > parseFloat(b),
  [comparisonTypes.gte]: ( a, b ) => parseFloat(a) >= parseFloat(b),
};


@Injectable()
export class ValidationService {
  static getValidatorErrorMessage( validatorName: string,
                                   validatorValue?: ValidationErrors | null,
                                   configOverride?: { [key: string]: string },
  ) {
    // tslint:disable:max-line-length
    let config = {
      'required': 'VALIDATION.required',
      'coeffsRequired': 'VALIDATION.coeffsRequired',
      'invalidCreditCard': 'VALIDATION.invalidCreditCard',
      'invalidEmailAddress': 'VALIDATION.invalidEmailAddress',
      'invalidPassword': 'VALIDATION.invalidPassword',
      'min': `VALIDATION.min`,
      'max': `VALIDATION.max`,
      'minLength': `VALIDATION.minLength`,
      'maxLength': `VALIDATION.maxLength`,
      'invalidDomain': 'VALIDATION.invalidDomain',
      'invalidIPv4Address': 'VALIDATION.invalidIPv4Address',
      'invalidIPv4AddressMask': 'VALIDATION.invalidIPv4AddressMask',
      'invalidPhoneNumberMask': 'VALIDATION.invalidPhoneNumberMask',
      'invalidDigitsOnly': 'VALIDATION.invalidDigitsOnly',
      'invalidNumbersOnly': 'VALIDATION.invalidNumbersOnly',
      'invalidIssueId': 'VALIDATION.invalidIssueId',
      'notEquals': `VALIDATION.notEquals`,
      'notEqualsString': `VALIDATION.notEqualsString`,
      'notEqualsPassword': 'VALIDATION.notEqualsPassword',
      'passwordNotEquals': 'VALIDATION.passwordNotEquals',
      'passwordMinLength': 'VALIDATION.passwordMinLength',
      'passwordContainDigit': `VALIDATION.passwordContainDigit`,
      'passwordContainLowercase': `VALIDATION.passwordContainLowercase`,
      'passwordContainUppercase': `VALIDATION.passwordContainUppercase`,
      'invalidLatinCharsDigitsSymbols': `VALIDATION.invalidLatinCharsDigitsSymbols`,
      'JSON': `VALIDATION.JSON`,
      'minLengthArray': `VALIDATION.minLengthArray`,
      'dateGreaterThan': `VALIDATION.dateGreaterThan`,
      'dateGreaterThanNow': 'VALIDATION.dateGreaterThanNow',
      'startAndEndDate': 'VALIDATION.startAndEndDate',
      'domainHttp': 'VALIDATION.domainHttp',
      'domainUrlParts': 'VALIDATION.domainUrlParts',
      'urlIsNotCorrect': 'VALIDATION.urlIsNotCorrect',
      'urlFormat': 'ALL.urlFormat',
      'validateFirstIfSecondIsSetWithValue': `Field "${validatorValue.firstControlName}" is required`,
      'validateFirstMaxValueIfSecondSetWithValue': 'VALIDATION.validateFirstMaxValueIfSecondSetWithValue',
      'compareFirstValueWithSecondValue': buildMessageForCompareFirstValueWithSecondValue(validatorName, validatorValue),
      'atLeastOneItemRequired': `At least one of these values is required: ${validatorValue.itemsString}`,
      'fileFormatNotSupported': 'VALIDATION.fileFormatNotSupported',
      'spacesAreNotAllowed': 'VALIDATION.spacesAreNotAllowed',
      'invalidColorHexFormat': 'VALIDATION.invalidColorHexFormat',
      'invalidCustomerId': 'VALIDATION.invalidCustomerId',
      'invalidFormat': 'VALIDATION.invalidFormat',
      'aamsCodeError': 'VALIDATION.aamsCodeError',
      'passwordNotMatch': 'VALIDATION.passwordNotMatch',
      'valuesAreNotTheSame': 'VALIDATION.valuesAreNotTheSame',
      'atLeastOneFieldShouldBeRequired': `One of these fields are required: ${validatorValue.fields}`,
      'firstShouldBeMoreThanSecondAndLessThanThird': `${validatorValue.firstDisplayName} value should be more than ` +
        `${validatorValue.secondDisplayName} and less than ${validatorValue.thirdDisplayName}`,
      'minLowerThanMax': 'VALIDATION.minLowerThanMax',
      'lowerThan': 'VALIDATION.lowerThan',
      'greaterThan': 'VALIDATION.greaterThan',
      'oneOrNoneOfFieldsShouldBeSpecified': `Only one of ${validatorValue.fieldsDisplayNames} fields should be specified (or none of them)`,
      'greaterOrEqual': 'VALIDATION.greaterOrEqual',
      'valueShouldBeUnique': 'VALIDATION.valueShouldBeUnique',
      ...configOverride
    };
    // tslint:enable:max-line-length

    return config[validatorName];
  }

  static creditCardValidator( control ) {
    // Visa, MasterCard, American Express, Diners Club, Discover, JCB
    // tslint ignore:line
    const regexp = new RegExp('^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47]' +
      '[0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11})$');
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidCreditCard': true };
    }
  }

  static emailValidator( control ) {
    const { value } = control;
    if (!control.value || isEmail(value)) {
      return null;
    } else {
      return { 'invalidEmailAddress': true };
    }
  }

  static passwordValidator( control ) {
    // (?=.*\d)                should contain at least one digit
    // (?=.*[a-z])             should contain at least one latin lower case
    // (?=.*[A-Z])             should contain at least one latin upper case
    // [a-zA-Z0-9]{8,}         should contain at least 8 from the mentioned characters
    if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)^[a-zA-Z0-9!@#$%^&*()_~\-`\\/"'+|\[\]}{:;?>.<,]{8,255}$/.test(control.value)) {
      return null;
    } else {
      return { 'invalidPassword': true };
    }
  }

  static passwordConditions( min: number = 1 ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let errors: any = {};

      if (control.value.length < 8) {
        errors.passwordMinLength = true;
      }

      if (control.value.replace(/\D+/g, '').length < min) {
        errors.passwordContainDigit = { value: min };
      }

      if (control.value.replace(/[^a-z]/g, '').length < min) {
        errors.passwordContainLowercase = { value: min };
      }

      if (control.value.replace(/[^A-Z]/g, '').length < min) {
        errors.passwordContainUppercase = { value: min };
      }

      if (!Object.keys(errors).length) {
        errors = null;
      }

      return errors;
    };
  }

  static passwordEqual( equalsTo ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      const password1 = control;
      const password2 = control.root.get(equalsTo);

      if (password1 && password2 && password1.value !== password2.value) {
        return { 'passwordNotEquals': true };
      }

      return null;
    };
  }

  static domainValidator( control ) {
    const regexp = /^(?!:\/\/)([a-zA-Z0-9-]+\.){0,5}[a-zA-Z0-9-][a-zA-Z0-9-]+\.[a-zA-Z]{2,64}?$/gi;
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidDomain': true };
    }
  }

  static ipv4AddressValidator( control ) {
    const regexp = /\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b/;
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidIPv4Address': true };
    }
  }

  static issueIdValidator( control: AbstractControl ): ValidationErrors | null {
    const regexp = new RegExp('[A-Z][A-Z\\d]+-\\d+');
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidIssueId': true };
    }
  }

  static ipv4AddressMaskListValidator( control ) {
    // see sandbox https://regex101.com/r/Nt3D75/2
    /* tslint:disable */
    const regexp = /^((?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\*)\.|)){3}(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\*)$/m;
    /* tslint:enable */
    if (control.value) {
      const addressList = control.value.trim().split('\n');
      if (addressList && addressList.length) {
        const valid = addressList.every(( value ) => isCidr.v4(value) ? true : value.match(regexp));
        if (!valid) {
          return { 'invalidIPv4AddressMask': true };
        }
      }
    }
    return null;
  }

  static phoneNumberValidator( control ) {
    const regexp = /^[\\+]?[0-9]{4,18}$/;
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidPhoneNumberMask': true };
    }
  }

  static digitsOnlyValidator( control ) {
    const regexp = /^(\d+)$/;

    const value = control.value === null ? '' : control.value?.toString();
    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'invalidDigitsOnly': true };
    }
  }

  static numbersOnlyValidator( control ) {
    const regexp = /^\d+(\.\d{1,3})?$/; // example 10.142

    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'invalidNumbersOnly': true };
    }
  }

  static latinCharsDigitsSymbols( control ) {
    const regexp = /^([a-z0-9\-\_:]+)$/gi;

    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidLatinCharsDigitsSymbols': true };
    }
  }

  static notEquals( restrictedNumber: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return control.value === restrictedNumber ? { 'notEquals': { value: control.value } } : null;
    };
  }

  static notEqualsString( restrictedString: string ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return control.value === restrictedString ? { 'notEqualsString': { value: control.value } } : null;
    };
  }

  static notEqualsPassword( restrictedPassword: string ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return control.value === restrictedPassword ? { 'notEqualsPassword': { value: control.value } } : null;
    };
  }

  static JSONValidator( control ) {
    let error: Object | null = null;
    try {
      JSON.parse(control.value);
    } catch (e) {
      error = { 'JSON': { error: e.message } };
    }
    return error;
  }

  static IfNotEmpty( validator: Function ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let error = null;

      if (control.value && control.value !== '') {
        error = validator(control);
      }

      return error;
    };
  }

  static minLengthArray( min: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      if (control.value.length >= min) {
        return null;
      }

      return { 'minLengthArray': { valid: false, min } };
    };
  }

  static minLength( min: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let letterCount = 0;
      if (control.value) {
        letterCount = control.value.toString().replace(/\s+/g, '').length;
      }
      if (letterCount >= min) {
        return null;
      }
      return { 'minLength': { valid: false, min } };
    };
  }

  static maxLength( max: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let letterCount = 0;
      if (control.value) {
        letterCount = control.value.toString().replace(/\s+/g, '').length;
      }
      if (letterCount <= max) {
        return null;
      }
      return { 'maxLength': { valid: false, max } };
    };
  }

  static dateGreaterThan( date: Date ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {

      const inputDate = new Date(control.value);

      if (inputDate.getTime() > date.getTime()) {
        return null;
      }

      return { 'dateGreaterThan': { valid: false, date } };
    };
  }

  static startAndEndDate( startDateControlName: string, endDateControlDate: string ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {

      const startDate = new Date(group.controls[startDateControlName].value);
      const endDate = new Date(group.controls[endDateControlDate].value);

      if (startDate.getTime() < endDate.getTime()) {
        return null;
      }

      return { 'startAndEndDate': { valid: false } };
    };
  }

  static validateFirstIfSecondIsSetWithValue( firstControlName: string, secondControlName: string,
                                              secondControlValue: string, checkForZero: boolean = false,
  ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {
      const firstControl = getControlFromGroup(firstControlName, group);
      const secondControl = getControlFromGroup(secondControlName, group);

      let secondControlValueMismatch = secondControl.value !== secondControlValue;
      let firstControlValueSet = firstControl.value !== ''
        || (checkForZero && firstControl.value !== '' && firstControl.value !== 0);

      if (secondControlValueMismatch || (!secondControlValueMismatch && firstControlValueSet)) {
        return null;
      }

      return { 'validateFirstIfSecondIsSetWithValue': { valid: false, firstControlName } };
    };
  }

  static validateFirstMaxValueIfSecondSetWithValue( firstControlName: string, secondControlName: string,
                                                    values: { [secondValue: string]: number },
                                                    checkForZero: boolean = false,
                                                    firstDisplayName?: string,
                                                    secondDisplayName?: string
  ) {
    return ( group: FormGroup ): { [key: string]: any } => {
      const firstControl = getControlFromGroup(firstControlName, group);
      const secondControl = getControlFromGroup(secondControlName, group);

      if (!firstDisplayName) {
        firstDisplayName = firstControlName;
      }
      if (!secondDisplayName) {
        secondDisplayName = secondControlName;
      }

      const value = values[secondControl.value];
      const firstControlValueGreaterThanValue = firstControl.value > value;
      let firstControlValueSet = firstControl.value !== ''
        || (checkForZero && firstControl.value !== '' && firstControl.value !== 0);

      if (!firstControlValueGreaterThanValue && firstControlValueSet) {
        return null;
      }

      return {
        'validateFirstMaxValueIfSecondSetWithValue': {
          valid: false,
          firstDisplayName,
          secondDisplayName,
          firstControlName,
          secondControlName,
          secondControlValue: secondControl.value,
          value
        }
      };
    };
  }


  static compareFirstValueWithSecondValue( firstControlName: string,
                                           secondControlName: string,
                                           comparisonType: ValidationComparisonType,
                                           controlDisplayNames?: { [controlName: string]: string },
                                           ignoreZeroValue: boolean = false
  ) {
    return ( group: FormGroup ): { [key: string]: any } => {
      let validationResult = null;

      let firstControl = getControlFromGroup(firstControlName, group);
      let secondControl = getControlFromGroup(secondControlName, group);

      const firstControlValue = firstControl.value;
      const secondControlValue = secondControl.value;
      const firstControlValueNotSet = firstControl.value === '' || (ignoreZeroValue && firstControl.value === 0);
      const secondControlValueNotSet = secondControl.value === '' || (ignoreZeroValue && secondControl.value === 0);
      const comparisonFn = comparisonTypeToComparisonFn[comparisonType];

      if (!firstControlValueNotSet && !secondControlValueNotSet && comparisonFn(firstControlValue, secondControlValue) === false) {
        validationResult = {
          'compareFirstValueWithSecondValue': {
            firstControlName,
            secondControlName,
            comparisonType,
            controlDisplayNames
          }
        };

      }

      return validationResult;
    };
  }

  static dateGreaterThanNow( control: AbstractControl ): { [key: string]: any } {
    const date = new Date();
    const inputDate = new Date(control.value);

    if (inputDate.getTime() > date.getTime()) {
      return null;
    }

    return { 'dateGreaterThanNow': { valid: false } };
  }

  static entityDomainValidator(): ValidatorFn {
    return ( control: AbstractControl ): ValidationErrors | null => {
      const value = control.value || '';
      const parts = value.split('.').filter(i => i.length);
      const levels = 2;
      let result = null;

      if (value.substr(0, 4) === 'http') {
        result = Object.assign({}, result, { domainHttp: true });
      }

      if (value && parts.length < levels) {
        result = Object.assign({}, result, { domainUrlParts: true });
      }

      return result;
    };
  }

  static atLeastOneItemRequired( items: any[], valueSelector: Function, titles?: any[] ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let value;
      let itemsString;
      let result = null;
      let valid;

      if (valueSelector) {
        value = valueSelector(control);
      } else {
        value = control.value;
      }

      if (Array.isArray(value)) {
        valid = items.some(item => value.indexOf(item) > -1);
      } else {
        valid = items.some(item => item === value);
      }

      if (titles) {
        itemsString = titles.join(', ');
      } else {
        itemsString = items.join(', ');
      }

      if (!valid) {
        result = { 'atLeastOneItemRequired': { valid: false, itemsString: itemsString } };
      }

      return result;
    };
  }

  static fileFormatValidator( control: AbstractControl ): ValidationErrors {
    const value = control.value;
    if (!value || !(value instanceof Error)) {
      return null;
    }
    return { fileFormatNotSupported: true };
  }

  static simpleUrlValidation( control: AbstractControl ): ValidationErrors | null {
    const regexp = /(^(?:http(s)?:\/\/)?([\{]?)+[\w]+([\-\.]{1}[\w]+)*\.[\w]{2,5}(:[\d]{1,5})?([\}]?)+(\/.*)?$)|(^(?:http(s)?:\/\/)?([\{]?)+[\w]+([\}]?)+(\/.*)?$)/gm;
    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    }
    return { 'urlIsNotCorrect': true };
  }

  static simpleUrlLocalhostValidation( control: AbstractControl ): ValidationErrors | null {
    const regexp = /(^(?:http(s)?:\/\/)?([\{]?)+[\w]+([\-\.]{1}[\w]+)*\.[\w]{2,5}(:[\d]{1,5})?(?:localhost:[\d]{1,5})?([\}]?)+(\/.*)?$)|(^http(s)?:\/\/localhost:[\d]{1,5}(\/.*)?$)|(^(?:http(s)?:\/\/)?([\{]?)+[\w]+([\}]?)+(\/.*)?$)/gm;

    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    }
    return { 'urlIsNotCorrect': true };
  }

  static fullUrlValidation( control: AbstractControl ): ValidationErrors | null {
    const value = control.value === null ? '' : control.value.toString();
    return isOptionalUrl(value) ? null : { 'urlFormat': true };
  }

  static urlValidation( control: AbstractControl ): ValidationErrors | null {
    const regexp = new RegExp([
      '^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})',
      '(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})',
      '(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}',
      '(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff]',
      '[a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+',
      '(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$'
    ].join(''), 'i');
    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    }
    return { 'urlIsNotCorrect': true };
  }

  static gameCoeffValidation(): ValidatorFn {
    return ( control: FormArray ): { [key: string]: any } => {
      return control.controls.filter(( { status } ) => status === 'INVALID')
        .length === 0 ? null : { coeffsRequired: true };
    };
  }

  static noWhitespaceValidation( control: AbstractControl ): ValidationErrors | null {
    return !control.value.includes(' ') ? null : { 'spacesAreNotAllowed': true };
  }

  static colorHexValidator( control: AbstractControl ) {

    const regexp = /^(#{1})([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$/; // example #000000 || #000

    const value = control.value === null ? '' : control.value.toString();

    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'invalidColorHexFormat': false };
    }
  }

  static customerIdValidator( control ) {
    const regexp = /^[\w-]{6,30}$/gi;

    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidCustomerId': true };
    }
  }

  static stakeAllValidator( divider = ',' ): ValidatorFn {
    return ( control: FormArray ): { [key: string]: any } => {
      let stakeAll = control.value.split(divider).map(Number);

      if (!stakeAll.some(item => item < 0.01) && !stakeAll.some(isNaN)) {
        return null;
      } else {
        return { 'invalidStakeAll': true };
      }
    };
  }

  static validateStakeDef(): ValidatorFn {
    return ( control: FormArray ): { [key: string]: any } => {
      if (control.parent && control.parent.controls && control.parent.controls['stakeAll']) {
        let stakeAll = control.parent.controls['stakeAll'];
        const arr = stakeAll.value.split(',').map(( el: string ) => parseFloat(el));

        if (arr.indexOf(parseFloat(control.value)) !== -1) {
          return null;
        } else {
          return { 'invalidStakeDef': true };
        }
      }
    };
  }

  static gamesAndGameGroupsAreNotTheSame( firstControlName: string, secondControlName: string,
                                          firstValue: string, secondValue: string
  ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {
      const firstControl = getControlFromGroup(firstControlName, group);
      const secondControl = getControlFromGroup(secondControlName, group);

      if (firstControl.value === firstValue && secondControl.value === secondValue) {
        return { 'limitsCanNotCloneThemselves': true };
      } else {
        return null;
      }
    };
  }

  static validateStakeAllValues( range ): ValidatorFn {
    if (!range.length) return null;

    return ( control: FormArray ): { [key: string]: any } => {
      let stakeAll = control.value.split(',').map(Number);

      if (stakeAll.every(value => range.includes(value))) {
        return null;
      } else {
        return { 'stakeAllOutOfRange': { range: range.join(', ') } };
      }
    };
  }

  static arrayDuplicatesValidator( divider = ',' ): ValidatorFn {
    return ( control: FormArray ): { [key: string]: any } => {
      let stakeAll = control.value.split(divider).map(Number);

      if ((new Set(stakeAll)).size !== stakeAll.length) {
        return { 'valuesCanNotBeDuplicated': true };
      } else {
        return null;
      }
    };
  }

  static gameGroupNameValidator( control: FormArray ) {
    const regex = /^[\w]{4,5}:\/{1,2}/;
    return control.value.match(regex) ? { 'invalidFormat': true } : null;
  }

  static aamsCodeValidator( control ) {
    try {
      if (!control.value) {
        return { 'aamsCodeError': true };
      }

      const settings = JSON.parse(control.value);
      const regexp = /^[0-9]*$/;

      if (settings.aamsCode && settings.aamsCode.toString().match(regexp)) {
        return null;
      } else {
        return { 'aamsCodeError': true };
      }
    } catch (e) {
    }
  }

  static areControlsValuesEquals( firstControlName: string, secondControlName: string,
                                  errorMessage = 'valuesAreNotTheSame'
  ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {
      const firstControl = getControlFromGroup(firstControlName, group);
      const secondControl = getControlFromGroup(secondControlName, group);

      if (firstControl.value && secondControl.value && firstControl.value !== secondControl.value) {
        return { [errorMessage]: true };
      } else {
        return null;
      }
    };
  }

  static positiveNumbers( control: AbstractControl ) {
    return control.value >= 0 ? null : { 'positiveNumbers': true };
  }

  static atLeastOneFieldShouldBeRequired( fieldNames: string[], displayNames?: string ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {
      let controls: AbstractControl[] = [];

      fieldNames.forEach(fieldName => {
        controls.push(getControlFromGroup(fieldName, group));
      });

      return controls.some(control => control.value) ?
        null :
        { 'atLeastOneFieldShouldBeRequired': { valid: false, fields: displayNames ? displayNames : fieldNames?.join(', ') } };
    };
  }

  static validateFirstMoreThanSecondAndLessThanThird( firstName: string, secondName: string, thirdName: string,
                                                      firstDisplayName: string, secondDisplayName: string, thirdDisplayName: string
  ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {

      let firstControl = getControlFromGroup(firstName, group);
      let secondControl = getControlFromGroup(secondName, group);
      let thirdControl = getControlFromGroup(thirdName, group);

      if (firstControl?.value && secondControl?.value && parseFloat(firstControl?.value) < parseFloat(secondControl?.value)) {
        return { 'firstShouldBeMoreThanSecondAndLessThanThird': { valid: false, firstDisplayName, secondDisplayName, thirdDisplayName } };
      } else if (firstControl?.value && thirdControl?.value && parseFloat(firstControl?.value) > parseFloat(thirdControl?.value)) {
        return { 'firstShouldBeMoreThanSecondAndLessThanThird': { valid: false, firstDisplayName, secondDisplayName, thirdDisplayName } };
      } else {
        return null;
      }
    };
  }

  static validateOneOrNoneOfFieldsIsSpecified( firstName: string, secondName: string, fieldsDisplayNames: string,
  ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {

      let firstControl = getControlFromGroup(firstName, group);
      let secondControl = getControlFromGroup(secondName, group);

      if (firstControl.value && secondControl.value) {
        return { 'oneOrNoneOfFieldsShouldBeSpecified': { valid: false, fieldsDisplayNames } };
      } else {
        return null;
      }
    };
  }

  static fractionsNumbersLengthValidator( control: AbstractControl ) {
    const regexp = /^-{0,1}\d+(\.\d{1,2})?$/;
    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'fractionsNumbersLength': true };
    }
  }

  static minMaxValidator( min: string, max: string ) {
    return ( group: FormGroup ) => {
      const minValue = group.controls[min].value;
      const maxValue = group.controls[max].value;

      if (minValue && maxValue) {
        if (minValue > maxValue || minValue === maxValue) {
          group.controls[min].setErrors({ 'minLowerThanMax': true });
        } else {
          if (group.controls[min].hasError('minLowerThanMax')) {
            delete group.controls[min].errors['minLowerThanMax'];
            group.controls[min].updateValueAndValidity();
          }
        }
      } else {
        if (group.controls[min].hasError('minLowerThanMax')) {
          delete group.controls[min].errors['minLowerThanMax'];
          group.controls[min].updateValueAndValidity();
        }
      }
    };
  }

  static lowerThanValidator( value: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return (control.value > value || control.value === value) ? { 'lowerThan': { value: value } } : null;
    };
  }

  static greaterThanValidator( value: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return (control.value < value || control.value === value) ? { 'greaterThan': { value: value } } : null;
    };
  }

  static greaterOrEqualValidator( min: string, max: string ) {
    return ( group: FormGroup ) => {
      const minValue = group.controls[min].value;
      const maxValue = group.controls[max].value;

      if (minValue && maxValue) {
        if (minValue > maxValue) {
          group.controls[min].setErrors({ 'greaterOrEqual': { max, min } });
        } else {
          if (group.controls[min].hasError('greaterOrEqual')) {
            delete group.controls[min].errors['greaterOrEqual'];
            group.controls[min].updateValueAndValidity();
          }
        }
      } else {
        if (group.controls[min].hasError('greaterOrEqual')) {
          delete group.controls[min].errors['greaterOrEqual'];
          group.controls[min].updateValueAndValidity();
        }
      }
    };
  }

  static isArrayContainsValue( arr: any[] ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let value = typeof control.value === 'string' ?
        control.value?.toLowerCase() :
        control.value;

      if (!arr?.includes(value)) {
        return null;
      }

      return { 'valueShouldBeUnique': true };
    };
  }
}
