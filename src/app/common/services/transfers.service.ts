import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { map, share } from 'rxjs/operators';

import { BaseService } from '../base';
import { Payment } from '../typings';
import { CsvSchema, CsvService, transformDate } from './csv.service';
import { FORMAT_DATETIME } from '../../app.constants';
import { transformCurrencyItem, transformFormatCurrencyValue } from '../core/currecy-transform';

const csvSchema = ( timezoneName: string, format: string, from?: CsvSchema, to?: CsvSchema ): CsvSchema[] => [
  {
    name: 'trxId',
    title: 'PAYMENTS_TRANSFERS.CSV.trxId',
  },
  {
    name: 'playerCode',
    title: 'PAYMENTS_TRANSFERS.CSV.playerCode',
  },
  {
    name: 'orderType',
    title: 'PAYMENTS_TRANSFERS.CSV.from',
    transform( data: string ): string {
      switch (data) {
        case 'transfer_in':
          return 'OP Wallet';
        case 'transfer_out':
          return 'SW Wallet';
        default:
          return '';
      }
    }
  },
  ...(from ? [from] : []),
  {
    name: 'extTrxId',
    title: 'PAYMENTS_TRANSFERS.CSV.extTrxId',
  },
  {
    name: 'amount',
    title: 'PAYMENTS_TRANSFERS.CSV.amount',
    transform( data: string, rows ): string {
      return transformFormatCurrencyValue(Number(data), rows.currencyCode);
    }
  },
  {
    name: 'playerBalanceAfter',
    title: 'PAYMENTS_TRANSFERS.CSV.playerBalanceAfter',
    transform( data: string, rows ): string {
      return transformFormatCurrencyValue(Number(data), rows.currencyCode);
    }
  },
  {
    name: 'currencyCode',
    title: 'PAYMENTS_TRANSFERS.CSV.currencyCode',
    transform( data: string ): string {
      return transformCurrencyItem(0, data).label;
    }
  },
  {
    name: 'orderType',
    title: 'PAYMENTS_TRANSFERS.CSV.to',
    transform( data: string ): string {
      switch (data) {
        case 'transfer_out':
          return 'OP Wallet';
        case 'transfer_in':
          return 'SW Wallet';
        default:
          return '';
      }
    }
  },
  ...(to ? [to] : []),
  {
    name: 'startDate',
    title: 'PAYMENTS_TRANSFERS.CSV.startDate',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'endDate',
    title: 'PAYMENTS_TRANSFERS.CSV.endDate',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'orderStatus',
    title: 'PAYMENTS_TRANSFERS.CSV.orderStatus',
  },
];

const ORDER_TYPES = {
  'transfer_in':
    {
      to: 'SW Wallet',
      from: 'OP Wallet'
    },
  'transfer_out':
    {
      to: 'OP Wallet',
      from: 'SW Wallet',
    }
};

@Injectable()
export class TransfersService extends BaseService<Payment> implements SwuiGridDataService<Payment> {
  public urlList: string = '/payments/';
  public urlGet: string = '/payments/';
  public urlSave: string = '/payments/';
  public urlCreate: string = '/payments/';
  public urlRemove: string = '/payments/';

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient,
               private csvService: CsvService,
               private setting: SettingsService
  ) {
    super(notifications, http);
  }

  public getGridData( params: HttpParams, data?: GridRequestData ): Observable<HttpResponse<Payment[]>> {
    let path;

    if (typeof data !== 'undefined' && data.hasOwnProperty('path')) {
      path = data['path'];
    } else if (params.has('path')) {
      path = params.get('path');
    }

    return this.http
      .get<Payment[]>(this.getUrl(path), {
        params,
        observe: 'response'
      }).pipe(
        map(res => {
          return res.clone({
            body: (res.body || []).map(item => {
              return {
                ...item,
                ...(ORDER_TYPES[item.orderType] || {})
              };
            })
          });
        })
      );
  }

  public getList( filter? ): any {
    let fn = this.processRecord.bind(this);
    filter.values = {
      ...filter.values,
      orderType__in: ['transfer_in', 'transfer_out']
    };
    this.requestParams = BaseService.getRequestParams(filter);
    let { values: { path } } = filter;
    const url = this.getUrl(path);

    const source = this.http
      .get<Payment[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          const data = response.body.map(fn) as Payment[];
          this.responseHeaders = this.getResponseParams(response, filter, data);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public processRecord( record: Payment ): Payment {

    record._meta = {
      startDate: record.startDate && moment(record.startDate),
      endDate: record.endDate && moment(record.endDate),
    };

    record.agentDomain = record.agentDomain || '';


    let from: string;
    let to: string;
    switch (record.orderType) {
      case 'transfer_in':
        to = 'SW Wallet';
        from = 'OP Wallet';
        break;
      case 'transfer_out':
        to = 'OP Wallet';
        from = 'SW Wallet';
        break;
      default:
        break;
    }
    // todo: add from and to fields

    Object.assign(record, { from, to });

    return record;
  }

  public downloadCsv(): Observable<any> {
    const fileName = `Export Transfers ${moment().format('YYYY-MM-DD HH:MM')}`;
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const schema = csvSchema(timezoneName, datetimeFormat);
    return this.csvService.download(this.getUrl.bind(this), schema, fileName, {
      orderType__in: 'transfer_in,transfer_out'
    });
  }

  public exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const fileName = `Export Transfers ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const schema = csvSchema(timezoneName, datetimeFormat,
      {
        name: 'from',
        title: 'PAYMENTS_TRANSFERS.CSV.from',
        transform: ( _: string, record: Payment ) => {
          switch (record.orderType) {
            case 'transfer_in':
              return 'OP Wallet';
            case 'transfer_out':
              return 'SW Wallet';
            default:
              return '';
          }
        },
      },
      {
        name: 'to',
        title: 'PAYMENTS_TRANSFERS.CSV.to',
        transform: ( _: string, record: Payment ) => {
          switch (record.orderType) {
            case 'transfer_in':
              return 'SW Wallet';
            case 'transfer_out':
              return 'OP Wallet';
            default:
              return '';
          }
        },
      },
    );
    this.csvService.exportToCsv(schema, data, fileName, columns);
  }
}
