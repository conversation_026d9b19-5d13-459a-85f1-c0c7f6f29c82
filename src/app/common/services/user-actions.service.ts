import { Injectable } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Observable, of, throwError } from 'rxjs';
import { share, switchMap } from 'rxjs/operators';
import { PERMISSIONS_NAMES } from '../../app.constants';
import { User } from '../../pages/users/user.model';
import { Entity } from '../models/entity.model';
import { UserService } from './user.service';

@Injectable()
export class UserActionsService {

  constructor( private authService: SwHubAuthService,
               private service: UserService<User>,
  ) {
  }

  public unblockUserGranted( entity: Entity ): boolean {
    let unblockUserPermission: string[] = [
      PERMISSIONS_NAMES.USER_UNBLOCK_CHANGE_PASSWORD,
      PERMISSIONS_NAMES.USER_UNBLOCK_LOGIN
    ];

    if (entity.isRoot()) {
      unblockUserPermission = [
        PERMISSIONS_NAMES.KEYENTITY_USER_UNBLOCK_CHANGE_PASSWORD,
        PERMISSIONS_NAMES.KEYENTITY_USER_UNBLOCK_LOGIN
      ];
    }

    return this.authService.allowedTo(unblockUserPermission);
  }

  public onUnblockUserAction( { user, type }, entity: Entity ): Observable<any> {
    let result = of();
    if (this.unblockUserGranted(entity)) {
      result = this.service.unblockUserAccount(user, type);
    } else {
      result = result.pipe(
        switchMap(() => throwError('Unable to unblock user'))
      );
    }
    return result;
  }

  public twofaResetEnabled(): boolean {
    return this.authService.allowedTo([PERMISSIONS_NAMES.USER_EDIT]);
  }

  public editUserEnabled(): boolean {
    return this.authService.allowedTo([
      PERMISSIONS_NAMES.USER_EDIT,
      PERMISSIONS_NAMES.KEYENTITY_USER_EDIT
    ]);
  }

  public onTwofaResetAction( { user, types } ) {
    return this.service.resetTwoFactorSettingsForUser(user, types)
      .pipe(share());
  }

  public deleteUserGranted( entity: Entity ): boolean {
    let deletePermission: string = PERMISSIONS_NAMES.USER_DELETE;

    if (entity.isRoot()) {
      deletePermission = PERMISSIONS_NAMES.KEYENTITY_USER_DELETE;
    }

    return this.authService.allowedTo([deletePermission]);
  }

  public onDeleteUserAction( user, entity: Entity ): Observable<any> {
    let result = of(null);
    if (this.deleteUserGranted(entity)) {
      result = this.service.deleteUserAccount(user).pipe(share());
    }
    return result;
  }
}
