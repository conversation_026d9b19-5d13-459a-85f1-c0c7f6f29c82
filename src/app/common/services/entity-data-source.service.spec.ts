import { TestBed } from '@angular/core/testing';

import { EntityDataSourceService } from './entity-data-source.service';
import { EntityService } from './entity.service';
import { EntityServiceMock } from './mocks/entity-service.mock';

describe('EntityDataSourceService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    providers: [
      { provide: EntityService, useClass: EntityServiceMock }
    ]
  }));

  it('should be created', () => {
    const service: EntityDataSourceService = TestBed.inject(EntityDataSourceService);
    expect(service).toBeTruthy();
  });
});
