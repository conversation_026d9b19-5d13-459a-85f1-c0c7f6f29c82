import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { catchError, concatAll, map, share } from 'rxjs/operators';

import { BaseService } from '../base';

@Injectable()
export class NewLimitsService<T> extends BaseService<T> {

  public urlGameLimits: string = '/game-limits';
  public urlGameLimitsExtended: string = '/game-limits-extended';

  constructor( public http: HttpClient,
               public notifications: SwuiNotificationsService,
  ) {
    super(notifications, http);
  }

  public getLimitIfExists( schemaDefinitionId, path?, gameCode? ) {
    if (!path) {
      path = undefined;
    }

    let url = this.getUrl(path, this.urlGameLimits);
    let params = { schemaDefinitionId, gameCode };

    const source = this.http
      .get<T[]>(url, {
        params: BaseService.getRequestParams({}, params)
      }).pipe(
        map(items => {
          const filteredItems = items.filter(item => {
            return gameCode && item['gameCode'] && item['gameCode'] === gameCode ?
              item : (!gameCode && !item['gameCode'] ? item : null);
          });
          return filteredItems.length ? filteredItems : [{}];
        }),
        concatAll()
      );

    source.pipe(
      catchError(err => this.handleErrors.call(this, err)),
      share(),
    );

    return source;
  }

  public changeGameLimits( body, entityPath?, id? ) {
    if (!entityPath) {
      entityPath = undefined;
    }

    let urlGameLimits = id ? `${this.urlGameLimits}/${id}` : this.urlGameLimits;
    let url = this.getUrl(entityPath, urlGameLimits);

    const method = id ? 'PATCH' : 'POST';

    return this.http
      .request(method, url, { body })
      .pipe(
        share()
      );
  }

  public getExtendedGameLimits( path, gameCode, gameGroup? ): Observable<any[]> {
    if (!path) {
      path = undefined;
    }

    let url = `${this.getUrl(path, this.urlGameLimitsExtended)}/${gameCode}`;

    if (gameGroup) {
      url = `${url}?gameGroupName=${gameGroup}`;
    }

    return this.http.get<any[]>(url).pipe(
      share()
    );
  }

  getLimits( path, gameCode, gameGroup?, schemaDefinitionId? ) {
    if (!path) {
      path = undefined;
    }

    let url = this.getUrl(path, this.urlGameLimits);
    let params = { gameCode, gameGroup, schemaDefinitionId };

    const source = this.http
      .get<T[]>(url, {
        params: BaseService.getRequestParams({}, params)
      });

    source.pipe(
      catchError(err => this.handleErrors.call(this, err)),
      share(),
    );

    return source;
  }
}
