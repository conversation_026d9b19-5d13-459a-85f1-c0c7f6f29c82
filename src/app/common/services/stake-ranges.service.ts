import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { share } from 'rxjs/operators';
import { BaseService } from '../base';
import { StakeAllRange } from '../typings/stake-all-range';

@Injectable()
export class StakeRangesService {

  public urlStakeRanges = 'stake-ranges';

  constructor( public http: HttpClient ) {
  }

  getStakeAllRanges(): Observable<StakeAllRange[]> {
    let url = `${BaseService.apiEndpoint}/${this.urlStakeRanges}`;

    return this.http.get<StakeAllRange[]>(url, {}).pipe(
      share()
    );
  }
}
