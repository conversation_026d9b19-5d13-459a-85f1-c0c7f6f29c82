import { Balance, BalanceData } from '../typings';
import { FormattedMoneyPipe } from '../pipes/formatted-money/formatted-money.pipe';

interface TransformedCurrency {
  value: number;
  label: string;
  fractionCount: number;
}

const cryptoCurrencies = {
  BTC: {
    code: 'BTC',
    label: 'BTC',
    divider: 100000000,
    fractionCount: 8
  },
  ETH: {
    code: 'ETH',
    label: 'ETH',
    divider: 1000000000,
    fractionCount: 9
  },
  LTC: {
    code: 'LTC',
    label: 'LTC',
    divider: 100000000,
    fractionCount: 8
  },
  DOG: {
    code: 'DOG',
    label: 'DOGE',
    divider: 100000000,
    fractionCount: 8
  },
  DOGE: {
    code: 'DOGE',
    label: 'DOGE',
    divider: 1,
    fractionCount: 8
  },
  TET: {
    code: 'TET',
    label: 'USDT',
    divider: 100000000,
    fractionCount: 8
  },
  USDT: {
    code: 'USDT',
    label: 'USDT',
    divider: 1,
    fractionCount: 8
  },
  USDC: {
    code: 'USDC',
    label: 'USDC',
    divider: 1000000,
    fractionCount: 6
  },
};

const ghCryptoCurrencies = {
  BTC: {
    code: 'BTC',
    label: 'µBTC',
    divider: 100
  },
  DOG: {
    code: 'DOG',
    label: 'DOGE',
    divider: 100000000,
    toFixed: 2
  },
  ETH: {
    code: 'ETH',
    label: 'mETH',
    divider: 1000000,
    toFixed: 2
  },
  LTC: {
    code: 'LTC',
    label: 'mLTC',
    divider: 100000,
    toFixed: 2
  },
  TET: {
    code: 'TET',
    label: 'USDT',
    divider: 100000000,
    toFixed: 2
  },
  USDC: {
    code: 'USDC',
    label: 'USDC',
    divider: 1000000,
    fractionCount: 6
  },
};

function convertCurrencyItem(item: number, cryptoValue: any): TransformedCurrency {
  let res = item ?? 0;
  res = res / cryptoValue.divider;

  if (cryptoValue.fractionCount) {
    res = Math.round(res * Math.pow(10, cryptoValue.fractionCount)) / Math.pow(10, cryptoValue.fractionCount);
  }

  return {value: res, label: cryptoValue.label, fractionCount: cryptoValue.fractionCount};
}

export function getCurrencyFractionCount(code: string): number {
  const cryptoValue = cryptoCurrencies[code];

  if (!cryptoValue) {
    return 2;
  }

  return cryptoValue.fractionCount || 2;
}

export function getCurrencyLabel(code: string): string {
  const cryptoValue = cryptoCurrencies[code];

  if (!cryptoValue) {
    return code;
  }

  return cryptoValue.label || code;
}

export function getCurrencyDivider(code: string): number {
  const cryptoValue = cryptoCurrencies[code];

  if (!cryptoValue) {
    return 1;
  }

  return cryptoValue.divider || 1;
}

export function transformCurrencyItem(item: number, cryptoCode: string): TransformedCurrency {
  const cryptoValue = cryptoCurrencies[cryptoCode];

  if (!cryptoValue) {
    return {value: item, label: cryptoCode, fractionCount: 2};
  }

  return convertCurrencyItem(item, cryptoValue);
}

export function transformCurrencyValue(item: number, cryptoCode: string): number {
  const cryptoValue = cryptoCurrencies[cryptoCode];

  if (!cryptoValue) {
    return item;
  }

  return convertCurrencyItem(item, cryptoValue).value;
}

export function transformFormatCurrencyValue(item: number, cryptoCode: string): string {
  const cryptoValue = cryptoCurrencies[cryptoCode];

  if (!cryptoValue) {
    return new FormattedMoneyPipe().transform(item, 2);
  }

  return new FormattedMoneyPipe().transform(convertCurrencyItem(item, cryptoValue).value, cryptoValue.fractionCount);
}

export function transformBalances(balances: Balance, useString?: boolean): Balance {
  return Object.entries<BalanceData>(balances)
    .reduce<Balance>((res: Balance, [currency, value]) => {
      const cryptoValue = transformCurrencyItem(value.main, currency);

      res[currency] = {
        main: useString ? cryptoValue.value.toFixed(cryptoValue.fractionCount) : cryptoValue.value,
        label: cryptoValue.label
      } as any;

      return res;
    }, {} as Balance);
}

export const transformSpin = (spin: any, currency: string): any => {
  const cryptoValue = cryptoCurrencies[currency];

  if (!cryptoValue) {
    return spin;
  }

  ['win', 'bet', 'balanceBefore', 'balanceAfter', 'totalJpContribution', 'totalJpWin', 'credit', 'debit']
    .forEach(field => {
      spin[field] = transformCurrencyValue(spin[field], currency);
    });
};

export const transformGhSpinDetails = (spin: any, currency: string) => {
  const cryptoValue = ghCryptoCurrencies[currency];

  if (!cryptoValue) {
    return spin;
  }

  spin.currency = cryptoValue.label;

  if (!spin.details) {
    return spin;
  }

  convertCryptoInObject(cryptoValue, spin);
};

export const transformSpinDetails = (spin: any, currency: string) => {
  const cryptoValue = cryptoCurrencies[currency];

  if (!cryptoValue) {
    return spin;
  }

  spin.currency = cryptoValue.label;

  if (!spin.details) {
    return spin;
  }

  convertCryptoInObject(cryptoValue, spin);
};

export function multiplyAmount(amount: number, currency: string): number {
  const cryptoValue = cryptoCurrencies[currency];

  if (!cryptoValue) {
    return amount;
  }

  return amount * cryptoValue.divider;
}

const excludedFields = {
  'details.multiplier': true,
  'details.events': true,
  'details.reels': true,
  'details.amountExplosion': true,
  'details.countWild': true,
  'details.rewards.lineMultiplier': true,
  'details.rewards.multiplier': true,
  'details.rewards.paytable': true,
  'details.rewards.sceneMultiplier': true,
  'details.rewards.symbolCounter': true,
  'details.rewards.symbolId': true,
  'details.rewards.winWay': true,
  'details.rewards.amountExplosion': true,
  'details.rewards.lineId': true,
  'details.stake.lines': true,
  'details.stake.coin': true,
  'details.state.multiplier': true,
  'details.state.towerStages': true,
  'details.state.freeSpinsCount': true,
  'details.state.initialFreeSpinsCount': true,
  'details.state.totalFreeSpinsCount': true,
  'details.roundsInfo.currentRoundId': true,
  'details.roundsInfo.selectedTreeId': true,
  'details.roundsInfo.previousRounds.id': true,
  'spinNumber': true,
  'initSettings': true,
  'historyInfo': true,
  'win': true,
  'bet': true,
  'balanceBefore': true,
  'balanceAfter': true,
  'totalJpContribution': true,
  'totalJpWin': true,
  'credit': true,
  'debit': true,
  'offset': true
};

const convertCryptoInObject = ( cryptoValue: typeof ghCryptoCurrencies.TET, data: any, field: string = '' ) => {
  Object.entries(data).forEach(( [key, value] ) => {
    if (excludedFields[`${field}${key}`] || value === null || value === undefined) {
      return;
    }

    if (typeof value === 'number') {
      data[key] = value / cryptoValue.divider;
      return;
    }

    if (Array.isArray(value)) {
      value.forEach(val => {
        convertCryptoInObject(cryptoValue, val, `${field}${key}.`);
      });
      return;
    }

    if (typeof value === 'object') {
      convertCryptoInObject(cryptoValue, value, `${field}${key}.`);
    }
  });
};

