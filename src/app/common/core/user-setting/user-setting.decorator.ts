import { SwDexieService } from '@skywind-group/lib-swui';
import { AppModule } from '../../../app.module';
import { UserSettingOptions } from './user-setting.model';

export function userSetting( opts: UserSettingOptions | string ) {
  return function<T>(target: T, key: keyof T) {
    const dexie = AppModule.injector.get(SwDexieService);
    let val;
    let options: UserSettingOptions;
    if (typeof opts === 'string') {
      options = { name: opts };
    } else if (typeof opts === 'object') {
      options = <UserSettingOptions>opts;
    }

    dexie.getSetting(options).then(result => val = result);

    Object.defineProperty(target, key, {
      configurable: true,
      enumerable: true,
      set: (value) => {
        val = value;
        if (opts) {
          dexie.putSetting(options, value);
        }
      },
      get: () => val,
    });
  };
}
