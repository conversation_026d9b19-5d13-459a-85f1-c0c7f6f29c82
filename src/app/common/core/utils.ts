import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';


export function getRouteMergedSnapshotParams( route: ActivatedRoute ): any {
  return getRouteSnapshotChain(route)
    .reduce(( data, item ) => ({ ...data, ...item.params }), {});
}

export function getRouteMergedSnapshotData( route: ActivatedRoute ): any {
  return getRouteSnapshotChain(route)
    .reduce(( data, item ) => ({ ...data, ...item.data }), {});
}

export function getRouteSnapshotChain( route: ActivatedRoute ): ActivatedRouteSnapshot[] {
  return route.pathFromRoot.filter(p => !!p.component).map(p => p.snapshot);
}
