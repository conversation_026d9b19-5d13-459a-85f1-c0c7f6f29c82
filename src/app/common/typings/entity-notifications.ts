export interface EntityNotifications {
  lowBalance: EntityNotificationsGroup;
  engagement: EntityNotificationsGroup;
}

export interface  EntityNotificationsGroup {
  emails: string[];
  currencies?: FeatureCurrency;
  jackpot?: FeatureCurrencyEngagement;
  tournament?: FeatureCurrencyEngagement;
}

interface FeatureCurrencyEngagement {
  frequency: string;
  range: { from: string, to: string };
}

export interface FeatureCurrency {
  [key: string]: { min: number };
}
