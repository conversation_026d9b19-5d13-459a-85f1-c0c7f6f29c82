export class BiUrlInfoSettings {
  height?: string;
  language: string;
  reportId: string;
}

export class BiUrlInfo {
  url: string;
  baseUrl: string;
  name: string;
  workbook: string;
  token: string;
  expiredAt: string;
  settings: BiUrlInfoSettings;
  trustTicket: string;
}

export type BiReportStatus = 'normal' | 'suspended';

export class BiReport {
  id: string;
  name: string;
  caption: string;
  description: string;
  permission: string;
  workbook: string;
  status: BiReportStatus;
  createdAt: string;
  reportId: string;
  settings: object;

  urlInfo?: BiUrlInfo;
}
