// import { Balance, Balances } from "./entity";
// import { ITransaction } from "@skywind-group/sw-wallet/resources/index";

import { BaseApiObject } from '../base';

export interface GameHistory extends BaseApiObject {
  id?: number;
  roundId?: number;

  gameCode?: string;
  gameNameLabel?: string;

  currency?: string;
  currencyLabel?: string;
  betAmount?: number;
  winAmount?: number;
  revenue?: number;

  bet?: number;
  win?: number;

  balanceBefore?: number;
  balanceAfter?: number;
  outcome?: string;
  ggrPerc?: string;
  provider?: string;

  finished?: boolean;

  brandId?: number;
  playerCode?: string;
  isOnline?: boolean;

  deviceId?: string;
  spinSerialNumber?: number;
  timestamp: number | Date | string;
  gameResult?: string;
  requestedPositions?: string;

  affilate?: string;
  operator?: string;
  status?: string;
  recoveryType?: string;
  entityFinalizationSupport?: string;

  isTest: boolean;

  extraData?: any;
  details?: any;
  finalizationSupport?: string;
}

export interface ExternalGameHistory extends GameHistory {
  extTrxId: string;
  gameProviderCode: string;
}

export interface UnfinishedGameHistory extends GameHistory {
  gameContextId: string;
}
