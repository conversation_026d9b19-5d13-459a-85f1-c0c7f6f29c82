// import { Balance, Balances } from "./entity";
// import { ITransaction } from "@skywind-group/sw-wallet/resources/index";

import { BaseApiObject } from '../base';

export type FinancialReportItemType = 'credit' | 'debit';

export interface FinancialReportEntityItem {
  id: string;
  name: string;
  title: string;
  path: string;
}

export interface FinancialReportItem extends BaseApiObject {
  currency: string;
  amount: number;
  ts: string;
  isTest: boolean;
  toEntityInfo: FinancialReportEntityItem;
  fromEntityInfo: FinancialReportEntityItem;
  // type: FinancialReportItemType;
}
