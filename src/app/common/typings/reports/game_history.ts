import { BaseApiObject } from '../base';

export interface GameHistory extends BaseApiObject {
  id?: number;
  roundId?: number;

  gameCode?: string;
  gameNameLabel?: string;

  currency?: string;
  betAmount?: number;
  winAmount?: number;
  revenue?: number;

  bet?: number;
  win?: number;

  balanceBefore?: number;
  balanceAfter?: number;
  outcome?: string;
  provider?: string;

  finished?: boolean;

  brandId?: number;
  playerCode?: string;
  isOnline?: boolean;

  deviceId?: string;
  spinSerialNumber?: number;
  timestamp: number | Date | string;
  gameResult?: string;
  requestedPositions?: string;

  affilate?: string;
  operator?: string;
  recoveryType?: string;
}
