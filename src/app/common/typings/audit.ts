export interface Audit {
  auditId: number;
  ts: Date;
  entityId: number;
  initiatorType: string;
  auditType: string;
  initiatorName: string;
  initiatorIssueId: string;
  history: {
    key: string;
    statusCode: string;
    operation: string;
    parameters: any;
    result: any;
    changes?: any;
    playerId?: any;
  };
  ip: string;
  system: string;
  userAgent: string;
  auditsSummary?: AuditSummary;
}

enum ACTION_METHOD {
  GET = 'get',
  POST = 'post',
  PUT = 'put',
  PATCH = 'patch',
  DELETE = 'delete',
  CRON = 'cron',
  SERVICE = 'service',
}

interface AuditSummary {
  eventName: string;
  summary: string;
  path: string;
  method: ACTION_METHOD;
}
