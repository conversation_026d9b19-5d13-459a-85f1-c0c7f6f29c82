import { BaseApiObject } from './base';

export interface RtpReducer extends BaseApiObject {
  entityId: string;
  entityTitle: string;
  inheritedFromEntityTitle: string;
  inheritedFromEntityId: string;
  changeDateTime: string;
  gameTitle: string;
  gameCode: string;
  rtp: number;
  finalRTP: number;
  rtpDeduction: number;
}

export interface RtpGameConfiguration {
  gameName: string;
  gameCode: string;
  currentRtp?: number;
  currentRtpMax?: number;
  currentRtpMin?: number;
  newRtpDeduction?: number;
  newRtpDeductionMax?: number;
  newRtpDeductionMin?: number;
  error?: boolean;
}

export interface RtpDeduction {
  gameCode: string;
  newRtpDeduction: number;
  gameName?: string;
}
