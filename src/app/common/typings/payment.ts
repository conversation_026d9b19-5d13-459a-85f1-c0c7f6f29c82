import { BaseApiObject } from './base';

export interface Payment extends BaseApiObject {
  trxId: string;
  entityId: string;
  brandId: string;
  agentDomain: string;
  playerCode: string;
  orderId: string;
  agent: string;
  operator: string;
  orderType: string;
  orderDate?: Date | any;
  startDate?: Date | any;
  endDate?: Date | any;
  orderInfo: any;
  orderStatus: string;
  currencyCode: string;
  amount: number;
  paymentMethodCode: string;
  processedBy: string;
  marks: any;
  isTest?: boolean;
  path?: boolean;
}
