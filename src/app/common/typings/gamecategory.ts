export interface Provider {
  pid: string;
  title: string;
  group: string;
}

export interface Label {
  id: string;
  title: string;
  group: string;
}

export interface GameCategoryTranslation {
  title: string;
  description?: string;
  icon?: any;
}

export interface GameCategoryTranslations {
  [key: string]: GameCategoryTranslation;
}

export interface GameCategorySettings extends GameCategoryTranslation {
  translations?: GameCategoryTranslations;
}

export interface GameCategoryInfo extends GameCategorySettings {
  id?: string;
  type: string;
  status: string;
  sortorder: number;
  isEntityOwner: boolean;
  items: GameCategoryItemInterface[];
}

export interface GameCategory extends GameCategoryInfo {
  id?: string;

  toInfo(): GameCategoryInfo;
}

export type gameCategoryItemType = 'game' | 'provider' | 'label' | 'intersection';

export const gameCategoryItemTypes: { [propName: string]: gameCategoryItemType; } = {
  GAME: 'game',
  PROVIDER: 'provider',
  LABEL: 'label',
  INTERSECTION: 'intersection'
};

export interface GameCategoryItemInterface {
  id: string;
  type: gameCategoryItemType;
  items?: GameCategoryItemInterface[];
}

export interface GameCategoryCreateData {
  name: string;
  description?: string;
  mode?: string;
  brandId: number;
}

export interface GameCategoryUpdateData {
  description?: string;
  mode?: string;
  brandId: number;
}
