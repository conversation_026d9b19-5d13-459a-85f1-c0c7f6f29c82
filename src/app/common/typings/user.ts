import { BaseApiObject } from './base';
import { Role } from '../../pages/users/components/roles/role.model';


export type Permissions = Array<string>;

export interface UserPermissions {
  grantedPermissions: Permissions;
  revokedPermissions: Permissions;
}

export interface User extends BaseApiObject, UserPermissions {
  username: string;
  password?: string;
  email: string;
  phone: string;

  status?: string;
  userType?: string;
  roles?: Array<Role>;
  roleTitle?: string;

  name?: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  entity?: string;
  entityPath?: string;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
  hoverTitle?: string;
}

export interface UserBlocking extends User {
  blocking: {
    changePasswordTillDate: Date | null,
    loginTillDate: Date | null
  };
}

export interface UserInfo {
  username: string;
  status: string;
}

export interface LoginInfo {
  userId: number;
  entityId: number;
  username: string;
  accessToken: string;
}

export interface UserProfileDto {
  entityId?: number;
  userId?: number;
  username?: string;
  accessToken?: string;
  key?: string;
  grantedPermissions?: string;
}

export interface UserProfile {
  entityId?: number;
  userId?: number;
  username?: string;
  accessToken?: string;
  key?: string;
  grantedPermissions?: string[];
}

export interface UserCredentials {
  username: string;
  password: string;
  newPassword?: string;
  rememberme?: boolean;
}

export interface UserResetData {
  identifier: string;
  secretKey: string;
}
