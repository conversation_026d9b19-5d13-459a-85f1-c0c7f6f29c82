import { BaseApiObject } from './base';


export interface WithdrawalInfo extends BaseApiObject {
  trxId: string;
  entityId: string;
  playerCode: string;
  orderId: string;
  agentDomain: string;
  operator: string;
  // orderType: string;
  orderDate?: Date | any;
  startDate?: Date | any;
  endDate?: Date | any;
  orderInfo: any;
  orderStatus: string;
  currencyCode: string;
  amount: number;
  paymentMethodCode: string;
  processedBy: string;
  marks: any;
  isTest?: boolean;
}
