export const GAME_GROUP_CUSTOMIZATION = 'GAME_GROUP';
export const GAME_GROUP_FILTERS_CUSTOMIZATION = 'GAME_GROUP_FILTERS';

export interface FlatReportLimits {
  limits: {
    winMax: number;
    stakeAll: number[];
    stakeDef: number;
    stakeMax: number;
    stakeMin: number;
    maxTotalStake: number;
  };
  limitsCustomizations: string[];
}

export interface FlatReportGameGroupInfo {
  [currency: string]: FlatReportLimits;
}

export interface FlatReportGameInfo {
  [gameGroup: string]: FlatReportGameGroupInfo;
}

export interface FlatReportInfo {
  [gameCode: string]: FlatReportGameInfo;
}

export interface FlatReport {
  entityId: string;
  report: FlatReportInfo;
  reportType: string;
  createdAt: string;
  updatedAt: string;
}
