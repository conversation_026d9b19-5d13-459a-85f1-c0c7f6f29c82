import { BaseApiObject } from './base';

export interface GrcConfigParam {
  type: string;
  title: string;
}

export interface Grc extends BaseApiObject {
  id: number;
  name: string;
  startAt: Date;
  finishAt: Date;
  potAmount: number;
  state: string;
  targetBadges: number;
  definition: GrcDefinition;
  currency: string;
}

export interface GrcDefinition {
  [key: string]: any;
}

export interface GrcEmailStart {
  nameEn: string;
  nameCh: string;
}

export interface GrcEmail extends Grc {
  body: string;
  subject: string;
  recipients: string[];
  fromEmail?: string;
  fromName?: string;
}
