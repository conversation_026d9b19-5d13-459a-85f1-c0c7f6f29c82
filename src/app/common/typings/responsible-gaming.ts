import { BaseApiObject } from './base';

export interface ResponsibleGaming extends BaseApiObject {
  playerCode: string;
  brandId: string;
  jurisdiction: string;
  settings: ResponsibleGamingSettings;
}

export interface ResponsibleGamingSettings {
  casino: ResponsibleGamingSettingsItem;
  sport_bet: ResponsibleGamingSettingsItem;
}

export interface ResponsibleGamingSettingsItem {
  realityCheck: number;
  lossLimit: number;
  lossLimitTimeframe: string;
  depositLimit: number;
  depositLimitTimeframe: string;
  casinoTimeoutTillDate: string;
  selfExclusionTillDate: string;
  lossLimitPending: number;
  lossLimitPendingTimeframe: string;
  lossLimitPendingDate: Date;
  depositLimitPending: number;
  depositLimitPendingTimeframe: string;
  depositLimitPendingDate: Date;
  createdAt: string;
  updatedAt: string;
}
