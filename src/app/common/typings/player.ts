// import { Balance, Balances } from "./entity";
// import { ITransaction } from "@skywind-group/sw-wallet/resources/index";

import { BaseApiObject } from './base';
import { Balance } from './balance';



export interface Player extends BaseApiObject {
  code?: string;
  balances: Balance;
  firstName: string;
  lastName: string;
  nickname?: string;
  nicknameChangeAttempts?: number;
  email: string;
  status: string;
  currency: string;
  country: string;
  language: string;
  createdAt: string;
  lastLogin: string;
  lastAction?: string;
  updatedAt: string;
  statusTitle?: string;
  id?: string;
  brandId?: string;
  balance?: number;
  gamegroupId?: number;
  gamegroupName?: string;
  gameGroup?: string;
  defaultGameGroup?: string;

  registrationIP?: string;
  contentProvider?: string;
  deactivatedAt?: string;

  // toInfo(): PlayerInfo;

  // isFreeze(): Promise<boolean>;
  // save(): Promise<Player>;

  // getBalances(): Promise<Balances>;
  // getBalance(currency: string): Promise<Balance>;
  // deposit(trx: ITransaction, data: DepositData): Promise<void>;
  // withdrawal(trx: ITransaction, data: WithdrawalData): Promise<void>;
  // commitGamePayment(trx: ITransaction,
  //                   data: GamePaymentData): Promise<void>;

  isTest?: boolean;
  isBlocked?: boolean;
  isOnline?: boolean;
  provider?: string;
  login?: string;
  agentDomain?: string;
  operator?: string;
}

export interface PlayerCredentials {
  oldPassword?: string;
  newPassword: string;
  confirmPassword: string;
}
