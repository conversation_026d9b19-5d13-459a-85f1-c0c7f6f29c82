import { BaseApiObject } from './base';


export interface DepositInfo extends BaseApiObject {
  id?: number;
  trxId: string;
  brandId: number;
  agentDomain: string;
  operator: string;
  playerCode: string;
  orderId: string;
  orderType: string;
  orderDate: Date;
  orderInfo: any;
  orderStatus: string;
  currencyCode: string;
  amount: number;
  paymentMethodCode: string;
  startDate: Date;
  endDate: Date;
  processedBy: string;
  marks: any;
  isTest?: boolean;
}
