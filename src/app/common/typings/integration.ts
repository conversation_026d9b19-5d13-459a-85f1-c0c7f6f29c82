import { Merchant } from '../models/entity.model';

export interface IntegrationsTestHistory {
  id: number;
  createdAt: string;
  merchantName?: string;
}

interface IntegrationsReportEnd {
  end: string;
  start: string;
  tests: string;
  passes: string;
  suites: string;
  pending: string;
  duration: string;
  failures: string;
}

interface LogsRequest {
  baseUrl: string;
  data: any;
  url: string;
}

interface LogsResponseBody {
  ticket: string;
}

interface IntegrationsReportTestsResultLogs {
  request: LogsRequest;
  requestTime: number;
  responseBody: LogsResponseBody;
  responseCode: number;
  time: string;
}

interface NewIntegrationsReportTestsResultLogs {
  data: {ticket: string, merch_id: string, merch_pwd: string};
  headers: {Accept: string, 'Content-Type': string};
  qs: any;
  requestTime: number;
  response: {
    bet_limit: any;
    country: string;
    currency_code: string;
    cust_id: string;
    cust_login: string;
    cust_session_id: string;
    error_code: 0;
    language: string;
    status: number;
    test_cust: boolean;
  };
  responseStatus: number;
  'sw-trace-id': string;
  url: string;
  time: string;
}

interface IntegrationsReportTestsResult {
  err: string;
  logs: IntegrationsReportTestsResultLogs[];
  stack: string;
  status: string;
  duration: string;
  currentRetry: string;
  title: string;
}

interface NewIntegrationsReportTestsResult {
  err: string;
  logs: NewIntegrationsReportTestsResultLogs[];
  stack: string;
  status: string;
  duration: string;
  currentRetry: string;
  title: string;
}

export interface IntegrationsReportTests {
  title: string;
  status: string;
  results: IntegrationsReportTestsResult[];
}

interface NewIntegrationsReportTests {
  title: string;
  status: string;
  results: NewIntegrationsReportTestsResult[];
}

interface IntegrationsReport {
  end: IntegrationsReportEnd;
  start: {
    total: string;
  };
  tests: IntegrationsReportTests[];
}

interface NewIntegrationsReport {
  end: IntegrationsReportEnd;
  start: {
    total: string;
  };
  tests: NewIntegrationsReportTests[];
}

export interface Integrations {
  id: number;
  gameCode: string;
  merchType: string;
  merchCode: string;
  report: IntegrationsReport;
  status: string;
  createdAt: string;
  updatedAt: string;
  version: null;
}

export interface NewIntegrations {
  id: number;
  gameCode: string;
  merchType: string;
  merchCode: string;
  report: NewIntegrationsReport;
  status: string;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export interface MerchantSpecialCases {
  isNeedJPTests?: boolean;
  isNeedFreeBetTests?: boolean;
}

export interface IntegrationsMerchantForm extends Merchant {
  pass?: string;
  url?: string;
  custId?: string;
  currencyCode?: string;
  ticket?: string;
  gameCode?: string;
  specialCases?: MerchantSpecialCases;
}
