import { BaseApiObject } from './base';

export interface LimitsByCurrencyCode {
  [field: string]: {};
}

export interface GameFeatures {
  baseRTPRange?: {
    max: number;
    min: number;
  };

  decreaseMaxBetSupported?: boolean;
  increaseMinBetSupported?: boolean;

  translations?: GameTranslationByLocale;

  [field: string]: any;
}

export interface GameClientFeatures {
  [field: string]: {};
}

export interface SearchableKeys {
  [field: string]: Object;
}

export interface GameProvider {
  id?: number;
  code: string;
  title: string;
  secret: string;
  status: string;

  isSuspended(): boolean;

  save(): Promise<GameProvider>;

  toInfo(): GameProviderInfo;
}

export interface GameLabel {
  id?: string;
  group: string;
  title: string;
}

export interface GameRtpInfo {
  rtpDeduction: number;
  rtpFinal: number;
  rtpTheoretical: number;
}

export interface Game extends BaseApiObject {
  labels: GameLabel[];
  id?: number;
  code: string;
  gameCode?: string;
  title: string;
  type: string;
  url: string;
  providerId: string;
  providerName: string;
  providerCode: string;
  providerTitle: string;
  providerGameCode: string;
  status: string;
  defaultInfo: GameDescription;
  info: GameDescriptionByLocale;
  limits: LimitsByCurrencyCode;
  comment?: string;
  rtpInfo?: GameRtpInfo;
  settings?: GameSettings;
  features?: GameFeatures;
  clientFeatures?: GameClientFeatures;
  royalties?: number;
  gameProvider?: GameProvider;
  historyRenderType?: number;
  historyUrl?: string;
  schemaDefinitionId?: string;
  countries?: string[];
  totalBetMultiplier?: string;
  limitFiltersWillBeApplied?: boolean;
  externalGameId?: string;
  currency: string;

  toCodeInfo(): GameCodeInfo;

  toProviderGameCodeInfo(): ProviderGameCodeInfo;

  toInfo(): GameInfo;

  toShortInfo(): GameShortInfo;

  toProviderInfo(): GameProviderInfo;
}

export interface GameImages {
  icon?: string;
  logo?: string;
  poster?: string;
  screenshots?: string[];
  screenshots_hd?: string[];
  overlay?: string;
}

export interface GameDescription {
  name: string;
  description: string;
  images?: GameImages;

  // field - images, screen shots etc.
  [field: string]: any;
}

export interface GameDescriptionByLocale {
  [field: string]: GameDescription;
}

export interface GameTranslation {
  title: string;
  description?: string;
}

export interface GameTranslationByLocale {
  [key: string]: GameTranslation;
}

export interface GameCodeInfo {
  code: string;
}

export interface EntityGameData {
  module: string;
  settings?: GameSettings;
}

export interface EntityGameCodeInfo extends GameCodeInfo, EntityGameData {
}

export interface ProviderGameCodeInfo {
  providerGameCode: string;
}

export interface EntityGame {
  id?: number;
  entityId: number;
  gameId: number;
  parentEntityGameId: number;
  module: string;
  settings: GameSettings;
  game?: Game;

  toInfo(): EntityGameInfo;

  toCodeInfo(): EntityGameCodeInfo;
}

export interface GameSettings {
  transferEnabled?: boolean;
  jackpotId?: any;

  [field: string]: any;
}

export interface GameShortInfo extends BaseApiObject {
  code: string;
  status: string;
  royalties?: number;
  settings?: GameSettings;
  externalGameId?: string;
}

export interface GameInfo extends GameShortInfo {
  providerName: string;
  providerCode: string;
  providerTitle: string;
  title: string;
  type: string;
  defaultInfo: GameDescription;
  info: GameDescriptionByLocale;
  limits: LimitsByCurrencyCode;
  labels: GameLabel[];
  comment?: string;
  features?: GameFeatures;
  schemaDefinitionId?: string;
  totalBetMultiplier?: string;
  rtpInfo?: GameRtpInfo;
  limitFiltersWillBeApplied?: boolean;
}

export interface EntityGameInfo extends GameInfo, EntityGameData {
}

export interface GameProviderInfo {
  providerGameCode: string;
  title: string;
  type: string;
  url: string;
  defaultInfo: GameDescription;
  info: GameDescriptionByLocale;
  limits: LimitsByCurrencyCode;
  comment?: string;
}

export interface PlayerGameURLInfo {
  url: string;
  token: string;
}

export interface ChangeEntityGameData {
  status?: string;
  settings?: GameSettings;
  royalties?: number;
  externalGameId?: string;
}

export function convertProvidersToLabels( providers: GameProvider[] ): GameLabel[] {
  return providers.map(( provider: GameProvider ) => {
    return <GameLabel>{
      // DO NOT change 'provider.code'!!!
      // If you want to use provider.id then create new function instead
      id: provider.code,
      group: 'provider',
      title: provider.title,
    };
  });
}

export function isLiveGame( game: Game | GameInfo ): boolean {
  return game.type === 'live';
}

export function isGameSupportGameGroupFilters( game: Game | GameInfo ): boolean {
  return game.limitFiltersWillBeApplied;
}

export function isGameSupportIncreasingMinBet( game: Game | GameInfo ): boolean {
  return game.features.increaseMinBetSupported;
}

export function isGameSupportDecreasingMaxBet( game: Game | GameInfo ): boolean {
  return game.features.decreaseMaxBetSupported;
}
