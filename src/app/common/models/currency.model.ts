import { SelectOptionModel, SelectOptionProvider } from './select-option.model';

export class CurrencyModel implements SelectOptionProvider {
  code: string;
  displayName: string;
  label: string;

  constructor( config: any ) {
    Object.assign(this, config);
  }

  toSelectOption(): SelectOptionModel {
    return new SelectOptionModel(this.code, `${this.displayName} (${this.label || this.code})`);
  }
}
