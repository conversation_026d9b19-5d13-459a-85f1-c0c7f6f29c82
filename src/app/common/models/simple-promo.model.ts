import { BaseApiObject } from '../typings/base';

export class FreebetGameInfoCoin {
  [ curcode: string ]: {
    coin: number;
  }
}

export class FreebetGameInfo {
  gameCode: string;
  coins: FreebetGameInfoCoin[];
}

export class SimplePromoFreebetReward {
  freebetAmount: number;
  games: FreebetGameInfo[];
}

export class SimplePromoFreebet implements BaseApiObject {
  _meta?: Object = {};

  id?: string;
  title: string;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  reward: SimplePromoFreebetReward;
  status: string;
  state: string;

  constructor( obj?: any ) {
    Object.assign(this, obj || {});
  }
}
