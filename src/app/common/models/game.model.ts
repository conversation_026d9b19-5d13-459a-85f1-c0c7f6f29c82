import {
  Game, GameCodeInfo, GameDescriptionByLocale, GameFeatures, GameInfo, GameLabel, GameProvider, GameProviderInfo, GameRtpInfo, GameSettings,
  GameShortInfo, LimitsByCurrencyCode, ProviderGameCodeInfo
} from '../typings/game';

export interface GameDescription {
  name: string;
  description: string;

  // field - images, screen shots etc.
  [field: string]: any;
}

export enum GameType {
  Slot = 'slot',
  Action = 'action',
  Table = 'table',
  External = 'external',
  Live = 'live',
}

export class GameImpl implements Game {
  public id: number;
  public code: string;
  public title: string;
  public type: string;
  public url: string;
  public status: string;
  public providerId: string;
  public providerName: string;
  public providerCode: string;
  public providerTitle: string;
  public providerGameCode: string;
  public defaultInfo: GameDescription;
  public info: GameDescriptionByLocale;
  public limits: LimitsByCurrencyCode;
  public labels: Array<GameLabel>;
  public comment: string;
  public settings: GameSettings;
  public features: GameFeatures;
  public royalties: number;
  public gameProvider: GameProvider;
  public schemaDefinitionId: string;
  public totalBetMultiplier: string;
  public rtpInfo: GameRtpInfo;
  public limitFiltersWillBeApplied?: boolean;
  public externalGameId?: string;
  public currency: string;

  constructor( item ) {
    if (!item) {
      return;
    }

    this.id = item.id || null;
    this.code = item.code || null;
    this.providerName = item.providerName || null;
    this.providerCode = item.providerCode;
    this.providerTitle = item.providerTitle;
    this.title = item.title || item.defaultInfo.name || null;
    this.type = item.type || null;
    this.status = item.status || 'available';
    this.defaultInfo = item.defaultInfo || null;
    this.info = item.info || null;
    this.limits = item.limits || null;
    this.labels = item.labels || [];
    this.comment = item.comment || null;
    this.settings = item.settings || null;
    this.features = item.features || null;
    this.royalties = item.royalties || null;
    this.schemaDefinitionId = item.schemaDefinitionId || null;
    this.totalBetMultiplier = item.totalBetMultiplier || null;
    this.rtpInfo = item.rtpInfo || null;
    this.limitFiltersWillBeApplied = item.limitFiltersWillBeApplied || false;
    this.externalGameId = item.externalGameId || null;
    this.currency = item.currency || null;
  }

  public toCodeInfo(): GameCodeInfo {
    return {
      code: this.code,
    };
  }

  public toProviderGameCodeInfo(): ProviderGameCodeInfo {
    return {
      providerGameCode: this.providerGameCode,
    };
  }

  public toProviderInfo(): GameProviderInfo {
    return {
      providerGameCode: this.providerGameCode,
      title: this.title,
      type: this.type,
      url: this.url,
      defaultInfo: this.defaultInfo,
      info: this.info,
      limits: this.limits,
      comment: this.comment,
    };
  }

  public toInfo(): GameInfo {
    return {
      code: this.code,
      providerName: this.providerName,
      providerCode: this.providerCode,
      providerTitle: this.providerTitle,
      title: this.title,
      type: this.type,
      defaultInfo: this.defaultInfo,
      info: this.info,
      status: this.status,
      limits: this.limits,
      labels: this.labels,
      comment: this.comment,
      settings: this.settings,
      features: this.features,
      royalties: this.royalties,
      schemaDefinitionId: this.schemaDefinitionId,
      totalBetMultiplier: this.totalBetMultiplier,
      rtpInfo: this.rtpInfo,
      limitFiltersWillBeApplied: this.limitFiltersWillBeApplied,
      externalGameId: this.externalGameId,
    };
  }

  public toShortInfo(): GameShortInfo {
    return {
      code: this.code,
      status: this.status,
      royalties: this.royalties,
      settings: this.settings,
      externalGameId: this.externalGameId,
    };
  }
}
