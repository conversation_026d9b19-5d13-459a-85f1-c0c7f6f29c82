import {
  ResponsibleGaming,
  ResponsibleGamingSettings,
  ResponsibleGamingSettingsItem
} from '../typings/responsible-gaming';

export class RGSettingsItem implements ResponsibleGamingSettingsItem {
  realityCheck: number;
  lossLimit: number;
  lossLimitTimeframe: string;
  depositLimit: number;
  depositLimitTimeframe: string;
  casinoTimeoutTillDate: string;
  selfExclusionTillDate: string;
  lossLimitPending: number;
  lossLimitPendingTimeframe: string;
  lossLimitPendingDate: Date;
  depositLimitPending: number;
  depositLimitPendingTimeframe: string;
  depositLimitPendingDate: Date;
  createdAt: string;
  updatedAt: string;

  constructor( obj?: any ) {
    this.realityCheck = obj && obj.realityCheck || null;
    this.lossLimit = obj && obj.lossLimit || null;
    this.lossLimitTimeframe = obj && obj.lossLimitTimeframe || '';
    this.depositLimit = obj && obj.depositLimit || null;
    this.depositLimitTimeframe = obj && obj.depositLimitTimeframe || '';
    this.casinoTimeoutTillDate = obj && obj.casinoTimeoutTillDate || '';
    this.selfExclusionTillDate = obj && obj.selfExclusionTillDate || '';
    this.lossLimitPending = obj && obj.lossLimitPending || null;
    this.lossLimitPendingTimeframe = obj && obj.lossLimitPendingTimeframe || '';
    this.lossLimitPendingDate = obj && obj.lossLimitPendingDate || '';
    this.depositLimitPending = obj && obj.depositLimitPending || null;
    this.depositLimitPendingTimeframe = obj && obj.depositLimitPendingTimeframe || '';
    this.depositLimitPendingDate = obj && obj.depositLimitPendingDate || '';
    this.createdAt = obj && obj.createdAt || '';
    this.updatedAt = obj && obj.updatedAt || '';
  }
}

export class RGSettings implements ResponsibleGamingSettings {
  casino: RGSettingsItem;
  'sport_bet': RGSettingsItem;

  constructor( obj?: any ) {
    this.casino = obj ? new RGSettingsItem(obj.casino) : new RGSettingsItem();
    this.sport_bet = obj ? new RGSettingsItem(obj.sport_bet) : new RGSettingsItem();
  }
}

export class ResponsibleGamingModel implements ResponsibleGaming {
  brandId: string;
  jurisdiction: string;
  playerCode: string;
  settings: RGSettings;

  constructor( obj: any ) {
    this.brandId = obj && obj.brandId || '';
    this.jurisdiction = obj && obj.jurisdiction || '';
    this.playerCode = obj && obj.playerCode || '';
    this.settings = obj ? new RGSettings(obj.settings) : new RGSettings();
  }
}
