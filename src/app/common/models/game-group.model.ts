import { SelectOptionModel, SelectOptionProvider } from './select-option.model';

export interface GameGroupFilter {
  id?: string;
  maxTotalBet?: number;
  maxExposure?: number;
  minTotalBet?: number;
  defTotalBet?: number;
  winCapping?: number;
  games: string[];
  group?: GameGroup;
  currencies: string[];
  createdAt?: string;
  updatedAt?: string;
  groupId?: string;
  minBetWillIncreased?: boolean;
  maxBetWillDecreased?: boolean;
}

export class GameGroup implements SelectOptionProvider {
  public name: string;
  public description: string;
  public isDefault: boolean;
  public id: string;
  public isOwner: boolean;

  constructor( obj?: any ) {
    if ('id' in obj) {
      this.id = obj.id || null;
    }
    this.name = obj.name || null;
    this.description = obj.descriptipn || null;
    this.isDefault = obj.isDefault || false;
  }

  toSelectOption(): SelectOptionModel {
    return new SelectOptionModel(this.name, `${this.name}`, false, this.isDefault);
  }
}
