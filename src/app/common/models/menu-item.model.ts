export interface RouterMenuItem {
  path?: string;
  menu?: MenuItemData;
  children?: Array<RouterMenuItem>;
  permissions?: Array<string>;
  permissionsList?: Array<Array<string>>;
  resolve?: Object;
  isBrandOnly?: boolean;
  isSuperadmin?: boolean;
}

export interface MenuItemData {
  title: string;
  icon?: string;
  fontIcon?: string;
  fontSet?: string;
  svgIcon?: string;
  hide?: boolean;
  selected?: boolean;
  expanded?: boolean;
  order?: number;
  hideChildren?: boolean;
}

export class MenuItem {
  url: string;
  path: string;
  paths: string[];
  children: MenuItem[];
  parent: MenuItem;
  menu?: MenuItemData;
  permissions?: string[];
  permissionsList?: string[][];
  isBrandOnly?: boolean;
  isSuperadmin?: boolean;
  order?: number;

  constructor( menuData, parent? ) {
    const { path, paths, isBrandOnly, permissions, permissionsList, menu, children, isSuperadmin } = menuData;

    this.path = path;
    if (paths) {
      this.paths = paths;
    }

    this.isBrandOnly = isBrandOnly;
    this.permissions = permissions || [];
    this.permissionsList = permissionsList;

    this.isSuperadmin = isSuperadmin;

    if (menu) {
      this.menu = {
        title: menu.title || '',
        icon: menu.icon || '',
        fontIcon: menu.fontIcon || '',
        fontSet: menu.fontSet || '',
        svgIcon: menu.svgIcon || '',
        hide: menu.hide || false,
        order: menu.order || 0,
      };
    }

    if (parent) {
      this.parent = parent;
    }

    this.setupURL();

    if (children) {
      this.children = children.map(( item ) => new MenuItem(item, this));
    }
  }

  get route(): { path: string } {
    return {
      path: this.path,
    };
  }

  get title(): string {
    return this.getDataMenuValue<string>('title');
  }

  get icon(): string {
    return this.getDataMenuValue<string>('icon');
  }

  get fontIcon(): string {
    return this.getDataMenuValue<string>('fontIcon');
  }

  get fontSet(): string {
    return this.getDataMenuValue<string>('fontSet');
  }

  get svgIcon(): string {
    return this.getDataMenuValue<string>('svgIcon');
  }

  private getDataMenuValue<T>( itemName: string ): T {
    if (this.menu && itemName in this.menu) {
      return this.menu[itemName];
    }
  }

  private setupURL() {
    if (!this.paths) {
      this.paths = [...this.getParentPaths(), this.path];
    }
    this.url = `/${this.paths.join('/')}`;
  }

  private getParentPaths(): string[] {
    let parents = [];
    const fetchParent = ( item: MenuItem ) => {
      return item.parent ? item.parent : null;
    };

    const getParentsArray = ( item: MenuItem, result: string[] ) => {
      const parent = fetchParent(item);
      if (parent !== null) {
        result = getParentsArray(parent, [parent.path, ...result]);
      }
      return result;
    };

    return getParentsArray(this, parents);
  }
}
