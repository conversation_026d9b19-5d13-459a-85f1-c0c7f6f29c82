import { BaseApiObject } from '../typings/base';

export class Proxy implements BaseApiObject {
  _meta?: Object;

  id?: string;
  url: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;

  constructor( obj?: any ) {
    if (obj && 'id' in obj) {
      this.id = obj.id;
    }
    this.url = obj && obj.url || '';
    this.description = obj && obj.description || '';
    this.createdAt = obj && obj.createdAt || '';
    this.updatedAt = obj && obj.updatedAt || '';

  }

}
