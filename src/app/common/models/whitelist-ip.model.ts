import { Audit } from '../typings';

export const WHITELIST_TYPES = {
  OWN: 'own',
  PARENT: 'parent',
  DRAFT: 'draft',
};

export interface EntityWhitelistDetailed {
  own: string[];
  parent?: string[];
}

export class WhitelistIp {
  address: string;
  type: string;
  issueId?: string;

  constructor( address: string, type: string ) {
    this.address = address;
    this.type = type;
  }

  isParentItem(): boolean {
    return this.type === WHITELIST_TYPES.PARENT;
  }

  isDraftItem(): boolean {
    return this.type === WHITELIST_TYPES.DRAFT;
  }

  isOwnItem(): boolean {
    return this.type === WHITELIST_TYPES.OWN;
  }

  canBeDeleted(): boolean {
    return this.isOwnItem();
  }

}

export class Whitelist {
  private readonly parent: WhitelistIp[];
  private readonly own: WhitelistIp[] = [];
  private draft: WhitelistIp[] = [];

  constructor( value?: EntityWhitelistDetailed, readonly audits?: Audit[] ) {
    this.parent = (value?.parent ?? []).map(address => new WhitelistIp(address, WHITELIST_TYPES.PARENT));
    this.own = (value?.own ?? []).map(address => new WhitelistIp(address, WHITELIST_TYPES.OWN));
    this.own.forEach(whitelist => {
      const issue = audits.find(( { history: { parameters } } ) => Object.values(parameters).includes(whitelist.address));
      whitelist.issueId = issue?.initiatorIssueId;
    });
  }

  get data(): WhitelistIp[] {
    return this.parent.concat(this.own).concat(this.draft);
  }

  get ownListWith(): string[] {
    return [
      ...this.own,
      ...this.draft
    ].map(( { address } ) => address);
  }

  get draftList(): string[] {
    return this.draft.map(( { address } ) => address);
  }

  isDraftWhitelist(): boolean {
    return this.own.length === 0;
  }

  setDraftAddresses( value: string ) {
    const items = value?.trim().split('\n').map(l => l.trim()) ?? [];
    this.draft = items.map(address => new WhitelistIp(address, WHITELIST_TYPES.DRAFT));
  }
}
