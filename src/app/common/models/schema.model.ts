export interface SchemaField {
  class?: string; // HTML object class
  disabled?: boolean;
  field: string;
  relatedField?: string;
  title: string;
  placeholder?: string;
  type?: string;
  scope?: string;
  data?: any;
  value?: any;
  isList?: boolean;
  isEditable?: boolean;
  isCreate?: boolean;
  isViewable?: boolean;
  isSortable?: boolean;
  sorting?: string; // enum GridOrderDirectionEnum
  isFilterable?: boolean;
  filterMatch?: number;
  sanitizeValue?: boolean;
  labelClass?: boolean;
  isTooltipHidden?: boolean;
  dateOptions?: {
    timeDisableLevel: CalendarTimeDisableLevel,
    hideTimeDisabled: boolean,
  };

  td?: SchemaField;
  th?: SchemaField;
  tag?: SchemaField;
  form?: SchemaField;
  mock?: string;      // Fake data generator ID

  // titleFn( el, schema: SchemaField );
  // classFn( el, schema: SchemaField );

  // Columns management item
  isListVisible?: boolean;
}
