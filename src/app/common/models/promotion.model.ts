import { BaseApiObject, GameInfo, Label } from '../typings';

export type PromoConditionValueOperator = '<' | '<=' | '=' | '>' | '>=';
export type PromoRewardType = 'GGR' | 'NGR' | 'MinMax' | 'PresetAmount';
export type PromoIntervalType = 'daily' | 'weekly' | 'monthly';
export type PromoType = 'rebate' | 'freebet' | 'virtual_money' | 'freebet_simple' | 'bonus_coin';

export interface PromoTypes {
  rebate: string;
  freebet: string;
  virtualMoney: string;
  freebetSimple: string;
}

export const promoTypes: PromoTypes = {
  rebate: 'rebate',
  freebet: 'freebet',
  virtualMoney: 'virtual_money',
  freebetSimple: 'freebet_simple',
};

export interface PromoOwners {
  skywind: string;
  operator: string;
}

export const promoOwners: PromoOwners = {
  skywind: 'skywind',
  operator: 'operator'
};

export const promoExpirationTypes = {
  'IN': 'in',
  'ON': 'on'
};

export const promoStatuses = {
  ACTIVE: 'active',
  INACTIVE: 'inactive'
};

export const promoStates = {
  NEW: 'new', // can't been received from backend (only client-side state while promo creation)
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  FINISHED: 'finished',
  EXPIRED: 'expired'
};

export const promoRewardStatuses = {
  NOT_STARTED: 'not_started',                // promo is not started for player or not rewarded in the wallet
  AWARDED: 'awarded',                        // player received promo in the wallet
  STARTED: 'started',                        // player started playing bonus coins
  EXPIRED: 'expired',                        // promo expired for player without redeemable bonus coins
  REDEEMED: 'redeemed',                      // player redeemed bonus coins
  FINISHED: 'finished',                      // player finished playing bonus coins with 0 amount
  REDEEMABLE_EXPIRED: 'redeemable_expired',   // promo expired for player but there are redeemable bonus coins,
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  REVOKED: 'revoked',
};

export interface PromoIntervalTypes {
  daily: string;
  weekly: string;
  monthly: string;
}

export const promoIntervalTypes: PromoIntervalTypes = {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly'
};

export interface Condition {
  and?: Condition[];
  or?: Condition[];
  value?: number;
  operator?: PromoConditionValueOperator;
  valueField?: string;
}

export interface PromotionInfo {
  id?: string;
  title: string;
  type: string;
  conditions?: Condition;
  archived?: boolean;
  status?: string;
  state?: string;
  everStarted?: boolean;
  brandId?: string;
  totalParticipated?: number;
  totalPayout?: number;
  startDate: string;
  endDate: string;
  createdUserId?: string;
  updatedUserId?: string;
  description: string;
  createdAt?: Date;
  updatedAt?: Date;
  rewards?: CommonRewardData[];
  labels?: Label[];
  createdUsername?: string;

  intervalType: string;
  daysOfWeek?: string[];
  daysOfMonth?: string[];
  timeOfDay?: string;

  customerIds?: string[];

  /**
   * @deprecated ???
   */
  agentsId?: string[];
}

export class Promotion implements PromotionInfo, BaseApiObject {
  _meta?: Object = {};

  id: string;
  title: string;
  type: string;
  conditions: Condition;
  archived: boolean;
  status: string;
  state: string;
  everStarted: boolean;
  brandId?: string;
  totalParticipated: number;
  totalPayout: number;
  startDate: string;
  endDate: string;
  createdUserId: string;
  createdUsername: string;
  updatedUserId: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  rewards: CommonRewardData[];
  startRewardOnGameOpen: boolean;
  labels: Label[];

  // these fields for now are used in rebate but present in general promo model (TBD)
  intervalType: string;
  daysOfWeek: string[];
  daysOfMonth: string[];
  timeOfDay: string;
  customerIds: string[];

  brandPath?: string; // contains operator path if we're creating promo under reseller
  owner?: string; // contains owner if we're creating promo under specific owner

  /**
   * @deprecated
   */
  agentsId: string[];

  constructor( obj?: any ) {
    this.id = obj && obj['id'] || '';
    this.title = obj && obj['title'] || '';
    this.type = obj && obj['type'] || '';
    this.conditions = obj && obj['conditions'] || {};
    this.archived = obj && obj['archived'] || false;
    this.status = obj && obj['status'] || '';
    this.state = obj && obj['state'] || promoStates.NEW;
    this.everStarted = obj && obj['everStarted'] || false;
    this.brandId = obj && obj['brandId'] || '';
    this.totalParticipated = obj && obj['totalParticipated'] || 0;
    this.totalPayout = obj && obj['totalPayout'] || 0;
    this.startDate = obj && obj['startDate'] || '';
    this.endDate = obj && obj['endDate'] || '';
    this.createdUserId = obj && obj['createdUserId'] || '';
    this.createdUsername = obj && obj['createdUsername'] || '';
    this.updatedUserId = obj && obj['updatedUserId'] || '';
    this.description = obj && obj['description'] || '';
    this.createdAt = obj && obj['createdAt'] || '';
    this.updatedAt = obj && obj['createdAt'] || '';
    this.rewards = obj && obj['rewards'] || [];
    this.labels = obj && obj['labels'] || [];

    this.intervalType = obj && obj['intervalType'] || '';
    this.daysOfWeek = obj && obj['daysOfWeek'] || [];
    this.daysOfMonth = obj && obj['daysOfMonth'] || [];
    this.timeOfDay = obj && obj['timeOfDay'] || '';
    this.customerIds = obj && obj['customerIds'] || [];

    this.brandPath = obj && obj['brandPath'] || '';
    this.owner = obj && obj['owner'] || promoOwners.operator;

    this.startRewardOnGameOpen = obj && obj['startRewardOnGameOpen'] || false;
  }

  asCreatePromotionInfo(): CreatePromotionInfo {
    const data: CreatePromotionInfo = {
      title: this.title,
      type: this.type,
      conditions: this.conditions,
      status: this.status,
      state: this.state,
      startDate: this.startDate,
      endDate: this.endDate,
      description: this.description,
      rewards: this.rewards,
      labels: this.labels,
      intervalType: this.intervalType,
      daysOfWeek: this.daysOfWeek,
      daysOfMonth: this.daysOfMonth,
      timeOfDay: this.timeOfDay,
      owner: this.owner,
      startRewardOnGameOpen: this.startRewardOnGameOpen,
    };

    if ('timeOfDay' in data && data['timeOfDay'] === '') {
      delete data['timeOfDay'];
    }

    if ('daysOfWeek' in data && data['daysOfWeek'].length === 0) {
      delete data['daysOfWeek'];
    }

    if ('daysOfMonth' in data && data['daysOfMonth'].length === 0) {
      delete data['daysOfMonth'];
    }

    if (typeof data['owner'] === 'undefined' || data['owner'] === promoOwners.operator) {
      delete data['owner'];
    }

    return data;
  }

  isFinishedOrExpired(): boolean {
    return [promoStates.FINISHED, promoStates.EXPIRED].indexOf(this.state) > -1;
  }

  convertToClone() {
    this.id = '';
    this.createdAt = undefined;
    this.updatedAt = undefined;
    this.createdUserId = '';
    this.updatedUserId = '';
    this.startDate = '';
    this.endDate = '';
    this.state = promoStates.NEW;
    this.status = promoStatuses.INACTIVE;
  }

}

export interface CreatePromotionInfo {
  title: string;
  type: string;
  conditions?: Condition;
  status?: string;
  state?: string;
  startDate: string;
  endDate: string;
  description: string;
  rewards?: CommonRewardData[];
  labels?: Label[];

  intervalType: string;
  daysOfWeek?: string[];
  daysOfMonth?: string[];
  timeOfDay?: string;
  owner?: string;

  startRewardOnGameOpen: boolean;
}

export interface CommonRewardData {
  id?: string;
  promoId?: string;
}

export interface QualifyingGameConfig {
  [gameCode: string]: { coeff: number };
}

export interface FreebetGameConfig {
  gameCode: string;
  coins: FreebetGameCoinConfig[];
}

export interface FreebetGameCoinConfig {
  [field: string]: { coin: number };
}

export interface FreebetGameInfo extends GameInfo {
  coins: FreebetGameCoinConfig[];
}

export interface PromoPlayerInfo {
  _meta: Object;
  playerCode: string;
  status: string;
  expireAt: string;
}

export const applyParticipantTypes = {
  SUCCESS: 'success',
  ERROR: 'error',
};

export const participantCancelled = 'Cancelled';
