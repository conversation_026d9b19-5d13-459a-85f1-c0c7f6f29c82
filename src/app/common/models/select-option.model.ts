export class SelectOptionModel {
  id: string;
  text: string;
  disabled?: boolean;
  data?: any;
  visiblePath?: string;
  label?: string;

  constructor( id: string, text: string, disabled?: boolean, data?: any, path?: string, label?: string ) {
    this.id = id;
    this.text = text;
    this.disabled = disabled;
    this.data = data;
    this.visiblePath = path;
    this.label = label;
  }
}

export interface SelectOptionProvider {
  toSelectOption(): SelectOptionModel;
}
