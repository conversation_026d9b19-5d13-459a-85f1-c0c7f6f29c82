export class Site {
  static STATUS_NORMAL: string = 'normal';
  static STATUS_SUSPENDED: string = 'suspended';
  static STATUS_INFO: string = 'info';

  public id?: string;
  public entityId?: string;
  public title?: string;
  public url: string;
  public status?: string;
  public insertedAt?: Date;
  public operatorSiteGroupName?: string;
  public externalCode?: string;
  public isDefault?: boolean;

  constructor( data: any = {} ) {
    this.id = 'id' in data ? data.id : '';
    this.entityId = 'entityId' in data ? data.entityId : '';
    this.title = 'title' in data ? data.title : '';
    this.url = 'url' in data ? data.url : '';
    this.status = 'status' in data ? data.status : '';
    this.insertedAt = 'insertedAt' in data ? new Date(data['insertedAt']) : null;
    this.operatorSiteGroupName = 'operatorSiteGroupName' in data ? data.operatorSiteGroupName : '';
    this.externalCode = 'externalCode' in data ? data.externalCode : '';
    this.isDefault = 'isDefault' in data ? data.isDefault : false;
  }
}

export interface SiteLog {
  url: string;
  error?: {
    code: number;
    message?: string;
  };
}

export const SITE_STATUS_LIST = [
  { id: Site.STATUS_NORMAL, code: Site.STATUS_NORMAL, displayName: 'Active' },
  { id: Site.STATUS_SUSPENDED, code: Site.STATUS_SUSPENDED, displayName: 'Inactive' },
  { id: Site.STATUS_INFO, code: Site.STATUS_INFO, displayName: 'Info' }
];

export const SITE_STATUS_LIST_TRANSLATE = [
  {
    id: Site.STATUS_NORMAL,
    code: Site.STATUS_NORMAL,
    displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusActive'
  },
  {
    id: Site.STATUS_SUSPENDED,
    code: Site.STATUS_SUSPENDED,
    displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusInactive'
  },
  {
    id: Site.STATUS_INFO,
    code: Site.STATUS_INFO,
    displayName: 'ENTITY_SETUP.WHITELISTING.siteStatusInfo'
  }
];
