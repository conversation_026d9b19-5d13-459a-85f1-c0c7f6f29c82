import { HttpClient, HttpParams, HttpResponse, HttpUrlEncodingCodec } from '@angular/common/http';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, Subject, throwError } from 'rxjs';
import { map, share } from 'rxjs/operators';

import { API_ENDPOINT, API_ENDPOINT2, DEFAULT_PAGE_SIZE, GridOrderDirectionEnum } from '../../app.constants';

import { BaseApiObject, GridFilter, ResponseRequestFormat } from '../typings';

class SwHttpUrlEncodingCodec extends HttpUrlEncodingCodec {

  encodeValue( value: string ): string {
    return encodeURIComponent(value);
  }
}

export abstract class BaseService<T extends BaseApiObject> {

  static apiEndpoint: string = API_ENDPOINT;
  static apiEndpoint2: string = API_ENDPOINT2;

  public urlList: string;
  public urlGet: string;
  public urlSave: string;
  public urlCreate: string;
  public urlRemove: string;

  public _httpResponse: Subject<ResponseRequestFormat>;
  public _item: Subject<T>;
  public _items: Subject<T[]>;

  public serviceName: string = 'Base';
  public reportFilter;
  public reportHeaders;
  public reportFields;
  public requestParams: HttpParams;
  public responseHeaders: ResponseRequestFormat;

  protected constructor( protected notifications: SwuiNotificationsService,
                         protected http: HttpClient
  ) {
    this._item = <Subject<T>>new Subject();
    this._items = <Subject<T[]>>new Subject();
    this._httpResponse = <Subject<ResponseRequestFormat>>new Subject();
  }

  // TODO: start a debate or move to the `lib`
  public static convertDateToBackendValue( val ): any {
    // const res = moment(val).utcOffset(0).unix() * 1000;
    // return res.toString();
    return moment(val).utcOffset(0).toJSON();
  }

  public getResponseParams( response: HttpResponse<T | T[]>, filter?, data? ): ResponseRequestFormat {
    let total = Number(response.headers.get('x-paging-total'));
    let limit = Number(response.headers.get('x-paging-limit'));
    let offset = Number(response.headers.get('x-paging-offset'));
    const date = moment(response.headers.get('date'));
    let currentPage = (Math.ceil(offset / limit) || 0) + 1;
    let isLastPage;
    let hasPagingDetails = true;

    if (filter && !total) {
      hasPagingDetails = false;

      let dataLength = data && data.length;
      offset = (filter.pages.currentPage - 1) * filter.pages.pageSize;
      total = offset + dataLength;
      limit = filter.pages.pageSize;
      const pageCount = filter.pages.pageCount || filter.pages.currentPage;
      if (dataLength && dataLength === limit &&
        ((filter.pages.currentPage <= pageCount || filter.pages.currentPage === 1) ||
          (filter.pages.total === 0 && dataLength >= limit))) {
        isLastPage = false;
        total = total + limit;
      } else if (dataLength < limit) {
        isLastPage = true;
      }
      currentPage = filter.pages.currentPage;
    }

    return { total, limit, offset, date, currentPage, isLastPage, hasPagingDetails };
  }

  public processRecord( record: T ): T {
    record._meta = {};
    return record;
  }

  get item() {
    return this._item.asObservable();
  }

  get items() {
    return this._items.asObservable();
  }

  get httpResponse() {
    return this._httpResponse.asObservable();
  }

  public getUrl( path?, urlList: string = this.urlList ): string {
    return `${BaseService.apiEndpoint}${path && path !== ':' ? '/entities/' + path : ''}${urlList}`;
  }

  public getList( filter?, _?: any ): Observable<T[]> {
    console.log('Base service: get list');

    let fn = this.processRecord.bind(this);

    this.requestParams = BaseService.getRequestParams(filter);
    let path = '';
    if (filter && filter.values) {
      path = filter.values.path;
    }
    let url = this.getUrl(path);

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          let data = response.body;
          this.responseHeaders = this.getResponseParams(response, { pages: {} }, data);
          data = data.map(fn) as T[];
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }


  public getItem( params: any ): Observable<T> {
    console.log(`${this.serviceName}  service: get element`, params.id);
    let fn = this.processRecord.bind(this);

    const source = this.http
      .get<T>(`${BaseService.apiEndpoint}${this.urlGet}${params.id}`, { observe: 'response' }).pipe(
        map(response => {
          this.responseHeaders = this.getResponseParams(response);
          const data = fn(response.body);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public getItemURL( id, _, url ) {
    if (id) {
      return `${BaseService.apiEndpoint}${url}/${id}`;
    } else {
      return `${BaseService.apiEndpoint}${url}`;
    }
  }

  public saveItem( id: string | number, data?: any ) {
    console.log('Base service: save element', id);
    delete data._meta;
    let body = JSON.stringify(data);
    const url = this.getItemURL(id, data, this.urlSave);

    const source = this.http.patch<T>(url, body).pipe(
      map(_data => this.processRecord(_data)),
      share()
    );
    source.subscribe(
      () => null,
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public createItem( data?: any ) {
    console.log('Base service: create element');
    delete data._meta;
    let body = JSON.stringify(data);
    const url = this.getItemURL(false, data, this.urlCreate);
    return this.http.post(url, body);
  }

  public deleteItem( id ) {
    console.log('Base service: delete element', id);
    const url = this.getItemURL(id, {}, this.urlCreate);
    return this.http.delete(url);
  }

  public handleErrors( httpErrorResponse ) {
    console.error(httpErrorResponse);
    const errorBody = httpErrorResponse.error;
    if (errorBody) {
      // this.notifications.translatedError(err);
      this.notifications.error(httpErrorResponse?.error?.message);
    } else {
      this.notifications.error(httpErrorResponse.statusText, `Status: ${httpErrorResponse.status}`);
    }

    return throwError(httpErrorResponse);
  }

  static getRequestParams( filter?: GridFilter, params? ): HttpParams {
    let p: HttpParams = new HttpParams({ encoder: new SwHttpUrlEncodingCodec() });

    if (filter) {
      const { pages, values, ordering } = filter;

      if (values) {
        Object.keys(values).forEach(( key ) => {
          let value = values[key];
          if (value) {
            if (value.toDate) {
              value = BaseService.convertDateToBackendValue(value);
            }
            p = p.has(key) ? p.append(key, value) : p.set(key, value);
          }
        });
      }

      if (pages) {
        // TODO: remove default params, make current page and pageSize mandatory fields
        pages.currentPage = pages.currentPage || 1;
        let limit = pages.pageSize || pages.limit;
        const offset = (pages.currentPage - 1) * limit;
        const shortInfo = pages.shortInfo || false;

        if (offset) {
          p = p.set('offset', offset.toString());
        }
        p = p.set('limit', limit.toString() || DEFAULT_PAGE_SIZE.toString());
        if (shortInfo) {
          p = p.set('shortInfo', shortInfo.toString());
        }
      }

      if (ordering) {
        Object.keys(ordering).forEach(order => {
          p = p.set('sortBy', order.toString());
          let sortOrder = ordering[order];

          if ([GridOrderDirectionEnum.ASC, GridOrderDirectionEnum.DESC].indexOf(sortOrder) !== -1) {
            p = p.set('sortOrder', GridOrderDirectionEnum[sortOrder]);
          }
        });
      }

      if (params) {
        const unique = ['limit', 'offset'];

        Object.keys(params).forEach(( key ) => {
          let value = params[key];
          if (value || value === 0) {
            if (unique.indexOf(key) > -1) {
              if (p.has(key)) {
                p = p.delete(key);
              }
              p = p.set(key, value);
            } else {
              p = p.has(key) ? p.append(key, value) : p.set(key, value);
            }
          }
        });
      }
    }

    return p;
  }

  public stringToObject( str: string ): any {
    return JSON.parse(str.replace(/'(?=\B)|(?=\B)'/g, '\"')); /* replace ' on " (excludes ' in words)*/
  }
}
