import { Pipe, PipeTransform } from '@angular/core';
import { SettingsService } from '@skywind-group/lib-swui';
import { getCurrencyFractionCount } from '../../core/currecy-transform';

@Pipe({ name: 'formattedMoney', pure: true })
export class FormattedMoneyPipe implements PipeTransform {
  public static currencyFormat: string;

  constructor( protected settingsService?: SettingsService ) {
    if (settingsService) {
      FormattedMoneyPipe.currencyFormat = settingsService.appSettings.currencyFormat;
    }
  }

  transform( input: any = 0, fractionCount: number = 2, delimiter: string = ' ', code?: string ): string {
    let result;
    const calcFractionCount = code ? getCurrencyFractionCount(code) : fractionCount;
    if (input === null) {
      result = '';
    } else if (input.toLocaleString && FormattedMoneyPipe.currencyFormat) {
      result = parseFloat(input).toLocaleString(FormattedMoneyPipe.currencyFormat, {
        minimumFractionDigits: calcFractionCount,
        maximumFractionDigits: calcFractionCount,
        useGrouping: !!delimiter,
      });
    } else {
      result = parseFloat(input).toFixed(calcFractionCount).replace(/(\d)(?=(\d{3})+($|\.))/g, '$1' + delimiter);
    }

    return result;
  }
}
