import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TrustUrlPipe } from './trustUrl/trustUrl.pipe';
import { BaProfilePicturePipe } from './baProfilePicture/baProfilePicture.pipe';
import { BaAppPicturePipe } from './baAppPicture/baAppPicture.pipe';
import { BaKameleonPicturePipe } from './baKameleonPicture/baKameleonPicture.pipe';
import { SwColoredNumberPipe } from './swColored/swColored.pipe';
import { FormattedMoneyPipe } from './formatted-money/formatted-money.pipe';
import { HighlightPipe } from './highlight/highlight.pipe';
import { Nl2BrPipe } from './nl2br/nl2br.pipe';
import { FormattedNumberPipe } from './formatted-number/formatted-number.pipe';
import { ObjectKeysPipe } from './objectKeys/object-keys.pipe';
import { BytesPipe } from './swBytes/bytes.pipe';
import { TruncatePipe } from './truncate/truncate.pipe';
import { SanitisePipe } from './sanitise/sanitise.pipe';


@NgModule({
  imports: [
    CommonModule,
  ],
  exports: [
    TrustUrlPipe,
    BaProfilePicturePipe,
    BaAppPicturePipe,
    BaKameleonPicturePipe,
    SwColoredNumberPipe,
    FormattedMoneyPipe,
    HighlightPipe,
    Nl2BrPipe,
    FormattedNumberPipe,
    ObjectKeysPipe,
    BytesPipe,
    TruncatePipe,
    SanitisePipe,
  ],
  declarations: [
    TrustUrlPipe,
    BaProfilePicturePipe,
    BaAppPicturePipe,
    BaKameleonPicturePipe,
    SwColoredNumberPipe,
    FormattedMoneyPipe,
    HighlightPipe,
    Nl2BrPipe,
    FormattedNumberPipe,
    ObjectKeysPipe,
    BytesPipe,
    TruncatePipe,
    SanitisePipe,
  ],
  providers: [],
})
export class PipesModule {
}
