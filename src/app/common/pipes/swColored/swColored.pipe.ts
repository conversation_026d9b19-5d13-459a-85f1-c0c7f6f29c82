import { DomSanitizer } from '@angular/platform-browser';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'coloredNumber' })
export class SwColoredNumberPipe implements PipeTransform {

  constructor( private sanitized: DomSanitizer ) {
  }

  transform( input: number ) { // : SafeHtml
    let cls = input > 0 ? 'text-green' : input < 0 ? 'text-danger' : '';
    return this.sanitized.bypassSecurityTrustHtml(`<span class="${cls}>${input}</span>`);
  }
}
