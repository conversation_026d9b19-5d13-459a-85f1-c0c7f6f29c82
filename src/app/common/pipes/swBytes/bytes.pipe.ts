import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'bytes'
})

export class BytesPipe implements PipeTransform {
  transform(value: number, decimals: number = 2): string {
    if (value === 0) return '0 Bytes';
    const k = 1000;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    // Math magic {
    const i = Math.floor(Math.log(value) / Math.log(k));
    return parseFloat((value / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
    // }
  }
}
