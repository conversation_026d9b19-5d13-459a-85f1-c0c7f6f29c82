import { Component, Input, TemplateRef, ViewContainerRef, ViewChild } from '@angular/core';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

@Component({
  selector: '[translate-template]',
  template: '<ng-container #vc></ng-container>'
})
export class TranslateTemplateComponent {
  @ViewChild('vc', { read: ViewContainerRef, static: true }) vc: ViewContainerRef;

  @Input() en: TemplateRef<any>;
  @Input() cn: TemplateRef<any>;

  language: string;
  fallbackLanguage: string = 'en';

  private sub: Subscription;

  constructor(
    private translate: TranslateService,
  ) {
    this.subscribeToLangChange();
  }

  ngOnInit() {
    this.initCurrentLanguage();
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  private subscribeToLangChange() {
    this.sub = this.translate.onLangChange.subscribe(( event: LangChangeEvent ) => {
      this.changeLanguage(event.lang);
    });
  }

  private changeLanguage( lang: string ) {
    this.language = lang;

    if (this.language in this) {
      this.vc.clear();
      this.vc.createEmbeddedView(this.getTemplate());
    }
  }

  private getTemplate(): TemplateRef<any> {
    let template = this[this.fallbackLanguage];

    if (this.language in this) {
      template = this[this.language];
    }

    return template;
  }

  private initCurrentLanguage() {
    this.changeLanguage(this.translate.currentLang);
  }
}
