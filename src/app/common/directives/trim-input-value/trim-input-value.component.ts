import { Directive, ElementRef, forwardRef, HostListener, Renderer2 } from '@angular/core';
import { DefaultValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

const TRIM_VALUE_ACCESSOR: any = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => TrimInputValueComponent),
  multi: true,
};


@Directive({
  selector: '[trimValue]',
  providers: [TRIM_VALUE_ACCESSOR]
})
export class TrimInputValueComponent extends DefaultValueAccessor {

  constructor( private elRef: ElementRef, renderer: Renderer2 ) {
    super(renderer, elRef, false);
  }

  @HostListener('paste', ['$event'])
  onPaste( $event: ClipboardEvent ) {
    $event.preventDefault();
    const pastedInput: string = $event.clipboardData
      .getData('text/plain').trim();

    const input = this.elRef.nativeElement;
    const { selectionStart, selectionEnd, value } = input;
    const beforeCaret = value.substr(0, selectionStart);
    const afterCaret = value.substr(selectionEnd);
    const newVal = (beforeCaret + pastedInput + afterCaret).trim();
    const endCaretPos = value.length - selectionEnd;

    this.writeValue(newVal);
    input.setSelectionRange(newVal.length - endCaretPos, newVal.length - endCaretPos);
  }

  @HostListener('blur', ['$event.target.value'])
  onBlur( value: any ) {
    this.writeValue(value);
  }

  writeValue( value: any ): void {
    if (typeof value === 'string') {
      value = value.trim();
    }

    this.onChange(value);
    super.writeValue(value);
  }
}
