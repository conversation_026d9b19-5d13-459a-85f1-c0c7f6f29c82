import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';


@Directive({
  selector: '[ifNotAllowedDisable]'
})
export class DisableIfNotAllowedDirective {

  @Input('ifNotAllowedDisable')
  private isAllowed: boolean;

  constructor( private el: ElementRef, private renderer: Renderer2 ) {}

  ngOnChanges() {
    if (!this.isAllowed) {
      this.renderer.setAttribute(this.el.nativeElement, 'disabled', 'disabled');
    } else {
      this.renderer.removeAttribute(this.el.nativeElement, 'disabled');
    }
  }
}
