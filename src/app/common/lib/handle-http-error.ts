import { MonoTypeOperatorFunction, of, throwError } from 'rxjs';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { TranslateService } from '@ngx-translate/core';

export function handleHttpError<T>( notifications: SwuiNotificationsService, body: T ): MonoTypeOperatorFunction<HttpResponse<T>> {
  return catchError(response => {
    const { error, statusText, status } = response;
    const message = error ? error.message : statusText;
    const title = error ? error.error : `Status: ${status}`;
    notifications.error(message, title);
    return of(new HttpResponse<T>({ body }));
  });
}

export function handleErrors( notifications: SwuiNotificationsService, translate: TranslateService ): MonoTypeOperatorFunction<never> {
  return input => input.pipe(catchError(( response: HttpErrorResponse ) => {
    const { error, statusText, status } = response;
    const message = error ? error.message : statusText;
    let title: string;
    if (error) {
      title = `${translate.instant('BACKEND_ERRORS.error_code')}${error.code}`;
    } else {
      title = `${translate.instant('BACKEND_ERRORS.status')}${status}`;
    }
    notifications.error(message, title);
    return throwError(response);
  }));
}
