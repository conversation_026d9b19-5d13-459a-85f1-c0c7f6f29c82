export enum FileType {
  Json = 'json',
  Csv = 'csv',
  Txt = 'txt',
  Html = 'html'
}

export function simulateBrowserDownload( data: any, filename: string, fileType: FileType ): void {
  const a = document.createElement<'a'>('a');
  a.setAttribute('style', 'display:none;');
  a.setAttribute('target', '_blank');
  document.body.appendChild(a);
  a.href = window.URL.createObjectURL(createFileContent(fileType, data));
  a.download = `${filename}.${fileType}`;
  a.click();
  a.remove();
}

function createFileContent( fileType: FileType, data: any ) {
  if (fileType === FileType.Csv) {
    return new Blob(['\ufeff', data], { type: `text/csv` });
  }
  if (fileType === FileType.Txt) {
    return new Blob([JSON.stringify(data, null, 4).replace(/"/g, '')], { type: `text/plain` });
  }
  if (fileType === FileType.Json) {
    return new Blob([JSON.stringify(data, null, 4)], { type: `application/json` });
  }
  return data;
}
