<ng-container *ngIf="value && value.length > 1; else singleVal">
  <div class="widget-list-label">
    {{value[0]}}
    {{ ('COMPONENTS.WIDGET.andMore' | translate: {number: (value.length - 1)}) }}
  </div>
  <button
    mat-icon-button
    [matMenuTriggerFor]="menu">
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
</ng-container>
<ng-template #singleVal>
  <span class="widget-list-label widget-list-label_single"
        [class.widget-list-label_single_disabled]="disabled"
        (click)="onClick(0)">{{value[0]}}</span>
</ng-template>

<mat-menu class="widget-list-menu" #menu="matMenu">
  <div class="widget-list-menu__inner" *ngIf="value && value.length" (click)="disabled && $event.stopPropagation();">
    <div
      class="widget-list-menu__item"
      [class.widget-list-menu__item_disabled]="disabled"
      mat-menu-item
      (click)="onClick(i)"
      *ngFor="let id of value; let i = index">
      {{ id | translate }}
    </div>
  </div>
</mat-menu>
