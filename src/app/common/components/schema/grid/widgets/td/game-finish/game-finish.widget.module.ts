import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { BaIfAllowedModule } from '../../../../../../directives/baIfAllowed/baIfAllowed.module';
import { TDGameFinishWidget } from './game-finish.widget';

@NgModule({
  imports: [
    MatMenuModule,
    TranslateModule,
    CommonModule,
    BaIfAllowedModule
  ],
  exports: [
    TDGameFinishWidget
  ],
  declarations: [
    TDGameFinishWidget
  ]
})
export class GameFinishWidgetModule {

}
