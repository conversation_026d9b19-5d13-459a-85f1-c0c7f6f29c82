import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { TdActionListWidget } from './action-list.widget';

@NgModule({
  imports: [
    CommonModule,
    MatMenuModule,
    TranslateModule.forChild(),
    MatIconModule,
    MatButtonModule
  ],
  exports: [
    TdActionListWidget,
  ],
  declarations: [
    TdActionListWidget,
  ]
})
export class ActionListWidgetModule {
}
