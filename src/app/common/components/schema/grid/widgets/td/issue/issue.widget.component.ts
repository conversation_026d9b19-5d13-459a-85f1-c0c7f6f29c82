import { Component, Inject } from '@angular/core';
import { SWUI_GRID_WIDGET_CONFIG, SwuiGridWidgetConfig } from '@skywind-group/lib-swui';

@Component({
  templateUrl: './issue.widget.html',
})
export class IssueWidgetComponent {
  readonly value: string;
  readonly classData: any;
  readonly url: string;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { value, schema, row }: SwuiGridWidgetConfig<any> ) {
    this.value = schema.td?.titleFn?.(row, schema) ?? value;
    this.classData = schema.td?.classFn?.(row, schema);
    this.url = `https://jira.skywindgroup.com/browse/${this.value}`;
  }
}
