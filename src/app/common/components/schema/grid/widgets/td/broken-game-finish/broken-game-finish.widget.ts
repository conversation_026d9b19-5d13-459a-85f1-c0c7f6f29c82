import { Component, EventEmitter, Inject } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { SWUI_GRID_WIDGET_CONFIG, SwuiGridWidgetConfig, WidgetActionEvent } from '@skywind-group/lib-swui';
import { FinishedOption } from 'src/app/common/typings/finished-option';

const DISPLAYED_GAME_FIELDS = ['forceFinish', 'roundStatistics', 'offlinePayments', 'manualPayments'];

const isAvailable = (finalizationSupport?: string): boolean | undefined => {
  if (finalizationSupport === undefined) {
    return undefined;
  }

  if (finalizationSupport === 'notSupported') {
    return false;
  }

  return DISPLAYED_GAME_FIELDS.some(field => finalizationSupport === field);
};

@Component({
  templateUrl: './broken-game-finish.widget.html',
  styleUrls: ['./broken-game-finish.widget.scss']
})
export class TDBrokenGameFinishWidget {
  optionsList: any[];
  titleFn: any;
  classObj: any;
  permissionsList: string[];
  useTranslate: boolean;
  value: any;
  loading: boolean = false;

  private optionsMap: Object = {};
  private readonly row: any;
  private readonly schema: any;
  private readonly action: EventEmitter<WidgetActionEvent>;

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { value, schema, row, action }: SwuiGridWidgetConfig<any>,
    sanitizer: DomSanitizer,
  ) {
    const titleFn = schema.td.titleFn;
    const classFn = schema.td.classFn;

    this.titleFn = (titleFn && titleFn(row, schema)) || value;
    this.classObj = classFn && classFn(row, schema);
    this.row = row;
    this.value = value;
    this.schema = schema;
    this.action = action;

    this.checkTranslateUsage();

    // TODO: unsafe operation
    if (schema.td && schema.td.sanitizeValue) {
      this.titleFn = sanitizer.bypassSecurityTrustHtml(this.titleFn);
    }

    this.optionsList = this.processOptionList(schema.td.optionsList);
    this.permissionsList = this.optionsList.reduce(( all, option ) => [...all, ...option.permissions], []);
    this.optionsList.forEach(option => this.optionsMap[option.title] = option);
  }

  statusClick( $event, option ) {
    $event.preventDefault();
    this.loading = true;
    const data = {
      field: this.schema.field,
      row: this.row,
      payload: {
        status: option.id,
        onCompleteFn: () => {
          this.loading = false;
        }
      }
    };
    this.action.emit(data);
  }

  private processOptionList( list: FinishedOption[] ): FinishedOption[] {
    switch (this.row.status) {
      case 'unfinished':
        return list.filter(( { id } ) => id !== 'retryPending' && id !== 'requireTransferOut'
          && (id !== 'finalize' || isAvailable(this.row.finalizationSupport)
          || (isAvailable(this.row.finalizationSupport) === undefined && isAvailable(this.row.entityFinalizationSupport))));
      case 'requireTransferOut':
        return list.filter(( { id } ) => id !== 'retryPending' && id !== 'revert' && id !== 'finalize');
      case 'brokenIntegration':
        return list.filter(( { id } ) => id !== 'finalize');
      default:
        return list;
    }
  }

  private checkTranslateUsage() {
    this.useTranslate = true;

    if ('td' in this.schema && 'useTranslate' in this.schema.td) {
      this.useTranslate = this.schema.td.useTranslate;
    }
  }
}

