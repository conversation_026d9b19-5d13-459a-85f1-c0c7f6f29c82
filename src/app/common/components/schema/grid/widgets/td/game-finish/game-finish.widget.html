<span class="widget">
  <ng-template #isFinished>
    <i class="widget icon-checkmark3 text-green"></i>
  </ng-template>
  <ng-container *ngIf="!value; else isFinished">
    <ng-container *ifAllowed="permissionsList">
      <ng-container *ngIf="!loading; else loadingBlock">
      <div class="btn-group">
        <a class="dropdown-toggle link" [ngClass]="classObj" href="#" (click)="false"
           [matMenuTriggerFor]="menu">
          <ng-container  *ngIf="useTranslate">
            {{ titleFn || '--' | translate }}
          </ng-container>
          <ng-container  *ngIf="!useTranslate">
            {{ titleFn || '--' }}
          </ng-container>
          <span class="caret"></span>
        </a>
        <mat-menu #menu="matMenu" xPosition="before">
          <ng-container *ngFor="let option of optionsList">
            <button mat-menu-item *ifAllowed="option.permissions" (click)="statusClick($event, option)">
              <ng-container *ngIf="useTranslate">
                {{ option.title || '--' | translate }}
              </ng-container>
              <ng-container *ngIf="!useTranslate">
                {{ option.title || '--' }}
              </ng-container>
            </button>
          </ng-container>
        </mat-menu>
      </div>
      </ng-container>
    </ng-container>
    <ng-template #loadingBlock>{{'COMPONENTS.GRID.LOADING' | translate}}</ng-template>
  </ng-container>
</span>
