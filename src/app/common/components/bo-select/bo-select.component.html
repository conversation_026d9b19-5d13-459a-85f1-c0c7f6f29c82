<div class="bo-select" dropdown [isDisabled]="disabled">
  <button [disabled]="disabled" dropdownToggle (click)="onButtonClick($event)"
          class="bo-select__button dropdown-toggle btn btn-default bg-white" [ngClass]="{'disabled':disabled}">
    <ng-container *ngIf="useTranslate; else tplNoTranslateBtn">
      {{innerValue ? (item?.text | translate) : (placeholder | translate)}}
    </ng-container>
    <ng-template #tplNoTranslateBtn>
      {{innerValue ? item?.text : (placeholder | translate)}}
    </ng-template>
    <ng-container *ngIf="innerValue && showCloseButton">
      <i class="icon-cross2 bo-select__cancel" (click)="onCancelClick()"></i>
    </ng-container>
  </button>

    <div
      *dropdownMenu
      class="bo-select__dropdown dropdown-menu"
      role="menu">
      <div
        class="bo-select__search"
        *ngIf="showSearch"
        (click)="$event.stopPropagation()">
        <div class="form-group has-feedback">
          <input tabindex="-1" trimValue
            type="text"
            class="form-control"
            placeholder="{{'COMPONENTS.BO_SELECT.placeholderSearch' | translate}}"
            [(ngModel)]="searchText"
          >
          <div class="form-control-feedback">
            <i class="icon-search4 text-size-base text-muted"></i>
          </div>
        </div>
      </div>
      <div
        class="bo-select__list list-group no-border"
        [ngStyle]="{'max-height': dropdownHeight+'px'}">
        <ng-container *ngIf="useTranslate; else tplNoTranslateItem">
          <div
            class="bo-select__item list-group-item"
            role="menuitem"
            *ngFor="let itemData of processedData | boSelectFilter : searchText"
            [title]="itemData.text | translate"
            [ngClass]="{'disabled' : itemData.disabled}"
            (click)="handleClick(itemData)">
            {{itemData.text | translate}}
          </div>
        </ng-container>
        <ng-template #tplNoTranslateItem>
          <div
            class="bo-select__item list-group-item"
            role="menuitem"
            *ngFor="let itemData of processedData | boSelectFilter : searchText"
            [title]="itemData.text"
            [ngClass]="{'disabled' : itemData.disabled}"
            (click)="handleClick(itemData)">
            {{itemData.text}}
          </div>
        </ng-template>

      </div>

    </div>

</div>
