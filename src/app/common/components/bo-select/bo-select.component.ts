import { Component, forwardRef, HostBinding, HostListener, Input, ViewEncapsulation } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

import { SelectOptionModel } from '../../models/select-option.model';

@Component({
  selector: 'bo-select',
  encapsulation: ViewEncapsulation.None,
  templateUrl: './bo-select.component.html',
  styleUrls: ['./bo-select.component.styl'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => BoSelectComponent),
      multi: true
    }
  ],
})

export class BoSelectComponent implements ControlValueAccessor {
  @Input() textKey: string;

  @Input() dropdownHeight: number;
  @Input() showSearch: boolean = false;
  @Input() useTranslate: boolean = false;
  @Input() showCloseButton: boolean = true;

  @HostBinding('attr.tabindex')
  public tabindex = 0;

  public processedData: SelectOptionModel[] = [];
  public searchText: string;
  public innerValue: any = '';
  public item: any;
  public disabled: boolean = false;

  private _placeholder = 'COMPONENTS.BO_SELECT.placeholder';
  private _onChange: ( _: any ) => void = (() => {
  });

  @Input()
  set placeholder( val: string ) {
    if (!val) {
      return;
    }
    this._placeholder = val;
  }

  get placeholder(): string {
    return this._placeholder;
  }

  @Input('data')
  set dataSetter( data: any[] ) {
    if (!data) {
      return;
    }
    this.processedData = this.processData(data);
    this.setItemIfNeed();
  }

  @Input()
  set value( value: any ) {
    this.searchText = '';

    if (value !== this.innerValue) {
      this.innerValue = value;
      this.setItemIfNeed();
    }
  }

  get value(): any {
    return this.innerValue;
  }

  @HostListener('blur') onblur() {
    this._onTouched();
  }

  writeValue( value: any ) {
    this.searchText = '';

    if (value !== this.innerValue) {
      this.innerValue = value;
      this.setItemIfNeed(false);
    }
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this._onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this._onTouched = fn;
  }

  processData( data: any[] ): any {
    return data.map(item => ({
        text: this.textKey !== undefined ? item[this.textKey] : item.name || item.text || item.title,
        id: item.id,
        disabled: item.disabled,
      })
    );
  }

  setDisabledState( disabled: boolean ) {
    this.disabled = disabled;
  }

  handleClick( processedItem: any ): void {
    if (processedItem && !processedItem.disabled) {
      this.item = processedItem;
      this.innerValue = processedItem.id;
      this._onChange(this.innerValue);
    }
  }

  onButtonClick( event: Event ) {
    event.preventDefault();
  }

  public onCancelClick() {
    if (this.disabled) return;

    this.innerValue = '';
    this.searchText = '';
    this._onChange(this.innerValue);
  }

  private _onTouched: any = () => {
  }

  private setItemIfNeed( emitEvent = true ): void {
    if (this.innerValue) {
      const processedItem = this.processedData.find(i => i.id === this.innerValue);
      if (processedItem) {
        this.item = processedItem;
        this.innerValue = processedItem.id;
        if (emitEvent) {
          this._onChange(this.innerValue);
        }
      }
    }
  }
}
