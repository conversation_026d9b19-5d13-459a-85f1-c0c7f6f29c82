import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'boSelectFilter'
})
export class BoSelectFilterPipe implements PipeTransform {
  transform( items: any[], searchText: string ): any[] {
    if (!items ) return [];
    if (!searchText ) return items;
    searchText = searchText.toLowerCase();
    return items.filter( it => {
      return it.text.toLowerCase().includes(searchText);
    });
  }
}
