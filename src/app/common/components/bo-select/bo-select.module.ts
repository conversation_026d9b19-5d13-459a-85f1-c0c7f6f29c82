import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TranslateModule } from '@ngx-translate/core';
import { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';

import { BoSelectComponent } from './bo-select.component';
import { BoSelectFilterPipe } from './bo-select-filter.pipe';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        BsDropdownModule.forRoot(),
        TranslateModule,
        TrimInputValueModule,
    ],
  exports: [BoSelectComponent],
  declarations: [
    BoSelectComponent,
    BoSelectFilterPipe,
  ],
  providers: [],
})
export class BoSelectModule {
}
