import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient, HttpParams, HttpUrlEncodingCodec } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { first, tap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

// import { AuthService } from '../../auth/auth.service';
import { EntityService } from '../../services/entity.service';
import { Entity } from '../../typings';
import { ServerConfig } from '../../typings/server-config';
import { SwHubAuthService } from '@skywind-group/lib-swui';

class UrlEncoder extends HttpUrlEncodingCodec {
  encodeValue( value: string ): string {
    return encodeURIComponent(value);
  }
}

@Component({
  selector: 'live-chat',
  styleUrls: ['./live-chat.styl'],
  templateUrl: 'live-chat.html'
})
export class LiveChatComponent implements OnInit {
  public licence: string;
  public isAuthorized: Boolean = false;
  private brief: Entity | null;

  constructor(
    private authService: SwHubAuthService,
    private router: Router,
    private entityService: EntityService<Entity>,
    private translateService: TranslateService,
    private http: HttpClient,
  ) {
  }

  ngOnInit(): void {
    this.http.get<ServerConfig>('/api/config').subscribe(value => this.licence = value.liveChatLicence);
    this.isAuthorized = this.authService.isLogged();
  }

  public onClick() {
    this.getBrief().subscribe(brief => this.open(brief), () => this.open());
  }

  private open( brief?: Entity | null ) {
    const username = this.authService.username || '';
    // const { username = '' } = profile;
    const { key = '', name = '' } = brief || {};

    const query = new HttpParams({ encoder: new UrlEncoder() })
      .set('name', username)
      .set('groups', '0')
      .set('lang', this.translateService.currentLang)
      .set('params', new HttpParams({ encoder: new UrlEncoder() })
        .set('url', this.router.url)
        .set('brief.key', key)
        .set('brief.name', name)
        .set('username', username)
        .toString())
      .toString();

    const url = `https://secure.livechatinc.com/licence/${this.licence}/open_chat.cgi?${query}`;
    window.open(url, `LiveChat_${username}`, 'width=530,height=520,resizable=yes,scrollbars=no');
  }

  private getBrief(): Observable<Entity | null> {
    if (!this.authService.isLogged()) {
      return of(null);
    }
    if (this.brief) {
      return of(this.brief);
    } else {
      return this.entityService.getBrief().pipe(
        first(),
        tap(brief => this.brief = brief)
      );
    }
  }
}
