import { Component, HostBinding, Input, ViewEncapsulation } from '@angular/core';
import { SwDexieService } from '@skywind-group/lib-swui';

@Component({
  selector: 'ba-card',
  templateUrl: './baCard.html',
  encapsulation: ViewEncapsulation.None
})
export class BaCardComponent {
  @Input() title: string;
  @Input() componentId: string;
  @Input() baCardClass: string;
  @Input() isCollapsible: Boolean = false;
  @HostBinding('class.collapsed-panel') isCollapsed: boolean = false;

  constructor( private dexieService: SwDexieService ) {

  }

  ngOnInit() {
    this.dexieService.getComponentState(this.componentId, 'ba-card').then(res => {
      this.isCollapsed = res && res.data && res.data.collapsed;
    });
  }

  public toggleCollapsed( event ) {
    event.preventDefault();
    this.isCollapsed = !this.isCollapsed;
    this.dexieService.updateComponentState(this.componentId, 'ba-card', { collapsed: this.isCollapsed });
  }
}
