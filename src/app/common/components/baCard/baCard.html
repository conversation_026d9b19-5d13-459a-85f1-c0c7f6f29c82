<div class="panel panel-flat">
  <div class="panel-heading">
    <h5 class="panel-title" *ngIf="title">{{title}}</h5>
    <ng-content select=".ba-card-title"></ng-content>
    <div class="heading-elements" *ngIf="isCollapsible">
    <ul class="icons-list">
    <li (click)="toggleCollapsed($event)"><a data-action="collapse"></a></li>
    <!--<li><a data-action="reload"></a></li>-->
    <!--<li><a data-action="close"></a></li>-->
    </ul>
    </div>
  </div>

  <div class="panel-body" *ngIf="!isCollapsed">
    <div baCardBlur class="animated fadeIn card {{baCardClass || ''}}" zoom-in>
      <!--<div *ngIf="title" class="card-header clearfix">-->
      <!--<h3 class="card-title">{{title}}</h3>-->
      <!--</div>-->
      <div class="card-body">
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</div>
