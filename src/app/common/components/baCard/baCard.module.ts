import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BaCardComponent } from './baCard.component';
import { BaCardBlurHelper } from './baCardBlurHelper.service';
import { BaCardBlur } from './baCardBlur.directive';
import { BaThemeConfigProvider } from '../../../theme';

@NgModule({
  imports: [
    CommonModule,
  ],
  exports: [BaCardComponent],
  declarations: [
    BaCardBlur,
    BaCardComponent,
  ],
  providers: [
    BaCardBlurHelper,
    BaThemeConfigProvider,
  ],
})
export class BaCardModule {
}

