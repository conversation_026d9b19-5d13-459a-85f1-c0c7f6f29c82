import { Injectable } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Observable, zip } from 'rxjs';
import { map, share } from 'rxjs/operators';
import { PERMISSIONS_NAMES } from '../../../app.constants';
import { User } from '../../../pages/users/user.model';
import { RoleService } from '../../services/role.service';
import { UserService } from '../../services/user.service';

@Injectable()
export class UserEditorService {

  private rolesHash;

  constructor(
    private authService: SwHubAuthService,
    private userService: UserService<User>,
    private roleService: RoleService,
  ) {
  }


  fetchAvailableRoles(forceFetchRoles: boolean = false): Observable<any[]> {
    return zip(
      this.roleService.getRoles(forceFetchRoles),
      this.userService.getCurrentUserProfile()
    ).pipe(
      map(( [roles, currentUser] ) => {
        if (!this.rolesHash || forceFetchRoles) {
          this.rolesHash = roles.reduce(( hash, item ) => {
            hash[item.id] = item;
            return hash;
          }, {});
        }
        return [roles, this.rolesHash, currentUser];
      }),
      share()
    );
  }

  hasCreateEditRoleAccess( keyentity: boolean = false ): boolean {
    const permissions = [PERMISSIONS_NAMES.ROLE_CREATE, PERMISSIONS_NAMES.ROLE_EDIT];
    const keyentityPermissions = [PERMISSIONS_NAMES.KEYENTITY_ROLE_CREATE, PERMISSIONS_NAMES.KEYENTITY_ROLE_EDIT];
    const required = keyentity ? keyentityPermissions : permissions;
    return this.authService.allowedTo(required);
  }
}
