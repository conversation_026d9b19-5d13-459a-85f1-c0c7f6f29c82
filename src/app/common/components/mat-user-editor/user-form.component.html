<form [formGroup]="form">
  <mat-form-field
    class="full-width"
    appearance="outline"
    *ngIf="entityOptions?.length > 0">
    <lib-swui-select
      formControlName="entity"
      placeholder="{{ 'INTEGRATIONS.selectOperator' | translate }}"
      [disableEmptyOption]="true"
      [showSearch]="entityOptions?.length > 10"
      [data]="entityOptions">
    </lib-swui-select>
    <mat-error>
      <lib-swui-control-messages
        [messages]="messageErrors"
        [control]="entityControl">
      </lib-swui-control-messages>
    </mat-error>
  </mat-form-field>
  <ng-container *ngIf="entityOptionsLoading$ | async as load">
    <mat-label>{{ 'ALL.IN_PROGRESS' | translate}} {{load}} qqqq</mat-label>
    <mat-progress-spinner
      [value]="50"
      [diameter]="20"
      [mode]="'indeterminate'">
    </mat-progress-spinner>
  </ng-container>

  <div fxLayout="row">
    <mat-form-field fxFlex="50" appearance="outline" class="mr-10">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.firstname' | translate }}:</mat-label>
      <input matInput trimValue autocomplete="off"
             formControlName="firstName"
             [placeholder]="'ENTITY_SETUP.USERS.MODALS.firstname' | translate"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="firstNameControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field fxFlex="50" appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.lastname' | translate }}:</mat-label>
      <input matInput trimValue autocomplete="off"
             formControlName="lastName"
             [placeholder]="'ENTITY_SETUP.USERS.MODALS.lastname' | translate"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="lastNameControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <div fxLayout="row">
    <mat-form-field fxFlex="50" appearance="outline" class="mr-10">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.username' | translate }}:</mat-label>
      <input matInput trimValue autocomplete="off"
             formControlName="username"
             [placeholder]="'ENTITY_SETUP.USERS.MODALS.username' | translate"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="usernameControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field fxFlex="50" appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.password' | translate }}:</mat-label>
      <input matInput trimValue autocomplete="off"
             formControlName="password"
             [placeholder]="'ENTITY_SETUP.USERS.MODALS.password' | translate"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="passwordControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <div fxLayout="row">
    <mat-form-field fxFlex="50" appearance="outline" class="mr-10">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.email' | translate }}:</mat-label>
      <input matInput trimValue autocomplete="off"
             formControlName="email"
             [placeholder]="'ENTITY_SETUP.USERS.MODALS.email' | translate"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="emailControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field fxFlex="50" appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.phoneNumber' | translate }}:</mat-label>
      <input matInput trimValue autocomplete="off"
             formControlName="phone"
             [placeholder]="'ENTITY_SETUP.USERS.MODALS.phoneNumber' | translate"/>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="phoneControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <div fxLayout="row">
    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.roles' | translate}}</mat-label>
      <lib-swui-chips-autocomplete
        formControlName="roles"
        [items]="roleSelectOptions">
      </lib-swui-chips-autocomplete>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="rolesControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <ng-template #rolesLoading>
      <mat-label>{{ 'ALL.IN_PROGRESS' | translate}}</mat-label>
      <mat-progress-spinner
        [value]="50"
        [diameter]="40"
        [mode]="'indeterminate'">
      </mat-progress-spinner>
    </ng-template>
  </div>

  <div fxLayout="row">
    <mat-form-field *ngIf="editMode" appearance="outline"  class="width100">
      <mat-label title="This roles can not be changed">
        {{ 'ENTITY_SETUP.USERS.MODALS.additionalroles' | translate}}
      </mat-label>
      <lib-swui-chips-autocomplete
        [disabled]="true"
        [value]="unsharedRoles">
      </lib-swui-chips-autocomplete>
    </mat-form-field>
  </div>

  <div fxLayout="row">
    <mat-form-field fxFlex="50" appearance="outline">
      <mat-label>{{ 'ENTITY_SETUP.USERS.MODALS.status' | translate}}</mat-label>
      <mat-select [(value)]="userStatus" formControlName="status">
        <mat-option *ngFor="let status of statuses" [value]="status.code">
          {{ status.displayName | translate }}
        </mat-option>
      </mat-select>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="statusControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <div fxLayout="row" fxLayout="start center">
    <h3>{{ 'ENTITY_SETUP.USERS.MODALS.userType' | translate }}:</h3>
  </div>

  <div fxLayout="row">
    <mat-radio-group formControlName="userType" fxFlex="100">
      <mat-card>
        <mat-radio-button [value]="'bo'">
          {{ 'ENTITY_SETUP.USERS.userTypeBO' | translate }}
        </mat-radio-button>

        <div>
          <mat-form-field fxFlex="50" appearance="outline">
            <mat-label>{{ 'ENTITY_SETUP.USERS.boUserPasswordExpires' | translate }}</mat-label>
            <input matInput type="number" size="2" formControlName="forcePasswordChangePeriod"/>
            <mat-error>
              <lib-swui-control-messages
                [messages]="messageErrors"
                [control]="form.get('forcePasswordChangePeriod')">
              </lib-swui-control-messages>
            </mat-error>
          </mat-form-field>

          <mat-select formControlName="forcePasswordChangePeriodType" fxFlex="20" style="padding: 14px 0 0 10px">
            <mat-option *ngFor="let periodType of changePasswordPeriodTypes"
                        [value]="periodType.id">
              {{ periodType.displayName | translate }}
            </mat-option>
          </mat-select>
        </div>
      </mat-card>

      <mat-card style="margin: 10px 0 10px 0">
        <mat-radio-button [value]="'operator_api'">
          {{ 'ENTITY_SETUP.USERS.userTypeOperatorAPI' | translate }}
        </mat-radio-button>
      </mat-card>
    </mat-radio-group>

    <mat-error>
      <lib-swui-control-messages
        [messages]="messageErrors"
        [control]="userTypeControl">
      </lib-swui-control-messages>
    </mat-error>
  </div>
</form>
