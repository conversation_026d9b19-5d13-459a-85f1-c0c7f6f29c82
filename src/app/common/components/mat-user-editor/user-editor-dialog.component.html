<h2 mat-dialog-title>
  <ng-container *ngIf="submittedData">
    {{ (isEdit ? 'ENTITY_SETUP.USERS.MODALS.userModified' : 'ENTITY_SETUP.USERS.MODALS.userCreated') | translate }}
  </ng-container>
  <ng-container *ngIf="!submittedData">
    {{ (data.user?.updatedAt ? 'ENTITY_SETUP.USERS.MODALS.editUser' : 'ENTITY_SETUP.USERS.MODALS.createUser') | translate }}
  </ng-container>
</h2>

<mat-dialog-content class="mat-typography">
  <ng-container *ngIf="!submittedData && data.entity && data.user">

    <user-form [user]="data.user" [brief]="data.brief" [entity]="data.entity" [excludedEmails]="excludedEmails"
               [excludedPasswords]="excludedPasswords" [backendErrorMessages]="backendErrorMessages"
               (formSubmitted)="onFormSubmitted($event)">
    </user-form>

  </ng-container>

  <ng-container *ngIf="submittedData && !isEdit">
    <dl>
      <dt>{{ 'ENTITY_SETUP.USERS.MODALS.secretKey' | translate }}</dt>
      <dd>{{ entityInfo?.key }}</dd>
      <dt>{{ 'ENTITY_SETUP.USERS.username' | translate }}</dt>
      <dd>{{ submittedData.username }}</dd>
      <dt>{{ 'ENTITY_SETUP.USERS.MODALS.password' | translate }}</dt>
      <dd>{{ submittedData.password }}</dd>
    </dl>
  </ng-container>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button *ngIf="!submittedData" mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="saveChanges($event)">
    <i class="icon-spinner2 spinner" *ngIf="loading"></i>
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
