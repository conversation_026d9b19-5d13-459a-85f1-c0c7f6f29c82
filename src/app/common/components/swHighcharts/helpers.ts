import { ChartSchemaField } from './highcharts.component';
import * as moment from 'moment';
import { FORMAT_DATE_MONTH_SHORT } from '../../../app.constants';

export const setDefaultValues = ( schemaFields: ChartSchemaField[] ): ChartSchemaField[] => {
  const availableFields: string[] = schemaFields.map(el => el.configField);

  if (availableFields.indexOf('title') === -1) {
    schemaFields = [...schemaFields, {
      configField: 'title',
      configValue: {
        text: ''
      }
    }];
  }

  return schemaFields;
};


export const moneyFormatter = ( num: number, digits: number = 1 ) => {
  let formats = [
    { value: 1E9, symbol: 'B' },
    { value: 1E6, symbol: 'M' },
    { value: 1E3, symbol: 'k' }
  ], rx = /\.0+$|(\.[0-9]*[1-9])0+$/;

  for (let i = 0; i < formats.length; i++) {
    if (num >= formats[i].value) {
      return (num / formats[i].value).toFixed(digits).replace(rx, '$1') + formats[i].symbol;
    }
  }

  return num.toFixed(digits).replace(rx, '$1');
};


export const latestMonths = ( monthsNum: number = 12 ): string[] => {
  let result = [];

  for (let i = monthsNum; i > 0; i--) {
    result.push(moment().set('day', 1).subtract(i, 'month').format(FORMAT_DATE_MONTH_SHORT));
  }

  return result;
};
