

export class SeriesConfigItem {
  data: any[];
  name: string;
}

export class TooltipConfig {
  headerFormat?: string;
  pointFormat?: string;
  footerFormat?: string;
  shared?: boolean;
  useHTML?: boolean;
}

export class AxisConfig {
  categories?: string[];
  crosshair?: boolean;
  min?: number;
  max?: number;
  title?: Object;
}


export class HighchartsConfig {
  series?: SeriesConfigItem[];
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
  tooltip?: TooltipConfig;
  plotOptions?: {};
}
