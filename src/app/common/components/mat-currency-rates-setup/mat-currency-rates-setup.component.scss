.rates {
  &__header {
    display: flex;
    align-items: center;
    padding: 4px 0 4px 0;
    background: #fff;

    button {
      margin-left: auto
    }
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
  }

  &__body {
    padding: 4px 16px 16px 0;
    background: #fff;
  }

  &__item {
    padding: 10px 0 10px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.12);

    &:last-of-type {
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
}

.currencies {
  &__add {
    margin-bottom: 16px;

    &--bottom {
      padding: 0 8px;
      margin-bottom: 0;
      margin-top: 8px;
    }
  }
}
