import { NgModule } from '@angular/core';

import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';
import { MatCurrencyRatesSetupComponent } from './mat-currency-rates-setup.component';
import { MatCurrencyRateItemComponent } from './currency-rate-item/mat-currency-rate-item.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { ControlMessagesModule } from '../control-messages/control-messages.module';

@NgModule({
    imports: [
        CommonModule,
        TooltipModule.forRoot(),
        ReactiveFormsModule,
        FormsModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        MatInputModule,
        MatSelectModule,
        TranslateModule.forChild(),
        ControlMessagesModule,
        TrimInputValueModule,
    ],
  exports: [
    MatCurrencyRatesSetupComponent
  ],
  declarations: [
    MatCurrencyRatesSetupComponent,
    MatCurrencyRateItemComponent,
  ],
  providers: [],
})
export class MatCurrencyRatesSetupModule {
}
