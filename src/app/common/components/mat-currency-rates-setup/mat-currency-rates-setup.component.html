<div class="rates">
  <div class="rates__header">
    <div class="rates__title">{{ 'ENTITY_SETUP.ENGAGEMENT.BONUS_COIN_SETTINGS.conversionRate' | translate }}</div>
  </div>
  <div class="rates__body">
    <div class="rates__item" *ngFor="let rateForm of ratesControls;">
      <mat-currency-rate-item
        [currencies]="currencies"
        [selected]="selected"
        [setupCurrency]="setupCurrency"
        [rateForm]="rateForm"
        (rateRemove)="removeRate(rateForm)">
      </mat-currency-rate-item>
    </div>
    <button mat-button
            color="primary"
            class="currencies__add currencies__add--bottom"
            (click)="addRate($event)"
            matTooltip="'Add new currency rate'">
      <mat-icon>add</mat-icon>
      Add Currency
    </button>
  </div>
</div>
