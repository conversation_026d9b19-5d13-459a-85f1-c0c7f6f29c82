.rate-item {
  display: flex;
  flex-flow: row nowrap;

  &--reference {
    display: block;
    min-width: 80px;
    height: 40px;
    margin: 3px;
    line-height: 42px;
  }

  &--value {
    min-width: 150px;
  }

  &--curcode {
    width:100px;
    margin-left:5px;
  }

  .btn-remove {
    opacity: 0.3;

    &:hover {
      opacity: 1;
    }
  }

  @media (max-width 500px) {
    flex-direction: column;
    label {
      margin-bottom: 20px;
    }
    input {
      margin-bottom: 20px;
    }
  }
}
