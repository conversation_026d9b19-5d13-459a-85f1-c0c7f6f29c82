<div [formGroup]="rateForm" class="rate-item" *ngIf="rateForm">

  <span class="rate-item--reference">1.00 {{ setupCurrency }} = </span>

  <mat-form-field class="rate-item--value no-field-padding" appearance="outline">
    <input matInput type="number" formControlName="value" min="0.01">
    <mat-error>
      <control-messages [control]="rateForm.get('value')"></control-messages>
    </mat-error>
  </mat-form-field>

  <mat-form-field class="rate-item--curcode no-field-padding" appearance="outline">
    <mat-select formControlName="currency">
      <mat-option *ngFor="let curcode of currencies" value="{{ curcode }}" [disabled]="alreadySelected(curcode) || curcode === 'BNS'">
        {{ curcode }}
      </mat-option>
    </mat-select>
    <mat-error>
      <control-messages [control]="rateForm.get('currency')"></control-messages>
    </mat-error>
  </mat-form-field>

  <button mat-icon-button (click)="removeRate($event)" class="btn-remove" matTooltip="Remove this currency rate">
    <mat-icon>close</mat-icon>
  </button>
</div>
