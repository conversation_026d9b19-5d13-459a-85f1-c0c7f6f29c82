import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';

import { TranslateModule } from '@ngx-translate/core';
import { BoConfirmationComponent } from './bo-confirmation.component';
import { MatButtonModule } from '@angular/material/button';
import { FlexLayoutModule } from '@angular/flex-layout';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    FlexLayoutModule,
    MatDialogModule
  ],
  exports: [BoConfirmationComponent],
  declarations: [BoConfirmationComponent],
  providers: [],
  entryComponents: [BoConfirmationComponent]
})
export class BoConfirmationModule {
}
