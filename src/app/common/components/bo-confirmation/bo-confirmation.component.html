<div mat-dialog-content class="padding24">
  {{ data.message | translate : data?.params }}
</div>
<div mat-dialog-actions fxLayout="row" fxLayoutAlign="center center">
  <button mat-stroked-button color="primary"
          style="margin-right: 16px"
          class="mat-button-md"
          (click)="onNoClick()">{{ 'DIALOG.no' | translate }}</button>
  <button mat-flat-button color="primary" class="mat-button-md"
          (click)="onConfirmClick()">{{ 'DIALOG.yes' | translate }}</button>
</div>
