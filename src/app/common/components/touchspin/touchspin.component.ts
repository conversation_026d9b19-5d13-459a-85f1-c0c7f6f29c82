import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, forwardRef, Input, ViewChild
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'touchspin',
  templateUrl: './touchspin.component.html',
  styleUrls: ['./touchspin.component.styl'],
  providers: [
    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => TouchspinComponent), multi: true }
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TouchspinComponent implements ControlValueAccessor {
  @ViewChild('input', { static: true }) public input: ElementRef;

  @Input() public prefix: string;
  @Input() public postfix: string;

  @Input() public min: number = 0;
  @Input() public max: number = 100;
  @Input() public step: number = 1;
  @Input() public fractionDigits: number = 0;
  @Input() public defaultValue: number = 0;

  public isDisabled: boolean;
  public downLabel: string = '-';
  public upLabel: string = '+';

  private propagateChange: any;
  private _value: number = this.defaultValue;

  @Input()
  public set value( value: number ) {
    value = this.sanitizeValue(value);
    this._value = value;
    this.input.nativeElement.value = this._value;

    if (typeof this.propagateChange === 'function') {
      this.propagateChange(this._value);
    }
    this.cdr.detectChanges();
  }

  public get value(): number {
    return this._value;
  }

  constructor( private cdr: ChangeDetectorRef
  ) {
    // this.cdr.detach();
  }

  ngOnInit() {
    this.cdr.detectChanges();
  }

  writeValue( value ) {
    this.value = value;
  }

  registerOnChange( fn ) {
    this.propagateChange = fn;
  }

  registerOnTouched() {
  }

  valueDown( event ) {
    event.preventDefault();
    this.value -= this.step;
  }

  valueUp( event ) {
    event.preventDefault();
    this.value += this.step;
  }

  valueChanged( { target } ) {
    this.value = ('value' in target) ? (target as HTMLInputElement).value : this.input.nativeElement.value;
  }

  setDisabledState( isDisabled: boolean ) {
    this.isDisabled = isDisabled;
  }

  private sanitizeValue( value: number ): number {

    let parsedValue;
    if (typeof value === 'string') {
      const str: string = value;
      const afterDotLength = str.substr(str.indexOf('.')).length - 1;

      if (str.indexOf('.0') > -1 && str[str.length - 1] === '0' && afterDotLength <= this.fractionDigits) {
        return value;
      } else {
        parsedValue = parseFloat(value);
        value = +parsedValue.toFixed(this.fractionDigits);
      }
    }

    if (isNaN(value)) {
      value = 0;
    }

    if (value > this.max) {
      value = this.max;
    }
    if (value < this.min) {
      value = this.min;
    }

    return value;
  }
}
