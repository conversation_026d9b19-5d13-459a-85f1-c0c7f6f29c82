<div class="input-group bootstrap-touchspin">
  <span class="input-group-btn">
    <button class="btn btn-default bootstrap-touchspin-down" type="button" (click)="valueDown($event)"
            [class.disabled]="isDisabled" [disabled]="isDisabled">{{ downLabel }}</button>
  </span>
  <span *ngIf="prefix" class="input-group-addon bootstrap-touchspin-prefix" [class.disabled]="isDisabled">
    {{ prefix }}
  </span>
  <input type="number" #input class="form-control" (keyup)="valueChanged($event)"
         [value]="value" (change)="valueChanged($event)" style="display: block;" [min]="min" [max]="max" [step]="step"
          [class.disabled]="isDisabled" [disabled]="isDisabled">
  <span *ngIf="postfix" class="input-group-addon bootstrap-touchspin-postfix" [class.disabled]="isDisabled">
    {{ postfix }}
  </span>
  <span class="input-group-btn">
    <button class="btn btn-default bootstrap-touchspin-up" type="button" (click)="valueUp($event)"
            [class.disabled]="isDisabled" [disabled]="isDisabled">{{ upLabel }}</button>
  </span>
</div>
