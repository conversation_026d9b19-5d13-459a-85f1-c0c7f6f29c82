import { AfterViewInit, Directive, ElementRef, forwardRef, Input, OnDestroy, Renderer2 } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { fromEvent, Observable, Subscription } from 'rxjs';

@Directive({
  selector: '[input-integer]',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputIntegerDirective),
      multi: true,
    }
  ]
})
export class InputIntegerDirective implements ControlValueAccessor, AfterViewInit, OnDestroy {

  @Input() allowNegative: boolean = false;

  private keyUp$: Observable<any>;
  private subscriptions: Subscription[] = [];

  constructor(
    private elementRef: ElementRef,
    private _renderer: Renderer2,
  ) {

  }

  ngAfterViewInit() {
    this.createSources();
    this.subscribeToChanges();
  }

  registerOnChange( fn: any ) {
    this._onChange = fn;
  }

  registerOnTouched(): void {
  }

  setDisabledState( isDisabled: boolean ): void {
    this._renderer.setProperty(this.elementRef.nativeElement, 'disabled', isDisabled);
  }

  writeValue( value: any ) {
    this.setElementValue(value);
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  applyTargetValue( event ) {
    let value;
    if (this.allowNegative && event.target.value === '-') {
      value = event.target.value;
    } else {
      value = this.parseInteger(event.target.value);
    }

    if (event.target.value !== '') {
      this.setElementValue(value);
    }

    this._onChange(value);
  }

  private _onChange: Function = ( _: any ) => {
  }

  private createSources() {
    this.keyUp$ = fromEvent(this.elementRef.nativeElement, 'keyup');
  }

  private subscribeToChanges() {
    this.subscriptions.push(this.keyUp$.subscribe(( data ) => this.applyTargetValue(data)));
  }

  private parseInteger( value: string ): number {
    let result = parseInt(value, 10);

    if (isNaN(result)) {
      result = 0;
    }

    return result;
  }

  private setElementValue( value: any ) {
    this._renderer.setProperty(this.elementRef.nativeElement, 'value', value);
  }
}
