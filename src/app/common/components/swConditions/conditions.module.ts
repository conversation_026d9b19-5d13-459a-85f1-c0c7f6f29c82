import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';
import { ConditionsComponent } from './conditions.component';
import { ConditionsElementComponent } from './conditions-element.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule,
        TrimInputValueModule,
    ],
  declarations: [
    ConditionsComponent,
    ConditionsElementComponent,
  ],
  exports: [
    ConditionsComponent,
    ConditionsElementComponent,
  ]
})
export class ConditionsModule {

}
