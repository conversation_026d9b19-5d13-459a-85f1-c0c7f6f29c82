.text-right
  text-align: right;

.bg-light-grey
  background-color: #fafafa;

.pl-30
  padding-left: 30px;

.btn-link-dashed
  border-bottom: 1px dotted #333;
  line-height: 14px;

.highlight_mixin
  content: '';
  display: block;
  position: absolute;
  left: -20px;
  top: 14px;
  width: 6px;
  height: calc(~"100% - 28px");
  background-color: #ddd;
  border-radius: 6px;
  animation: dots 3.5s linear infinite;

.condition
  &__header
    margin-bottom: 10px;
    ^[0]__select
      background-color: #fafafa

  &__body
    padding-left: 30px;
    ^[0]__select
      height: 32px;

  &__item
    position: relative;
    margin-bottom: 10px;
    .form-group
      margin-bottom: 0 !important;

  &__group
    position: relative;
    &--highlight
      &>.condition__body
        &>.condition__item
          &:after
           content: '';
           display: block;
           position: absolute;
           left: -20px;
           top: 14px;
           width: 6px;
           height: calc(~"100% - 28px");
           background-color: #ddd;
           border-radius: 6px;
           animation: dots 3.5s linear infinite;

  &__links
    line-height: 32px;



@keyframes dots
  0%, 50%, 100%
    opacity: 0.99;
  25%, 75%
    opacity: 0.3;
    text-shadow: none;

