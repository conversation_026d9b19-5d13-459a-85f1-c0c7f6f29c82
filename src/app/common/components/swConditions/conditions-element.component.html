<ng-container *ngIf="isConditionGroup()">
  <div [formGroup]="form" class="condition__header">
    <div class="row">
      <div class="col-sm-3">
        <select class="select form-control condition__select" (mouseenter)="highlightGroup($event)"
                (mouseleave)="highlightGroup($event)" formControlName="groupType">
          <option value="and">{{ 'COMPONENTS.CONDITIONS.allRules' | translate }}</option>
          <option value="or">{{ 'COMPONENTS.CONDITIONS.anyRules' | translate }}</option>
        </select>
      </div>
      <div class="col-sm-9 text-right" *ngIf="form.enabled">
        <ul class="condition__links list-inline pr-10 no-margin">
          <li>
            <a href="#" class="btn btn-link btn-link-dashed no-padding" (mouseenter)="highlightGroup($event)"
               (mouseleave)="highlightGroup($event)" (click)="removeGroup($event)">
              {{ 'COMPONENTS.CONDITIONS.clearAll' | translate }}
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="condition__body">
    <ng-container *ngFor="let item of conditionsGroup.controls">
      <div sw-conditions-element [form]="item" [valueFields]="valueFields" [operators]="operators"
           class="condition__item" [ngClass]="{'condition__group': item.controls.hasOwnProperty('group')}"
           (remove)="onRemoveChildItem($event)"></div>
    </ng-container>
  </div>
  <div class="condition__bottom" *ngIf="form.enabled">
    <div class="row">
      <div class="col-xs-12">
        <ul class="condition__links list-inline pl-30 no-margin">
          <li><a href="#" class="btn btn-link btn-link-dashed no-padding" (click)="addConditionItem($event)">
            {{ 'COMPONENTS.CONDITIONS.addCondition' | translate }}
          </a></li>
          <li><a href="#" class="btn btn-link btn-link-dashed no-padding" (click)="addConditionGroup($event)">
            {{ 'COMPONENTS.CONDITIONS.addGroup' | translate }}
          </a></li>
        </ul>
      </div>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="isConditionItem()">
  <div class="row" [formGroup]="form">
    <div class="col-md-11">
      <div class="form-group">
        <div class="row">
          <div class="col-xs-12 col-sm-6">
            <select class="select form-control condition__select" formControlName="valueField">
              <option>{{ 'COMPONENTS.CONDITIONS.placeholderValueField' | translate }}</option>
              <option *ngFor="let item of valueFields" value="{{ item.value }}">{{ item.title }}</option>
            </select>
          </div>
          <div class="col-xs-6 col-sm-3">
            <select class="select form-control condition__select" formControlName="operator">
              <option>{{ 'COMPONENTS.CONDITIONS.placeholderOperator' | translate }}</option>
              <option *ngFor="let item of operators" value="{{ item.value }}">{{ item.title }}</option>
            </select>
          </div>
          <div class="col-xs-6 col-sm-3">
            <input trimValue type="text" class="form-control input-xs"
                   [placeholder]="'COMPONENTS.CONDITIONS.placeholderValue' | translate" formControlName="value">
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-1 text-right" *ngIf="form.enabled">
      <div class="form-group">
        <button class="btn btn-xs btn-icon btn-flat" (click)="removeItem()" [ngClass]="{'disabled':disabled}"
                [disabled]="disabled"><i class="icon-cross"></i></button>
      </div>
    </div>
  </div>
</ng-container>
