import { Component, Input } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ValidatorFn } from '@angular/forms';
import { SelectOption } from './conditions-element.component';
import { ConditionsItem } from './conditions.model';

@Component({
  selector: 'sw-conditions',
  templateUrl: './conditions.component.html',
  styleUrls: [ './conditions.component.styl' ],
})
export class ConditionsComponent {

  @Input() public valueFields: SelectOption[];
  @Input() public operators: SelectOption[];

  private _parentForm: FormGroup;
  private _parentFormFieldName: string;
  private _data: ConditionsItem;
  private _disabled: boolean;
  private _conditionsValidator: ValidatorFn;
  set form( form: FormGroup ) {
    if (!this._parentForm) return;
    if (this._conditionsValidator) {
      form.setValidators(this._conditionsValidator);
    }

    this._parentForm.removeControl(this._parentFormFieldName);
    this._parentForm.setControl(this._parentFormFieldName, form);
  }

  get form(): FormGroup {
    let formGroup: FormGroup;
    if (this._parentForm && this._parentForm.controls.hasOwnProperty(this._parentFormFieldName)) {
      formGroup = <FormGroup>this._parentForm.get(this._parentFormFieldName);
    }
    return formGroup;
  }

  @Input()
  set data( value: ConditionsItem ) {
    if (!value) return;
    this._data = value;
    this.buildAndPopulateForm();
  }

  @Input()
  set parentForm( value: FormGroup ) {
    this._parentForm = value;
    this._conditionsValidator = this._parentForm.get(this._parentFormFieldName).validator;
    this.buildAndPopulateForm();
  }

  @Input()
  set disabled( value: boolean ) {
    this._disabled = value;
    this.buildAndPopulateForm();
  }

  @Input()
  set parentFormFieldName( value: string ) {
    this._parentFormFieldName = value;
  }

  constructor( private fb: FormBuilder ) {
    this.initForm();
  }

  public onRemoveAllItems( item: FormGroup ) {
    const arr: FormArray = item.get('group') as FormArray;
    while (arr.length) {
      arr.removeAt(0);
    }
  }

  private initForm() {
    this._parentFormFieldName = 'conditions';
    this._parentForm = this.fb.group({
      [this._parentFormFieldName]: this.fb.group({
        groupType: '',
        group: this.fb.array([])
      })
    });

    this._data = { groupType: 'and', group: [] };
  }

  private buildAndPopulateForm() {
    this.form = this.populateFormGroup(Object.assign({}, this._data));
  }


  private applyDisabled( value ) {
    return this._disabled ? [{ value, disabled: this._disabled }] : value;
  }

  private populateFormGroup( data: ConditionsItem ): FormGroup {
    if ('group' in data) {
      data = Object.assign({}, data, {
        groupType: this.applyDisabled(data.groupType),
        group: this.fb.array(data.group.map(item => this.populateFormGroup(item))),
      });
    } else {
      data = Object.assign({}, data, {
        valueField: this.applyDisabled(data.valueField),
        operator: this.applyDisabled(data.operator),
        value: this.applyDisabled(data.value),
      });
    }
    return this.fb.group(data);
  }

}
