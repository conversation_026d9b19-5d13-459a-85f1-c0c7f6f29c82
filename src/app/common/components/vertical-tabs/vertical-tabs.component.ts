import { AfterContentChecked, AfterContentInit, Component, ContentChildren, QueryList } from '@angular/core';
import { Observable } from 'rxjs';
import { delay, map, startWith } from 'rxjs/operators';
import { VerticalTabsItemComponent } from './vertical-tabs-item/vertical-tabs-item.component';



@Component({
  selector: 'sw-vertical-tabs',
  templateUrl: './vertical-tabs.component.html',
  styleUrls: ['./vertical-tabs.component.scss']
})
export class VerticalTabsComponent implements AfterContentInit, AfterContentChecked {

  @ContentChildren(VerticalTabsItemComponent) tabs?: QueryList<VerticalTabsItemComponent>;
  tabItems$?: Observable<VerticalTabsItemComponent[]>;
  activeTab: VerticalTabsItemComponent | undefined;

  ngAfterContentInit(): void {
    if (this.tabs) {
      this.tabItems$ = this.tabs.changes
        .pipe(
          startWith(''),
          delay(0),
          map(() => {
            return this.tabs ? this.tabs.toArray() : [];
          })
        );
    }
  }

  ngAfterContentChecked(): void {
    if (!this.activeTab) {
      Promise.resolve().then(() => {
        this.activeTab = this.tabs ? this.tabs.first : undefined;
      });
    }
  }

  selectTab( tabItem: VerticalTabsItemComponent ): void {
    if (this.activeTab === tabItem) {
      return;
    }

    if (this.activeTab) {
      this.activeTab.isActive = false;
    }

    this.activeTab = tabItem;

    tabItem.isActive = true;
  }
}
