<div class="sw-vertical-tabs">
  <div class="sw-vertical-tabs__nav">
    <div
      class="sw-vertical-tabs__label"
      (click)="selectTab(item)"
      [class.sw-vertical-tabs__label--active]="activeTab === item"
      *ngFor="let item of tabItems$ | async"
    >
      <div class="sw-vertical-tabs__name">
        <ng-container *ngIf="item.labelComponent">
          <ng-container *ngTemplateOutlet="item.labelComponent.labelContent">
          </ng-container>
        </ng-container>
      </div>

      <ng-container *ngIf="!item.labelComponent">
        {{ item.label }}
      </ng-container>
    </div>
  </div>
  <div class="sw-vertical-tabs__body">
    <ng-container *ngIf="activeTab && activeTab.bodyComponent">
      <ng-container *ngTemplateOutlet="activeTab.bodyComponent.bodyContent">
      </ng-container>
    </ng-container>
  </div>

</div>
