import { Component, ContentChild, Input } from '@angular/core';
import { VerticalTabsBodyComponent } from '../vertical-tabs-body/vertical-tabs-body.component';
import { VerticalTabsLabelComponent } from '../vertical-tabs-label/vertical-tabs-label.component';



@Component({
  selector: 'sw-vertical-tabs-item',
  templateUrl: './vertical-tabs-item.component.html',
})
export class VerticalTabsItemComponent {

  @Input() label?: string;
  @Input() isActive?: boolean;

  @ContentChild(VerticalTabsBodyComponent) bodyComponent: VerticalTabsBodyComponent | null;
  @ContentChild(VerticalTabsLabelComponent) labelComponent: VerticalTabsLabelComponent | null;

  constructor() {
    this.bodyComponent = null;
    this.labelComponent = null;
  }
}
