import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VerticalTabsBodyComponent } from './vertical-tabs-body/vertical-tabs-body.component';
import { VerticalTabsItemComponent } from './vertical-tabs-item/vertical-tabs-item.component';
import { VerticalTabsLabelComponent } from './vertical-tabs-label/vertical-tabs-label.component';
import { VerticalTabsComponent } from './vertical-tabs.component';



@NgModule({
  imports: [
    CommonModule
  ],
  declarations: [
    VerticalTabsComponent,
    VerticalTabsItemComponent,
    VerticalTabsLabelComponent,
    VerticalTabsBodyComponent,
  ],
  exports: [
    VerticalTabsComponent,
    VerticalTabsLabelComponent,
    VerticalTabsBodyComponent,
    VerticalTabsItemComponent,
  ]
})
export class VerticalTabsModule {
}
