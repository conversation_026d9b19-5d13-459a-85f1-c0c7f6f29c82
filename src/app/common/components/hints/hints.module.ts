import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { HintsComponent } from './hints.component';
import { PipesModule } from '../../pipes/pipes.module';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';


@NgModule({
  imports: [
    TranslateModule,
    CommonModule,
    PipesModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
  ],
  exports: [
    HintsComponent,
  ],
  declarations: [
    HintsComponent,
  ],
  providers: [],
})
export class HintsModule {

}
