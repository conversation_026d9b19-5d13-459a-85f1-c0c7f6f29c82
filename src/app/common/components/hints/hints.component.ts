import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { SwDexieService } from '@skywind-group/lib-swui';
import { from, Subscription } from 'rxjs';


@Component({
  selector: 'hints',
  templateUrl: 'hints.component.html',
  styleUrls: ['./hints.component.scss']
})

export class HintsComponent implements OnInit, OnDestroy {

  @Input() public message: string;
  @Input() public translate: boolean = true;
  @Input() public showCloseBtn: boolean = true;
  @Input() public fontSize: number = 14;
  @Input() public alwaysDisplay: boolean = false;

  public hintVisible: boolean;

  private hintsShown: { [key: string]: boolean };
  private subs: Subscription[] = [];

  constructor( private service: SwDexieService ) {
  }

  ngOnInit() {
    this.subs.push(from(this.service.getAppSettings()).subscribe(data => {
      const value = Object.assign(
        {},
        { hintsShown: {} },
        data && data.params
      );
      this.hintsShown = value.hintsShown;
      this.hintVisible = !(this.message in value.hintsShown);
    }));
  }

  ngOnDestroy(): void {
    this.subs.forEach(sub => sub.unsubscribe());
  }

  closeHintAndRemember() {
    this.markAsShown();
    this.closeHint();
  }

  closeHint() {
    this.hintVisible = false;
  }

  private markAsShown() {
    const hintCode: string = this.message;
    this.hintsShown = {
      ...this.hintsShown,
      [hintCode]: true
    };
    this.service.saveAppSettings({ hintsShown: this.hintsShown });
  }
}
