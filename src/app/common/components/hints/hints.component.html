<mat-card *ngIf="hintVisible || alwaysDisplay" class="sw-banner">
  <!--<button mat-icon-button class="sw-banner__close" (click)="closeHint()" title="{{ 'HINTS.btnCloseHint' | translate }}">-->
  <!--<mat-icon>close</mat-icon>-->
  <!--</button>-->
  <div class="sw-banner__left">
    <mat-icon class="sw-banner__icon" fontSet="material-icons-outline">info</mat-icon>
  </div>

  <div class="sw-banner__middle">
    <span [ngStyle]="{'font-size': fontSize+'px'}" *ngIf="translate" [innerHTML]="message | translate | nl2br"></span>
    <span *ngIf="!translate">{{ message }}</span>
  </div>

  <div *ngIf="showCloseBtn" class="sw-banner__right">
    <button mat-stroked-button (click)="closeHintAndRemember()">{{ 'HINTS.btnGotIt' | translate }}</button>
  </div>
</mat-card>
