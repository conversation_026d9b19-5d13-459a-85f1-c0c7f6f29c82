$hint-text: #2F2F2F;
$hint-bg: #FDFFC5;
$hint-icon: #CACBA0;


.sw-banner {
  position: relative;
  display: flex;
  background-color: $hint-bg;
  margin-bottom: 32px;
  &__close {
    position: absolute;
    top: 0;
    right: 0;
  }
  &__icon {
    width: 40px;
    height: 40px;
    font-size: 40px;
    line-height: 40px;
    color: $hint-icon;
  }
  &__left {
    display: flex;
    align-items: center;
    height: 46px;
  }
  &__middle {
    display: flex;
    align-items: center;
    flex-grow: 1;
    font-size: 14px;
    line-height: 1.2em;
    padding: 0 16px;
    color: $hint-text;
  }
  &__right {
    display: flex;
    align-items: flex-end;
  }

  @media (max-width: 1024px) {
    flex-wrap: wrap;
    &__middle {
      width: calc(100% - 40px);
    }
    &__right {
      justify-content: flex-end;
      width: 100%;
      padding-top: 16px;
    }
  }

}
