<div
  class="bo-labels"
  [class.bo-labels--editable]="isEditable">
  <div class="bo-labels__inner"
       [class.disabled]="isEditDisabled"
       [isDisabled]="isEditDisabled"
       dropdown
       #dropdown="bs-dropdown"
       [isOpen]="dropdownStatus.isopen"
       (click)="onEditClick()"
       (isOpenChange)="changeDropdownStatus($event)">
    <div class="bo-labels__selected">
      <ul class="bo-labels__selected-list label-list">
        <ng-container *ngIf="processedSelectedLabels.length > 0">
          <li *ngFor="let label of processedSelectedLabels">
            <div class="editable-item">
              <span
                class="editable-item__label label label-xs label-striped"
                [ngClass]="getLabelClass(label)">
                {{label.title}}
              </span>
              <button
                class="editable-item__remove"
                (click)="onRemoveLabelClick(label, $event)">
                <i class="icon-cross2"></i>
              </button>
            </div>
          </li>
        </ng-container>
      </ul>
    </div>
    <input
      trimValue
      type="text"
      class="bo-labels__input"
      [disabled]="isEditDisabled"
      [class.show]="processedSelectedLabels.length === 0"
      placeholder="{{ processedSelectedLabels.length === 0 ? ('ENTITY_SETUP.GAMES.MODALS.noLabels' | translate) : '' }}"
      id="textInput"
      autocomplete="off"
      [(ngModel)]="searchText"
      (keydown)="handleInputKeydown(dropdown, $event)">
    <button
      type="button"
      class="bo-labels__btn bo-labels__btn--edit btn btn-default"
      *ngIf="!isEditable">
      <span class="icon-pencil4"></span>
    </button>
    <button
      type="button"
      class="bo-labels__btn bo-labels__btn--select btn btn-xs btn-default dropdown-toggle"
      *ngIf="isEditable"
      dropdownToggle
      (click)="onSelectBtnClick()">
      <span class="caret"></span>
    </button>
    <ul
      class="bo-labels__dropdown dropdown-menu"
      role="menu"
      *dropdownMenu
      id="dropdownMenu"
      [ngStyle]="{'max-height': processedDropdownHeight}">
      <li role="menuitem" *ngIf="isNewBtnVisible(searchText)">
        <a
          class="bo-labels__add dropdown-item"
          tabindex="-1"
          (keydown)="handleAddItemKeydown($event)"
          (click)="onAddClick($event)">
          <span class="label label-xs label-striped">
            {{searchText}}
          </span>
          <span class="ml-10">Add new label</span>
        </a>
      </li>
      <ng-container *ngIf="processedAvailableLabels.length > 0; else noItems">
        <ng-container *ngFor="let label of processedAvailableLabels | boLabelsGroupFilter : searchText;
          let labelIndex = index">
          <li role="menuitem">
            <ng-container *ngIf="label !== -1; else noMatches">
              <a
                class="dropdown-item"
                tabindex="-1"
                (keydown)="handleDropdownItemKeydown(label, $event, labelIndex)"
                (click)="onDropdownItemClick(label, $event)">
            <span
              class="label label-xs label-striped"
              [ngClass]="getLabelClass(label)">
              {{label.title}}
            </span>
              </a>
            </ng-container>
          </li>
          <ng-template #noMatches>
            <ng-container *ngIf="label === -1 && isCreateNewLabelAllowed === false">
              <li class="bo-labels__noitems text-muted" tabindex="-1">
                <span>No matches...</span>
              </li>
            </ng-container>
          </ng-template>
        </ng-container>
      </ng-container>
      <ng-template #noItems>
        <ng-container *ngIf="processedAvailableLabels.length === 0">
          <li class="bo-labels__noitems text-muted">
            <span>No items...</span>
          </li>
        </ng-container>
      </ng-template>
    </ul>
  </div>
  <div class="bo-labels__actions">
    <button
      type="button"
      class="bo-labels__btn btn btn-xs btn-default"
      (click)="onSaveClick(dropdown)">
      <span class="icon-checkmark3"></span>
    </button>
    <button
      type="button"
      class="bo-labels__btn btn btn-xs btn-default"
      (click)="onCancelClick(dropdown)">
      <span class="icon-cross2"></span>
    </button>
  </div>
</div>



