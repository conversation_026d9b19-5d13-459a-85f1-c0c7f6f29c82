import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';
import { LabelsService } from '../../services/labels.service';

import { BoLabelsGroupComponent } from './bo-labels-group.component';
import { BoLabelsGroupPipe } from './bo-labels-group.pipe';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        BsDropdownModule.forRoot(),
        TranslateModule,
        TrimInputValueModule,
    ],
  exports: [BoLabelsGroupComponent],
  declarations: [
    BoLabelsGroupComponent,
    BoLabelsGroupPipe,
  ],
  providers: [
    LabelsService,
  ],
})

export class BoLabelsGroupModule {
}
