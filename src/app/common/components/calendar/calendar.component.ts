import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  forwardRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subscription } from 'rxjs';
import * as moment from 'moment';
import 'moment-timezone';

import { CalendarService } from '../../services/calendar.service';
import { AppSettings } from '../../typings';
import { SettingsService } from '@skywind-group/lib-swui';


const HOURS_IN_DAY = 24;
const MIN_SEC = 60;

const EMPTY_MASK_OPTIONS: CalendarMaskOptions = {
  mask: false,
};


@Component({
  selector: 'sw-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.styl'],
  providers: [
    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => CalendarComponent), multi: true },
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => CalendarComponent), multi: true },
  ],
})
export class CalendarComponent implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {
  @ViewChild('calendar', { static: true }) calendar: ElementRef;
  /**
   * Show when there is not applied date
   * @type {string}
   */
  @Input() placeholder = 'Select date';

  @Input() date: any;

  @Input() showSpecialButtons: boolean = false;

  @Input() minDate: moment.Moment;
  @Input() maxDate: moment.Moment;

  @Input() enableMask: boolean = true;

  @Input() timePickerEnabled: boolean = true;

  @Input() clearOnDateEmpty: boolean = true;

  @Output() dateChange = new EventEmitter<any>();
  @Output() onClear = new EventEmitter<void>();

  disabled: boolean = false;
  hideCalendar: boolean = false;

  @Input() hideTimeDisabled: boolean = false;

  /**
   * Calendar visibility state
   * @type {boolean}
   */
  isCalendarOpen = false;
  isDropUp: boolean;

  maskOptions: CalendarMaskOptions = EMPTY_MASK_OPTIONS;

  /**
   * Array of dates of current month
   */
  currentMonth: moment.Moment[][];
  /**
   * 'Main' date of month (get current year, month name from it)
   */
  currentDate: any;

  /**
   * Selected, but not applied date
   */
  selectedDate: moment.Moment;
  /**
   * Current hours, minutes, seconds
   */
  hours = 0;
  minutes = 0;
  seconds = 0;

  /**
   * ['Mon', 'Tue', 'Wed' ... ] Day names in header
   */
  dayNames = moment.weekdaysShort();
  /**
   * Months names map in header
   */
  monthNames = moment.months();
  today;

  hoursOptions = (new Array(HOURS_IN_DAY)).fill(0).map(( _, i ) => i);
  minSecOptions = (new Array(MIN_SEC)).fill(0).map(( _, i ) => i);

  /**
   * Availability to change hours/minutes/seconds
   */
  timeAvailability: CalendarTimeAvailabilityMap = {
    hours: true,
    minutes: true,
    seconds: true,
  };

  /**
   * Initial applied date and time. Component is connected with parent by this object
   */
  private _appliedDate: moment.Moment;

  /**
   * timeDisableLevel === 'seconds' disables seconds
   * timeDisableLevel === 'minutes' disables seconds, minutes
   * timeDisableLevel === 'hours' disables seconds, minutes
   * default timeDisableLevel === 'all' disables nothing
   */
  private _timeDisableLevel: CalendarTimeDisableLevel;

  private propagateChange: any;
  private appSettings: AppSettings;
  private _symbol: Symbol;
  private subs: Subscription[] = [];

  @Input()
  set disableCalendar(value: boolean) {
    this.hideCalendar = value;
  }

  @Input()
  set timeDisableLevel( value: CalendarTimeDisableLevel ) {
    this._timeDisableLevel = value;

    this.timeAvailability = {
      hours: value !== 'hours',
      minutes: value !== 'hours' && value !== 'minutes',
      seconds: value !== 'hours' && value !== 'minutes' && value !== 'seconds',
    };

    // Innovation code) Update date through setter
    this.appliedDate = this.appliedDate;
  }

  /**
   * Getter
   * @returns {any} - moment js object
   */
  get appliedDate() {
    return this._appliedDate;
  }

  /**
   * Setter
   * @param value - moment js object
   */
  set appliedDate( value: any ) {
    this.setDateValue(value);
    this.dateChange.emit(this._appliedDate);
  }

  get timeDisableLevel(): CalendarTimeDisableLevel {
    return this._timeDisableLevel;
  }
  get formattedValue() {
    if (this.appliedDate) {
      const timeFormat = this.filterTimeFormat(this.appSettings.timeFormat);
      return this.appliedDate.format(`${this.appSettings.dateFormat} ${timeFormat}`);
    }
    return '';
  }


  constructor( protected ref: ChangeDetectorRef,
               protected settingsService: SettingsService,
               private calendarService: CalendarService,
  ) {
    this._symbol = Symbol('calendar');
    this.today = moment();
  }

  ngOnInit(): void {
    this.subs.push(this.settingsService.appSettings$.subscribe(data => {
      this.setComponentData(data);
    }));
    this.initCalendarIdentifier();
  }

  ngOnChanges(): void {
    this.setComponentData(this.settingsService.appSettings);
  }

  ngOnDestroy(): void {
    this.subs.forEach(sub => sub.unsubscribe());
  }

  validate( control: FormControl ) {
    let value = control.value;
    return !Date.parse(value);
  }

  writeValue( value ) {
    this.date = value;
    this.appliedDate = value;
  }

  registerOnChange( fn ) {
    this.propagateChange = fn;
  }

  registerOnTouched() {
  }

  setDisabledState( disabled: boolean ) {
    this.disabled = disabled;
  }

  /**
   * Set selected date
   * @param event - mouse event
   * @param day
   */
  public selectDay( event: any, day: any ): void {
    event.preventDefault();

    this.selectedDate = day.clone();

    // const zoneFix = Math.abs(this.selectedDate.utcOffset() * 2);
    // this.selectedDate.utcOffset(zoneFix);
    if (!this.selectedDate.isSame(this.currentDate, 'month')) {
      this.setMonth(this.selectedDate.clone());
    }
  }

  /**
   * Set applied date and trigger it to parent
   */
  public applyChanges(): void {
    const { years, months, date } = this.selectedDate.toObject();
    this.appliedDate = this.selectedDate
      .clone()
      .year(years)
      .month(months)
      .date(date)
      .hours(this.hours)
      .minutes(this.minutes)
      .seconds(this.seconds);

    this.setCalendarVisibility(false);
  }

  public setCalendarVisibility( value: boolean ): void {
    this.isCalendarOpen = value;
    this.ref.detectChanges();
    this.calendarTrigger(value);
  }

  /**
   * Clear applied date, set all items to defaults
   */
  public clear(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    if (this.disabled) return;
    this.appliedDate = undefined;

    this.selectedDate = moment();

    this.setMonth(this.selectedDate);
    this.setCalendarVisibility(false);

    this.onClear.emit();
  }

  /**
   * Set 'main' date of month, set array of month dates
   * @param date
   */
  public setMonth( date: moment.Moment ): void {
    let firstDay = moment(date).startOf('month');
    let lastDay = moment(date).endOf('month');

    let result = [];

    while (firstDay.date() <= lastDay.date()) {
      if (!result.length || !firstDay.day()) {
        result.push([]);
      }

      result[result.length - 1][firstDay.day()] = moment(firstDay.valueOf());
      if (firstDay.date() === lastDay.date()) {
        break;
      } else {
        firstDay.add(1, 'days');
      }
    }

    this.currentDate = date.startOf('day');
    this.currentMonth = result;
  }

  public handleInputChange( newValue: string ): void {
    if (this.disabled) return;
    this.appliedDate = newValue;
  }

  /**
   *  Setup selected date and environment
   */
  public appliedDateToFormat() {
    if (this.timePickerEnabled) {
      return this.appSettings.dateFormat + ' ' + this.appSettings.timeFormat;
    } else {
      return this.appSettings.dateFormat;
    }
  }

  public onClickedOutside() {
    this.isCalendarOpen = false;
  }

  private setDateValue( value: any ) {
    if (value && typeof value === 'string') {
      // this._appliedDate = moment(value.replace(/[^0-9:./\\\-TZ ]*/gi, '').trim());
      if (this.timePickerEnabled) {
        this._appliedDate = moment(value, this.appSettings.dateFormat + ' ' + this.appSettings.timeFormat);
      } else {
        this._appliedDate = moment(value, this.appSettings.dateFormat);
      }
      if (!this._appliedDate.isValid()) {
        this._appliedDate = moment();
      }
    } else {
      this._appliedDate = value;
    }

    if (this._appliedDate) {
      this._appliedDate.millisecond(0);
      this._appliedDate = this.setDateByBounds(this._appliedDate);

      if (!this.timeAvailability.hours) this._appliedDate.hours(0);
      if (!this.timeAvailability.minutes) this._appliedDate.minutes(0);
      if (!this.timeAvailability.seconds) this._appliedDate.seconds(0);
    }

    this.setDateByApplied();
    this.setMonth(this.selectedDate);

    if (typeof this.propagateChange === 'function') {
      this.propagateChange(this._appliedDate && this._appliedDate.toString());
    }
  }

  private setComponentData( data: AppSettings ): void {
    this.appSettings = data;
    this.setMaskOtpions();

    if (this.date) {
      this.setDateValue(this.date);
    } else if (this.clearOnDateEmpty) {
      this.clear();
    }
  }

  private setMaskOtpions(): void {
    if (this.enableMask) {
      const timeFormat = this.filterTimeFormat(this.appSettings.timeFormat);
      const dateMask = this.getMaskFromStr(this.appSettings.dateFormat);
      const timeMask = this.getMaskFromStr(timeFormat);

      if (this.timePickerEnabled) {
        this.maskOptions = {
          mask: dateMask.concat(' ').concat(timeMask),
        };
      } else {
        this.maskOptions = {
          mask: dateMask,
        };
      }
    } else {
      this.maskOptions = EMPTY_MASK_OPTIONS;
    }
  }

  private getMaskFromStr( str: string ): any[] {
    return str
      .split('')
      .map(c => /\w/.test(c) ? new RegExp('[0-9]') : c);
  }

  private setDateByApplied(): void {
    this.selectedDate = this.appliedDate ? this.appliedDate.clone() : moment();

    this.selectedDate = this.setDateByBounds(this.selectedDate);

    this.hours = this.timeAvailability.hours ? this.selectedDate.hours() : 0;
    this.minutes = this.timeAvailability.minutes ? this.selectedDate.minutes() : 0;
    this.seconds = this.timeAvailability.seconds ? this.selectedDate.seconds() : 0;
  }

  private setDateByBounds( date: moment.Moment ): moment.Moment {
    if (this.minDate && date && this.minDate.isAfter(date, 'date')) return this.minDate.clone();
    if (this.maxDate && date && this.maxDate.isBefore(date, 'date')) return this.maxDate.clone();

    return date;
  }

  /**
   * Need for ngx-bootstrap dropdown component
   * @param value
   */
  // @ts-ignore
  private handleDropdownToggle( value: boolean ): void {
    this.isCalendarOpen = value;
  }

  private filterTimeFormat( timeFormat: string ) {
    if (this.hideTimeDisabled) {

      if (!this.timeAvailability.seconds) {
        timeFormat = timeFormat.replace(/([\:|\.]ss)/, '');
      }

      if (!this.timeAvailability.minutes) {
        timeFormat = timeFormat.replace(/([\:|\.]mm)/, '');
      }

    }
    return timeFormat;
  }

  private initCalendarIdentifier(): void {
    const calendarSubscription = this.calendarService.calendars.subscribe((value: Symbol[]) => {
      value.forEach(cal => {
        if (cal === this._symbol) {
          this.isCalendarOpen = true;
          this.isCalendarDropUp();
        } else {
          this.isCalendarOpen = false;
        }
      });
    });

    this.subs.push(calendarSubscription);
  }

  private calendarTrigger(value: boolean): void {
    value ? this.calendarService.addCalendar(this._symbol) : this.calendarService.removeCalendar(this._symbol);
  }

  private isCalendarDropUp() {
    const inputHeight = this.calendar.nativeElement.getBoundingClientRect().height;
    const dropdownHeight = 410;
    const calendarHeight = dropdownHeight + inputHeight;
    const windowHeight = window.outerHeight;
    const offsetTop = this.calendar.nativeElement.getBoundingClientRect().top;
    this.isDropUp = windowHeight - offsetTop < calendarHeight;
  }
}
