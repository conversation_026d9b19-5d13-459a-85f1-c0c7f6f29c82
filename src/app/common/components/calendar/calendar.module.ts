import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TrimInputValueModule } from '../../directives/trim-input-value/trim-input-value.module';
import { CalendarComponent } from './calendar.component';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { FormsModule } from '@angular/forms';
import { LeadingZeroPipe } from '../../pipes/leading-zero/leading-zero.pipe';
import { TranslateModule } from '@ngx-translate/core';
import { TextMaskModule } from 'angular2-text-mask';
import { ClickOutsideModule } from 'ng-click-outside';


@NgModule({
    imports: [
        ClickOutsideModule,
        CommonModule,
        TranslateModule,
        BsDropdownModule.forRoot(),
        FormsModule,
        TextMaskModule,
        TrimInputValueModule,
    ],
  declarations: [
    CalendarComponent,
    LeadingZeroPipe,
  ],
  exports: [
    CalendarComponent,
  ]
})
export class CalendarModule {
}
