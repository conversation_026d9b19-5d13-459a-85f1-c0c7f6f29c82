<div class="sw-calendar"
     #calendar
     [isOpen]="isCalendarOpen"
     dropdown
     [isDisabled]="disabled"
     [dropup]="isDropUp"
     [autoClose]="false"
     (isOpenChange)="setCalendarVisibility($event)"
     (clickOutside)="onClickedOutside()">

  <!-- Component input with applied date if it is -->
  <div class="input-group" dropdownToggle>
    <span class="input-group-addon pl-10 pr-10" [ngClass]="{'disabled': disabled}">
      <i class="icon-calendar22"></i>
    </span>
    <input type="text" class="form-control" trimValue
           #input
           [textMask]="maskOptions"
           [disabled]="disabled"
           [value]="formattedValue"
           (change)="handleInputChange(input.value)"
           [placeholder]="placeholder || 'Select date'">

    <span class="input-group-addon cursor-pointer pl-10 pr-10" *ngIf="appliedDate" (click)="clear($event)"
          [ngClass]="{'disabled': disabled}">
      <i class="icon-cross"></i>
    </span>
  </div>

  <ng-container *ngIf="!hideCalendar">
    <div class="sw-calendar_wrapper dropdown-menu" *dropdownMenu>
      <!-- Date area START -->
      <div class="mb-10 pl-10 pr-10">
        <table class="sw-calendar_table">
          <thead *ngIf="currentDate">
          <tr>
            <th class="sw-calendar_th prev" (click)="setMonth(currentDate.clone().add(-1, 'month'))">
              <i class="icon-arrow-left32"></i>
            </th>
            <th colspan="5" class="sw-calendar_th month">
              {{monthNames[currentDate.month()]}} {{currentDate.year()}}
            </th>
            <th class="sw-calendar_th next" (click)="setMonth(currentDate.clone().add(1, 'month'))" readonly="1">
              <i class="icon-arrow-right32"></i>
            </th>
          </tr>
          <tr *ngIf="showSpecialButtons">
            <th class="sw-calendar_th cursor-pointer"
                (click)="selectDay($event, selectedDate.clone().add(-1, 'day'))">-1D
            </th>
            <th colspan="2" class="sw-calendar_th cursor-pointer"
                (click)="selectDay($event, today.clone().add(-1, 'day'))">YTD
            </th>
            <th class="sw-calendar_th cursor-pointer"
                (click)="selectDay($event, today)">TD
            </th>
            <th colspan="2" class="sw-calendar_th cursor-pointer"
                (click)="selectDay($event, today.clone().add(1, 'day'))">TMW
            </th>
            <th class="sw-calendar_th cursor-pointer"
                (click)="selectDay($event, selectedDate.clone().add(1, 'day'))">+1D
            </th>
          </tr>
          <tr class="sw-calendar_tr">
            <th *ngFor="let dayName of dayNames" class="sw-calendar_th">{{dayName}}</th>
          </tr>
          </thead>

          <tbody>
          <tr *ngFor="let week of currentMonth" class="sw-calendar_tr">
            <td *ngFor="let day of week" class="sw-calendar_td"
                [ngClass]="{
                selected: day?.isSame(selectedDate, 'date'),
                today: day?.isSame(today, 'date'),
                disabled: (minDate && day?.isBefore(minDate, 'date')) || (maxDate && day?.isAfter(maxDate, 'date'))
               }"
                (click)="selectDay($event, day)">
              {{day?.format('D')}}
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <!-- Date area END -->

      <!-- Time area START -->
      <div class="text-center mb-20" *ngIf="timePickerEnabled">
        <select class="form-control input-sm sw-calendar_input select" [(ngModel)]="hours"
                [ngModelOptions]="{standalone: true}" [disabled]="!timeAvailability.hours">
          <option *ngFor="let timeOption of hoursOptions" [value]="timeOption">{{timeOption | leadingZero}}</option>
        </select>
        :
        <select class="form-control input-sm sw-calendar_input select" [(ngModel)]="minutes"
                [ngModelOptions]="{standalone: true}" [disabled]="!timeAvailability.minutes">
          <option *ngFor="let timeOption of minSecOptions" [value]="timeOption">{{timeOption | leadingZero}}</option>
        </select>

        <ng-container *ngIf="!(hideTimeDisabled && !timeAvailability.seconds)">
          :
          <select class="form-control input-sm sw-calendar_input select" [(ngModel)]="seconds"
                  [ngModelOptions]="{standalone: true}" [disabled]="!timeAvailability.seconds">
            <option *ngFor="let timeOption of minSecOptions" [value]="timeOption">{{timeOption | leadingZero}}</option>
          </select>
        </ng-container>
      </div>
      <!-- Time area END -->

      <!-- Buttons area START -->
      <div class="text-center">
        <div class="col-lg-6">
          <button type="button" class="btn btn-default full-width" (click)="setCalendarVisibility(false)">Cancel
          </button>
        </div>
        <div class="col-lg-6">
          <button type="button" class="btn bg-success full-width" (click)="applyChanges()">Apply</button>
        </div>
      </div>
      <!-- Buttons area END -->
    </div>
  </ng-container>
</div>
