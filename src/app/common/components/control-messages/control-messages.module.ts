import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { ControlMessagesComponent }   from './control-messages.component';
import { ValidationService } from '../../services/validation.service';

@NgModule({
  imports: [CommonModule, TranslateModule],
  exports: [ControlMessagesComponent],
  declarations: [ControlMessagesComponent],
  providers: [ValidationService],
})
export class ControlMessagesModule {
}
