<ng-container *ngIf="!loading; else spinner">
  <ng-container *ngIf="!isMaster; else iconCsv">
    <button mat-icon-button
            matTooltip="{{tooltip}}"
            (click)="downloadCsv.emit()">
      <mat-icon>{{iconName}}</mat-icon>
    </button>
  </ng-container>
  <ng-template #iconCsv>
    <button mat-icon-button
            style="cursor: initial; opacity: 0.5;"
            matTooltip="Please select an Operator in the entity picker">
      <mat-icon>{{iconName}}</mat-icon>
    </button>
  </ng-template>
</ng-container>
<ng-template #spinner>
  <mat-spinner diameter="24"></mat-spinner>
</ng-template>
