import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

import { SwHubEntityService } from '@skywind-group/lib-swui';

import { Entity } from '../../models/entity.model';


@Component({
  selector: 'download-csv',
  templateUrl: './download-csv.component.html',
  styleUrls: ['./download-csv.component.scss']
})
export class DownloadCsvComponent implements OnInit {
  @Input() loading: boolean = false;
  @Input() iconName = 'get_app';
  @Input() tooltip = 'Download CSV';
  @Output() downloadCsv: EventEmitter<void> = new EventEmitter();

  isMaster: boolean = false;
  private readonly destroyed$ = new Subject<void>();

  constructor(private readonly hubEntityService: SwHubEntityService) {
  }

  ngOnInit(): void {
    this.hubEntityService.entitySelected$
      .pipe(
        filter(data => !!data),
        takeUntil(this.destroyed$)
      ).subscribe((data) => {
      this.isMaster = data.name === Entity.MASTER_NAME && data.path === Entity.ROOT_PATH;
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
