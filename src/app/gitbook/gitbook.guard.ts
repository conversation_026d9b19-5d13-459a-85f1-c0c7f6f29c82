import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate } from '@angular/router';
import { map, switchMap, take } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { SwHubAuthService } from '@skywind-group/lib-swui';

const API = environment.ENV_API_SERVER_ENDPOINT + '/gitbook/login';

@Injectable()
export class GitbookGuard implements CanActivate {

  constructor( private readonly auth: SwHubAuthService,
               private readonly http: HttpClient ) {
  }

  canActivate( { params: { space } }: ActivatedRouteSnapshot ) {
    return this.auth.logged.pipe(
      take(1),
      switchMap(() => {
        return this.http.post<{ url: string }>(API, { space }).pipe(
          take(1),
          map(( { url } ) => {
            location.href = url;
            return true;
          })
        );
      })
    );
  }
}
