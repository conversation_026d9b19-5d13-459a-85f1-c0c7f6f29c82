import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GitbookComponent } from './gitbook.component';
import { RouterModule, Routes } from '@angular/router';
import { GitbookGuard } from './gitbook.guard';

const routes: Routes = [
  {
    path: ':space',
    component: GitbookComponent,
    canActivate: [GitbookGuard],
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
  ],
  declarations: [
    GitbookComponent,
  ],
  providers: [
    GitbookGuard
  ]
})
export class GitbookModule {
}
