{"rtpHistory": [{"entity": "Merchant_for_cascade_child1_Test", "inheritedForm": "test", "changeDateTime": "2020-09-25T11:46:52.456Z", "game": "test", "gameCode": "test", "rtpBefore": 2, "rtpAfter": 3, "rtpReduction": 3}, {"entity": "Merchant_for_cascade_child1_Test2", "inheritedForm": "test2", "changeDateTime": "2020-09-25T11:46:52.456Z", "game": "test2", "gameCode": "test2", "rtpBefore": 2, "rtpAfter": 3, "rtpReduction": 3}, {"entity": "Merchant_for_cascade_child1_Test3", "inheritedForm": "test3", "changeDateTime": "2020-09-25T11:46:52.456Z", "game": "test3", "gameCode": "test3", "rtpBefore": 2, "rtpAfter": 3, "rtpReduction": 3}, {"entity": "Merchant_for_cascade_child1_Test4", "inheritedForm": "test4", "changeDateTime": "2020-09-25T11:46:52.456Z", "game": "test4", "gameCode": "test4", "rtpBefore": 2, "rtpAfter": 3, "rtpReduction": 3}]}