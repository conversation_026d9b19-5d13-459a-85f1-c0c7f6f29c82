[{"code": "AED", "displayName": "UAE Dirham"}, {"code": "AFN", "displayName": "Afghani"}, {"code": "ALL", "displayName": "Lek"}, {"code": "AMD", "displayName": "Armenian Dram"}, {"code": "ANG", "displayName": "Netherlands Antillean Guilder"}, {"code": "AOA", "displayName": "Kwan<PERSON>"}, {"code": "ARS", "displayName": "Argentine Peso"}, {"code": "AUD", "displayName": "Australian Dollar"}, {"code": "AWG", "displayName": "Aruban Florin"}, {"code": "AZN", "displayName": "<PERSON><PERSON>"}, {"code": "BAM", "displayName": "Convertible Mark"}, {"code": "BBD", "displayName": "Barbados Dollar"}, {"code": "BDT", "displayName": "<PERSON><PERSON>"}, {"code": "BGN", "displayName": "Bulgarian Lev"}, {"code": "BHD", "displayName": "<PERSON><PERSON>"}, {"code": "BIF", "displayName": "Burundi Franc"}, {"code": "BMD", "displayName": "Bermudian Dollar"}, {"code": "BND", "displayName": "Brunei Dollar"}, {"code": "BOB", "displayName": "Boliviano"}, {"code": "BOV", "displayName": "Mvdol"}, {"code": "BRL", "displayName": "Brazilian Real"}, {"code": "BSD", "displayName": "Bahamian Dollar"}, {"code": "UBT", "displayName": "Micro Bitcoin (bit)"}, {"code": "MBT", "displayName": "Milli Bitcoin"}, {"code": "BTN", "displayName": "Ngultrum"}, {"code": "BWP", "displayName": "<PERSON><PERSON>"}, {"code": "BYN", "displayName": "Belarusian Ruble"}, {"code": "BYR", "displayName": "Belarusian Ruble"}, {"code": "BZD", "displayName": "Belize Dollar"}, {"code": "CAD", "displayName": "Canadian Dollar"}, {"code": "CDF", "displayName": "Congolese Franc"}, {"code": "CHE", "displayName": "WIR Euro"}, {"code": "CHF", "displayName": "Swiss Franc"}, {"code": "CHW", "displayName": "WIR Franc"}, {"code": "CLF", "displayName": "Unidad de Fomento"}, {"code": "CLP", "displayName": "Chilean Peso"}, {"code": "CNY", "displayName": "<PERSON>"}, {"code": "COP", "displayName": "Colombian Peso"}, {"code": "COU", "displayName": "Unidad de Valor Real"}, {"code": "CRC", "displayName": "Costa Rican Colon"}, {"code": "CUC", "displayName": "Peso Convertible"}, {"code": "CUP", "displayName": "Cuban Peso"}, {"code": "CVE", "displayName": "Cabo Verde Escudo"}, {"code": "CZK", "displayName": "Czech Koruna"}, {"code": "DJF", "displayName": "Djibouti Franc"}, {"code": "DKK", "displayName": "Danish Krone"}, {"code": "DOP", "displayName": "Dominican Peso"}, {"code": "DZD", "displayName": "Algerian Dinar"}, {"code": "EGP", "displayName": "Egyptian Pound"}, {"code": "ERN", "displayName": "Nakfa"}, {"code": "ETB", "displayName": "Ethiopian Birr"}, {"code": "EUR", "displayName": "Euro"}, {"code": "FJD", "displayName": "Fiji Dollar"}, {"code": "FKP", "displayName": "Falkland Islands Pound"}, {"code": "GBP", "displayName": "Pound Sterling"}, {"code": "GEL", "displayName": "<PERSON><PERSON>"}, {"code": "GHS", "displayName": "Ghana Cedi"}, {"code": "GIP", "displayName": "Gibraltar Pound"}, {"code": "GMD", "displayName": "<PERSON><PERSON>"}, {"code": "GNF", "displayName": "Guinea Franc"}, {"code": "GTQ", "displayName": "Quetzal"}, {"code": "GYD", "displayName": "Guyana Dollar"}, {"code": "HKD", "displayName": "Hong Kong Dollar"}, {"code": "HNL", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "HRK", "displayName": "<PERSON><PERSON>"}, {"code": "HTG", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "HUF", "displayName": "Forint"}, {"code": "IDR", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "IDS", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "RUP", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "ILS", "displayName": "New Israeli Sheqel"}, {"code": "INR", "displayName": "Indian Rupee"}, {"code": "IQD", "displayName": "Iraqi <PERSON>"}, {"code": "IRR", "displayName": "Iranian Rial"}, {"code": "ISK", "displayName": "Iceland Krona"}, {"code": "JMD", "displayName": "Jamaican Dollar"}, {"code": "JOD", "displayName": "<PERSON><PERSON>"}, {"code": "JPY", "displayName": "Yen"}, {"code": "KES", "displayName": "Kenyan Shilling"}, {"code": "KGS", "displayName": "Som"}, {"code": "KHR", "displayName": "Riel"}, {"code": "KMF", "displayName": "<PERSON><PERSON>"}, {"code": "KPW", "displayName": "North Korean Won"}, {"code": "KRW", "displayName": "Won"}, {"code": "KWD", "displayName": "<PERSON><PERSON>"}, {"code": "KYD", "displayName": "Cayman Islands Dollar"}, {"code": "KZT", "displayName": "Tenge"}, {"code": "LAK", "displayName": "<PERSON><PERSON>"}, {"code": "LBP", "displayName": "Lebanese Pound"}, {"code": "LKR", "displayName": "Sri Lanka Rupee"}, {"code": "LRD", "displayName": "Liberian Dollar"}, {"code": "LSL", "displayName": "Loti"}, {"code": "LYD", "displayName": "Libyan Dinar"}, {"code": "MAD", "displayName": "Moroccan <PERSON><PERSON><PERSON>"}, {"code": "MDL", "displayName": "Moldovan Leu"}, {"code": "MGA", "displayName": "Malagasy Ariary"}, {"code": "MKD", "displayName": "<PERSON><PERSON>"}, {"code": "MMK", "displayName": "Kyat"}, {"code": "MNT", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "MOP", "displayName": "Pataca"}, {"code": "MRO", "displayName": "Ouguiya"}, {"code": "MRU", "displayName": "Ouguiya"}, {"code": "MUR", "displayName": "Mauritius Rupee"}, {"code": "MVR", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "MWK", "displayName": "Malawi Kwacha"}, {"code": "MXN", "displayName": "Mexican Peso"}, {"code": "MXV", "displayName": "Mexican Unidad de Inversion (UDI)"}, {"code": "MYR", "displayName": "Malaysian Ringgit"}, {"code": "MZN", "displayName": "Mozambique Metical"}, {"code": "NAD", "displayName": "Namibia Dollar"}, {"code": "NGN", "displayName": "<PERSON><PERSON>"}, {"code": "NIO", "displayName": "Cordoba Oro"}, {"code": "NOK", "displayName": "Norwegian Krone"}, {"code": "NPR", "displayName": "Nepalese Rupee"}, {"code": "NZD", "displayName": "New Zealand Dollar"}, {"code": "OMR", "displayName": "<PERSON><PERSON>"}, {"code": "PAB", "displayName": "Balboa"}, {"code": "PEN", "displayName": "Sol"}, {"code": "PGK", "displayName": "<PERSON><PERSON>"}, {"code": "PHP", "displayName": "Philippine Peso"}, {"code": "PKR", "displayName": "Pakistan Rupee"}, {"code": "PLN", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "PYG", "displayName": "Guarani"}, {"code": "QAR", "displayName": "<PERSON><PERSON> R<PERSON>"}, {"code": "RON", "displayName": "Romanian Leu"}, {"code": "RSD", "displayName": "Serbian Dinar"}, {"code": "RUB", "displayName": "Russian Ruble"}, {"code": "RWF", "displayName": "Rwanda Franc"}, {"code": "SAR", "displayName": "Saudi Riyal"}, {"code": "SBD", "displayName": "Solomon Islands Dollar"}, {"code": "SCR", "displayName": "Seychelles Rupee"}, {"code": "SDG", "displayName": "Sudanese Pound"}, {"code": "SEK", "displayName": "Swedish Krona"}, {"code": "SGD", "displayName": "Singapore Dollar"}, {"code": "SHP", "displayName": "<PERSON>"}, {"code": "SLE", "displayName": "Leone"}, {"code": "SLL", "displayName": "Leone"}, {"code": "SOS", "displayName": "Somali Shilling"}, {"code": "SRD", "displayName": "Surinam Dollar"}, {"code": "SSP", "displayName": "South Sudanese Pound"}, {"code": "STD", "displayName": "Dobra"}, {"code": "STN", "displayName": "Dobra"}, {"code": "SVC", "displayName": "El Salvador Colon"}, {"code": "SYP", "displayName": "Syrian Pound"}, {"code": "SZL", "displayName": "<PERSON><PERSON><PERSON>"}, {"code": "THB", "displayName": "Baht"}, {"code": "TJS", "displayName": "So<PERSON><PERSON>"}, {"code": "TMT", "displayName": "Turkmenistan New Manat"}, {"code": "TND", "displayName": "Tunisian Dinar"}, {"code": "TOP", "displayName": "Pa’anga"}, {"code": "TRY", "displayName": "Turkish Lira"}, {"code": "TTD", "displayName": "Trinidad and Tobago Dollar"}, {"code": "TWD", "displayName": "New Taiwan Dollar"}, {"code": "TZS", "displayName": "Tanzanian <PERSON>"}, {"code": "UAH", "displayName": "Hryvnia"}, {"code": "UGX", "displayName": "Uganda Shilling"}, {"code": "USD", "displayName": "US Dollar"}, {"code": "USN", "displayName": "US Dollar (Next day)"}, {"code": "UYI", "displayName": "Uruguay Peso en Unidades Indexadas (URUIURUI)"}, {"code": "UYU", "displayName": "Peso Uruguayo"}, {"code": "UYW", "displayName": "Unidad Previsional"}, {"code": "UZS", "displayName": "Uzbekistan Sum"}, {"code": "VEF", "displayName": "Bolívar"}, {"code": "VES", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "VND", "displayName": "<PERSON>"}, {"code": "VNS", "displayName": "<PERSON>"}, {"code": "VDO", "displayName": "<PERSON>"}, {"code": "VUV", "displayName": "Vatu"}, {"code": "WST", "displayName": "<PERSON><PERSON>"}, {"code": "XAF", "displayName": "CFA Franc BEAC"}, {"code": "XAU", "displayName": "Gold"}, {"code": "XPD", "displayName": "Palladium"}, {"code": "XPT", "displayName": "Platinum"}, {"code": "XAG", "displayName": "Silver"}, {"code": "XBA", "displayName": "Bond Markets Unit European Composite Unit (EURCO)"}, {"code": "XBB", "displayName": "Bond Markets Unit European Monetary Unit (E.M.U.-6)"}, {"code": "XBC", "displayName": "Bond Markets Unit European Unit of Account 9 (E.U.A.-9)"}, {"code": "XBD", "displayName": "Bond Markets Unit European Unit of Account 17 (E.U.A.-17)"}, {"code": "XCD", "displayName": "East Caribbean Dollar"}, {"code": "XDR", "displayName": "SDR (Special Drawing Right)"}, {"code": "XOF", "displayName": "CFA Franc BCEAO"}, {"code": "XPF", "displayName": "CFP Franc"}, {"code": "XSU", "displayName": "<PERSON><PERSON>"}, {"code": "XTS", "displayName": "Codes specifically reserved for testing purposes"}, {"code": "XUA", "displayName": "ADB Unit of Account"}, {"code": "XXX", "displayName": "The codes assigned for transactions where no currency is involved"}, {"code": "FUN", "displayName": "Currency for FUN mode"}, {"code": "YER", "displayName": "Yemeni R<PERSON>"}, {"code": "ZAR", "displayName": "Rand"}, {"code": "ZMW", "displayName": "Zambian <PERSON>"}, {"code": "ZWL", "displayName": "Zimbabwe Dollar"}, {"code": "BNS", "displayName": "Bonus Coin"}, {"code": "WSC", "displayName": "Virtual currency for Social games in Falcon side."}, {"code": "MXX", "displayName": "Artificial currency for Casino770 - Mexican Peso"}, {"code": "USX", "displayName": "Artificial currency for Casino770 - US Dollar"}, {"code": "CDX", "displayName": "Artificial currency for Casino770 - Canadian Dollar"}, {"code": "GBX", "displayName": "Artificial currency for Casino770 - Pound Sterling"}, {"code": "EUX", "displayName": "Artificial currency for Casino770 - Euro"}, {"code": "MEH", "displayName": "Milli Ethereum"}, {"code": "MLC", "displayName": "<PERSON><PERSON>"}, {"code": "MCH", "displayName": "Milli Bitcoin Cash"}, {"code": "DOG", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "label": "DOGE"}, {"code": "TET", "displayName": "<PERSON><PERSON>", "label": "USDT"}, {"code": "BTC", "displayName": "Bitcoin"}, {"code": "ETH", "displayName": "Ethereum"}, {"code": "LTC", "displayName": "Litecoin"}]