[{"code": "sw_mrmnky", "title": "Mr <PERSON>", "type": "slot", "defaultInfo": {"name": "Mr <PERSON>", "description": "Slots"}, "info": {}, "labels": [{"name": "branded", "group": "feature"}, {"name": "roulette", "group": "category"}, {"name": "slot", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_mrmnky@0.1.18", "settings": {}}, {"code": "sw_sf", "title": "San Fu", "type": "slot", "defaultInfo": {"name": "San Fu", "description": "Slots"}, "info": {}, "labels": [{"name": "branded", "group": "feature"}, {"name": "jackpot", "group": "feature"}, {"name": "progressive", "group": "feature"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_sf@0.1.5", "settings": {}}, {"code": "sw_omqjp", "title": "Old master Q", "type": "slot", "defaultInfo": {"name": "Old Master <PERSON>", "description": "Sky Wind"}, "info": {}, "labels": [{"name": "branded", "group": "feature"}, {"name": "html5", "group": "type"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_omqjp@0.1.5", "settings": {}}, {"code": "sw_al", "title": "Amazon Lady", "type": "slot", "defaultInfo": {"name": "Amazon Lady Slot", "description": "Sky Wind"}, "info": {}, "labels": [{"name": "branded", "group": "feature"}, {"name": "jackpot", "group": "feature"}, {"name": "html5", "group": "type"}, {"name": "slot", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_al@0.1.13", "settings": {}}, {"code": "sw_fufish", "title": "sw_fufish", "type": "action", "defaultInfo": {"name": "Fu FISH", "description": "Fu FISH description"}, "info": {}, "labels": [{"name": "progressive", "group": "feature"}, {"name": "roulette", "group": "category"}, {"name": "table game", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw-fufish@stable", "settings": {}}, {"code": "sw_mer", "title": "Mermaids Beauty", "type": "slot", "defaultInfo": {"name": "Mermaids Beauty", "description": "Slots"}, "info": {}, "labels": [{"name": "branded", "group": "feature"}, {"name": "jackpot", "group": "feature"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_mer@0.1.4", "settings": {}}, {"code": "sw_csy", "title": null, "type": "slot", "defaultInfo": {"name": "<PERSON><PERSON>", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_csy@0.1.3", "settings": {}}, {"code": "sw_gol", "title": "God of Lightning", "type": "slot", "defaultInfo": {"name": "God of Lightning Slot", "description": "Sky Wind"}, "info": {}, "labels": [{"name": "jackpot", "group": "feature"}, {"name": "html5", "group": "type"}, {"name": "slot", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_gol@0.2.6", "settings": {}}, {"code": "sw_tc", "title": "<PERSON>", "type": "slot", "defaultInfo": {"name": "<PERSON>", "description": "Slots"}, "info": {}, "labels": [{"name": "progressive", "group": "feature"}, {"name": "flash", "group": "type"}, {"name": "slot", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_tc@0.1.1", "settings": {}}, {"code": "sw_sq", "title": "Snowfall Queen", "type": "slot", "defaultInfo": {"name": "Snowfall Queen", "description": "Slots"}, "info": {}, "labels": [{"name": "progressive", "group": "feature"}, {"name": "downloadable", "group": "type"}, {"name": "roulette", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_sq@0.1.3", "settings": {}}, {"code": "sw_pc", "title": "Panda Chef", "type": "slot", "defaultInfo": {"name": "Panda Chef Slot", "description": "Sky Wind"}, "info": {}, "labels": [{"name": "branded", "group": "feature"}, {"name": "progressive", "group": "feature"}, {"name": "downloadable", "group": "type"}, {"name": "roulette", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_pc@0.1.7", "settings": {}}, {"code": "sw_rm", "title": null, "type": "slot", "defaultInfo": {"name": "<PERSON><PERSON><PERSON>", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_rm@0.1.4", "settings": {}}, {"code": "sw_888t", "title": null, "type": "slot", "defaultInfo": {"name": "888 Turtles", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_888t@0.2.41", "settings": {}}, {"code": "sw_ycs", "title": null, "type": "slot", "defaultInfo": {"name": "<PERSON>", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_ycs@0.1.5", "settings": {}}, {"code": "sw_fbb", "title": null, "type": "slot", "defaultInfo": {"name": "Fu Bao Bao", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_fbb@0.1.6", "settings": {}}, {"code": "sw_slbs", "title": null, "type": "slot", "defaultInfo": {"name": "<PERSON>", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_slbs@0.0.10", "settings": {}}, {"code": "sw_rf", "title": null, "type": "slot", "defaultInfo": {"name": "<PERSON><PERSON>", "description": "Slots"}, "info": {}, "labels": [], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_rf@0.1.5", "settings": {}}, {"code": "sw_mf", "title": "Maneku <PERSON>s", "type": "slot", "defaultInfo": {"name": "<PERSON><PERSON><PERSON>s", "description": "Sky Wind"}, "info": {}, "labels": [{"name": "jackpot", "group": "feature"}, {"name": "flash", "group": "type"}, {"name": "html5", "group": "type"}, {"name": "table game", "group": "category"}, {"name": "slot", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_mf@0.1.7", "settings": {}}, {"code": "sw_sod", "title": "Symphony Of Diamonds", "type": "slot", "defaultInfo": {"name": "Symphony Of Diamonds", "description": "Slots"}, "info": {}, "labels": [{"name": "flash", "group": "type"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_sod@0.1.14", "settings": {}}, {"code": "sw_dd", "title": "Dolphin Delight", "type": "slot", "defaultInfo": {"name": "Dolphin Delight", "description": "Slots"}, "info": {}, "labels": [{"name": "slot", "group": "category"}], "comment": null, "providerCode": "skywind", "providerTitle": null, "status": "normal", "module": "@skywind-group/sw_dd@0.1.6", "settings": {}}]