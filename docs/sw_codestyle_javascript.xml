<code_scheme name="SW Backoffice codestyle">
  <JSCodeStyleSettings>
    <option name="REFORMAT_C_STYLE_COMMENTS" value="false" />
    <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
    <option name="SPACES_WITHIN_IMPORTS" value="true" />
  </JSCodeStyleSettings>
  <TypeScriptCodeStyleSettings>
    <option name="REFORMAT_C_STYLE_COMMENTS" value="false" />
    <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
    <option name="SPACES_WITHIN_IMPORTS" value="true" />
  </TypeScriptCodeStyleSettings>
  <codeStyleSettings language="TypeScript">
    <option name="LINE_COMMENT_ADD_SPACE" value="false" />
    <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
    <option name="SPACE_WITHIN_METHOD_PARENTHESES" value="true" />
    <option name="METHOD_PARAMETERS_RPAREN_ON_NEXT_LINE" value="true" />
  </codeStyleSettings>
</code_scheme>
