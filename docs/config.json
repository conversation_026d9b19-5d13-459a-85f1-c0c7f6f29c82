{"baseUrl": "/sw-backoffice/", "locals": {"url": "http://localhost:8080", "name": "sw-backoffice blog", "owner": "SW", "description": ""}, "plugins": ["wintersmith-sassy", "./plugins/paginator.coffee"], "sass": {"debug": "undefined"}, "require": {"moment": "moment", "_": "underscore", "typogr": "typogr"}, "jade": {"pretty": true}, "markdown": {"smartLists": true, "smartypants": true}, "paginator": {"perPage": 3, "groupSort": {"Quick Start": 1000, "Customization": 900}}}