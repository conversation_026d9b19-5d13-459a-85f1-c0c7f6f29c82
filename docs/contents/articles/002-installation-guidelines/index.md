---
title: Installation Guidelines
author: vl
sort: 500
group: Quick Start
template: article.jade
---

## Prerequisites

The following instructions allow you to run a local copy on your machine.

## Install tools

If you don't have any of these tools installed already, you will need to:
* Download and install [git](https://git-scm.com/)
* Download and install nodejs [https://nodejs.org](https://nodejs.org)

**Note**: Make sure you have Node version >= 5.5 and NPM >= 3

Once you have those, you should install these globals with `npm install --global`:

* 
[Install graphicsMagick and ImageMagick for node](https://www.npmjs.com/package/gm)
```bash
sudo apt-get install graphicsmagick -y

or

brew install imagemagick --with-webp
brew install graphicsmagick

```
```
npm install gm
```


* webpack-dev-server
```bash
npm install --global webpack-dev-server
```

* typescript
```bash
npm install --global typescript@2.1.4
```

* Zopfly
```bash
npm install --global node-zopfli
```

## Clone repository and install dependencies

You will need to clone the source code:

```bash
git clone https://bitbucket.skywindgroup.com/scm/byswbo/sw-backoffice.git
```
After the repository is cloned, go inside of the repository directory and install dependencies:

```bash
cd sw-backoffice

git checkout develop

npm install
npm dedup

```
This will setup a working copy of ng2-admin on your local machine.

## Running local copy

To run a local copy in development mode, execute:

```bash
# 1.
npm start
```
In the separate console:

```bash
# 2.
npm run api
```




Go to http://0.0.0.0:3001 or http://localhost:3001 in your browser.


To run the local copy in production mode and build the sources, execute:

```bash
npm run test 
npm run build:prod
npm run server:prod
```

This will clear up your dist folder (where release files are located), generate a release build and start the 
built-in server.
Now you can copy the sources from the `dist` folder and use it with any backend framework or 
simply put it under a web server.

