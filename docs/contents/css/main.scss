@mixin header {
  margin: 10px 0;
  font-family: inherit;
  font-weight: bold;
  color: inherit;
  text-rendering: optimizelegibility;
}

html {
  font-family: 'Source Sans Pro', sans-serif;
  color: #484848;
  line-height: 1.28
}

body {
  position: relative;
  min-width: 1060px;
}
p {
  margin: 0 0 10px
}

em {
  font-style: italic
}

h1 {
  @include header;

  line-height: 40px;
  font-size: 39px
}

h2 {
  @include header;

  line-height: 40px;
  margin-top: 30px;
  font-size: 31px;
}

h3 {
  @include header;

  line-height: 40px;
  font-size: 23px;
}

h4 {
  @include header;

  line-height: 20px;
  font-size: 16px;
}

h5 {
  @include header;

  line-height: 40px;
  text-transform: uppercase;
  font-size: 14px;
}

h6 {
  @include header;

  line-height: 20px;
  font-size: 11px;
}


h1 small {
  font-size: 24px;
}

h2 small {
  font-size: 18px;
}

h3 small {
  font-size: 16px;
}

h4 small {
  font-size: 14px;
}

ul, ol {
  margin: 0 0 10px 25px;
  padding: 0
}

ul ul, ul ol, ol ol, ol ul {
  margin-bottom: 0
}

li {
  line-height: 20px
}

a {
  color: #285eb8;
  text-decoration: none
}

a:hover, a:focus {
  color: #234fb8;
  text-decoration: underline
}

a:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px
}

.center {
  text-align: center
}

body, pre {
  border: none;
  margin: 0;
  padding: 0
}

html {
  background: #f9f9f9
}

.browser-mockup {
  border-top: 2em solid #F3F3F3;
  position: relative;
  border-radius: 3px 3px 0 0
}

.browser-mockup:before {
  display: block;
  position: absolute;
  content: '';
  top: -1.25em;
  left: 1em;
  width: 0.5em;
  height: 0.5em;
  border-radius: 50%;
  background-color: #f44;
  box-shadow: 0 0 0 2px #f44, 1.5em 0 0 2px #9b3, 3em 0 0 2px #fb5;
}

.browser-mockup a {
  display: block;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #ddd;
  width: 100%;
}

.left {
  float: left
}

.right {
  float: right
}

.container {
  padding-top: 50px;
  min-width: 1060px
}

.wrap {
  width: 1060px;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}

.skinnyWrap {
  width: 690px;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}

hr {
  height: 0;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #eee
}

ul, li {
  margin-left: 20px
}

li + li {
  margin-top: 10px
}

h1 .anchor, h2 .anchor, h3 .anchor, h4 .anchor, h5 .anchor, h6 .anchor {
  margin-top: -50px;
  position: absolute
}

h1:hover .hash-link, h2:hover .hash-link, h3:hover .hash-link, h4:hover .hash-link, h5:hover .hash-link, h6:hover .hash-link {
  display: inline
}

.hash-link {
  color: #aaa;
  display: none
}

.nav-main {
  background: #222;
  color: #fafafa;
  position: fixed;
  top: 0;
  height: 50px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  width: 100%;
  z-index: 100
}

.nav-main:after {
  content: "";
  display: table;
  clear: both
}

.nav-main a {
  color: #e9e9e9;
  text-decoration: none
}

.nav-main .nav-site-internal {
  margin: 0 0 0 20px
}

.nav-main .nav-site-external {
  float: right;
  margin: 0
}

.nav-main .nav-site li {
  margin: 0
}

.nav-main .nav-site li > a {
  box-sizing: content-box;
  padding: 0 10px;
  line-height: 50px;
  display: inline-block;
  height: 50px;
  color: #ddd
}

.nav-main .nav-site li > a:hover {
  color: #fff
}

.nav-main .nav-site li > a.active {
  color: #fafafa;
  border-bottom: 3px solid #00abff;
  background: #333
}

.nav-main .nav-home {
  color: #ffffff;
  font-size: 24px;
  line-height: 50px;
  height: 50px;
  display: inline-block
}

.nav-main .nav-home .blur-label {
  color: #00abff;
}

.nav-main .nav-logo {
  vertical-align: middle;
  display: inline-block;
  margin-bottom: 9px;
}

.nav-main ul {
  display: inline-block;
  vertical-align: top
}

.nav-main li {
  display: inline
}

.hero {
  padding-bottom: 75px;
}

.hero .hero-content {
  color: #e9e9e9;
  font-weight: 300;
  background: #313131;
  padding-top: 50px;
}

.hero .text {
  font-size: 64px;
  text-align: center
}

.hero .minitext {
  font-size: 16px;
  text-align: center;
  text-transform: uppercase
}

.hero strong {
  color: #00abff;
  font-weight: 400
}

.white-text {
  color: rgb(249, 249, 249);
}

.hero .admin-screenshots {
  margin-top: 40px;
  display: flex;
  flex-direction: row;
}

.hero .admin-screenshot {
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 15px;
  text-align: center;
}

.admin-screenshot img {
  width: 100%;
}

.demo-link {
  display: block;
  position: relative;
  line-height: 0;
}

.demo-link:before {
  content: '';
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  height: 52px;
  background-image: linear-gradient(to bottom, transparent, rgb(249, 259, 249));
}

.demo-link .demo-link-label {
  display: flex;
  align-items: center;
  justify-content: center;
  content: 'Demo';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: black;
  background: rgba(0, 0, 0, 0.5);
  color: rgb(249, 249, 249);
  font-size: 32px;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.demo-link:hover .demo-link-label {
  opacity: 1;
}

.buttons-unit {
  margin-top: 60px;
  text-align: center
}

.buttons-unit a {
  color: #61dafb
}

.buttons-unit .button {
  font-size: 24px;
  background: #00abff;
  color: #fafafa;
}

.buttons-unit .button:active, .buttons-unit .button:focus {
  background: #00abff;
  text-decoration: none;
}

.index-block {
  padding: 40px 0;

  &:nth-child(even) {
    background: #f2f2f2;
  }
}

.centered {
  text-align: center;
}

.why-items {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin-top: 15px;
  padding-top: 15px;
}

.why-item {
  flex: 33%;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 15px;
}

.why-item img {
  width: 110px;
}

.nav-docs {
  color: #2d2d2d;
  font-size: 14px;
  float: left;
  width: 210px
}

.nav-docs ul {
  list-style: none;
  margin: 0
}

.nav-docs ul ul {
  margin: 6px 0 0 20px
}

.nav-docs li {
  line-height: 16px;
  margin: 0 0 6px
}

.nav-docs a {
  color: #666;
  display: block
}

.nav-docs a:hover {
  text-decoration: none;
  color: #285eb8
}

.nav-docs a.active {
  color: #285eb8
}

.nav-docs a.external:after {
  content: "";
  display: inline-block;
  width: 10px;
  height: 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding-left: 5px;
  background: url("../img/external.png") 100% 0 no-repeat;
  font-size: 10px;
  line-height: 1em;
  opacity: 0.5
}

@media only screen and (-webkit-min-device-pixel-ratio: 1.3), only screen and (min--moz-device-pixel-ratio: 1.3), only screen and (-o-min-device-pixel-ratio: 1.3 / 1), only screen and (min-resolution: 125dpi), only screen and (min-resolution: 1.3dppx) {
  .nav-docs a.external:after {
    background-image: url("../img/external_2x.png");
    background-size: 10px 10px
  }
}

.nav-docs .nav-docs-section {
  border-bottom: 1px solid #ccc;
  border-top: 1px solid #eee;
  padding: 12px 0
}

.nav-docs .nav-docs-section:first-child {
  padding-top: 0;
  border-top: 0
}

.nav-docs .nav-docs-section:last-child {
  padding-bottom: 0;
  border-bottom: 0
}

.nav-blog li {
  margin-bottom: 5px
}

.nav-docs-right {
  display: block;
  float: right;
  line-height: 50px;
}

.nav-docs-right a {
  color: #00abff;
  text-decoration: none;
}

.nav-docs-right a:hover {
  text-decoration: underline;
}

.home-section {
  margin: 50px 0
}

.home-divider {
  border-top-color: #bbb;
  margin: 0 auto;
  width: 400px
}

.skinny-row:after {
  content: "";
  display: table;
  clear: both
}

.skinny-col {
  float: left;
  margin-left: 40px;
  width: 305px
}

.skinny-col:first-child {
  margin-left: 0
}

.marketing-row {
  margin: 50px 0
}

.marketing-row:after {
  content: "";
  display: table;
  clear: both
}

.marketing-col {
  float: left;
  margin-left: 40px;
  width: 280px;
}

.marketing-col p {
  font-size: 16px
}

.marketing-col:first-child {
  margin-left: 0
}

.home-bottom-section {
  margin-bottom: 100px
}

.docs-nextprev:after {
  content: "";
  display: table;
  clear: both
}

.jsxCompiler {
  margin: 0 auto;
  padding-top: 20px;
  width: 1220px
}

.jsxCompiler .compiler-option {
  display: block;
  margin-top: 5px
}

.jsxCompiler .playgroundPreview {
  padding: 0;
  width: 600px;
  word-wrap: break-word
}

.jsxCompiler .playgroundPreview pre {
  font-family: 'source-code-pro', Menlo, Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5
}

.jsxCompiler .playgroundError {
  padding: 15px 20px
}

.docs-prev {
  float: left
}

.docs-next {
  float: right
}

footer {
  font-size: 13px;
  font-weight: 600;
  margin-top: 66px;
  margin-bottom: 18px;
  overflow: auto
}

.blogContent {
  padding-top: 20px
}

.blogContent:after {
  content: "";
  display: table;
  clear: both
}

.blogContent blockquote {
  padding: 5px 15px;
  margin: 20px 0;
  background-color: #f8f5ec;
  border-left: 5px solid #f7ebc6
}

.blogContent code {
  font-size: inherit;
  line-height: inherit;
  color: #555;
  background-color: black;
  background-color: rgba(0, 0, 0, 0.04)
}

.documentationContent {
  padding-top: 20px
}

.documentationContent:after {
  content: "";
  display: table;
  clear: both
}

.documentationContent blockquote {
  padding: 15px 30px 15px 15px;
  margin: 20px 0;
  background-color: black;
  background-color: rgba(204, 122, 111, 0.1);
  border-left: 5px solid black;
  border-left: 5px solid rgba(191, 87, 73, 0.2);
}

.documentationContent blockquote p {
  margin-bottom: 0
}

.documentationContent blockquote p:first-child {
  font-weight: bold;
  font-size: 17.5px;
  line-height: 20px;
  margin-top: 0;
  text-rendering: optimizelegibility
}

.docs-prevnext {
  padding-top: 40px;
  padding-bottom: 40px
}

.button {
  background: -webkit-linear-gradient(#9a9a9a, #646464);
  background:    -moz-linear-gradient(#9a9a9a, #646464);
  background:     -ms-linear-gradient(#9a9a9a, #646464);
  background:      -o-linear-gradient(#9a9a9a, #646464);
  background:        -webkit-gradient(#9a9a9a, #646464);
  background:         linear-gradient(#9a9a9a, #646464);
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 18px;
  font-weight: 400;
  margin: 0 12px;
  display: inline-block;
  color: #fafafa;
  text-decoration: none;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.button:hover {
  text-decoration: none
}

.button:active {
  box-shadow: none
}

.hero .button {
  box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.3)
}

.row {
  padding-bottom: 4px
}

.row .span4 {
  width: 33.33%;
  display: table-cell
}

.row .span8 {
  width: 66.66%;
  display: table-cell
}

.row .span6 {
  width: 50%;
  display: table-cell
}

p {
  margin: 10px 0
}

.highlight {
  padding: 10px;
  margin-bottom: 20px
}

figure {
  text-align: center
}

.inner-content {
  float: right;
  width: 650px
}

.nosidebar .inner-content {
  float: none;
  margin: 0 auto
}

.inner-content img {
  max-width: 100%;
}

.inner-content table {
  border-collapse: collapse;
  width: 100%;
}

.inner-content th, .inner-content td {
  padding: 0.25rem;
  text-align: left;
  border: 1px solid #ccc;
}

.inner-content tbody tr:nth-child(odd) {
  background: #eee;
}

h1:after {
  content: "";
  display: table;
  clear: both
}

.edit-page-link {
  float: right;
  font-size: 16px;
  font-weight: normal;
  line-height: 20px;
  margin-top: 17px
}

.post-list-item + .post-list-item {
  margin-top: 60px
}

/* code styling */

code {
  font-family: 'Anonymous Pro', sans-serif;
  font-size: 0.85em;
  color: #000;
}

pre code {
  display: block;
  line-height: 1.1;
  color: #333333;
  background: #f8f5ec;
  padding: 30px 14px 14px;
  position: relative;
  overflow-x: auto;
}

pre code:before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #c2c0bc;
  background-color: #f1ede4;
  content: "Code";
}

p code {
  padding: 0.1em 0.3em 0.2em;
  border-radius: 0.3em;
  position: relative;
  background: #fffff3;

  white-space: nowrap;
}

/* syntax hl stuff */

code.lang-markdown {
  color: #424242;
}

code.lang-markdown .header,
code.lang-markdown .strong {
  font-weight: bold;
}

code.lang-markdown .emphasis {
  font-style: italic;
}

code.lang-markdown .horizontal_rule,
code.lang-markdown .link_label,
code.lang-markdown .code,
code.lang-markdown .header,
code.lang-markdown .link_url {
  color: #555;
}

code.lang-markdown .blockquote,
code.lang-markdown .bullet {
  color: #bbb;
}

/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
.tomorrow-comment, pre .comment, pre .title {
  color: #8e908c;
}

.tomorrow-red, pre .variable, pre .attribute, pre .tag, pre .regexp, pre .ruby .constant, pre .xml .tag .title, pre .xml .pi, pre .xml .doctype, pre .html .doctype, pre .css .id, pre .css .class, pre .css .pseudo {
  color: #c82829;
}

.tomorrow-orange, pre .number, pre .preprocessor, pre .built_in, pre .literal, pre .params, pre .constant {
  color: #f5871f;
}

.tomorrow-yellow, pre .class, pre .ruby .class .title, pre .css .rules .attribute {
  color: #eab700;
}

.tomorrow-green, pre .string, pre .value, pre .inheritance, pre .header, pre .ruby .symbol, pre .xml .cdata {
  color: #718c00;
}

.tomorrow-aqua, pre .css .hexcolor {
  color: #3e999f;
}

.tomorrow-blue, pre .function, pre .python .decorator, pre .python .title, pre .ruby .function .title, pre .ruby .title .keyword, pre .perl .sub, pre .javascript .title, pre .coffeescript .title {
  color: #4271ae;
}

.tomorrow-purple, pre .keyword, pre .javascript .function {
  color: #8959a8;
}

/* media queries */

@media screen and (max-width: 960px) {
  .nav-main {
    position: static
  }

  .container {
    padding-top: 0
  }
}
